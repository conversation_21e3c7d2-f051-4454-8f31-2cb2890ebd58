import pytest
from unittest.mock import AsyncMock, patch

# 这有点棘手。因为从根目录运行 pytest 时，`src` 目录不会自动添加到 python 路径中，
# 所以我们需要添加它。
# 一个更好的解决方案是使用 `pyproject.toml` 并以可编辑模式安装包。
# 目前，这个 sys.path 修改将在测试运行中起作用。
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from src.cpquery_scraper.app import App


def test_app_creation():
    """
    测试 App 是否可以无错误地实例化。
    """
    print("test_app_creation start")
    try:
        app = App()
        assert app is not None
    except Exception as e:
        pytest.fail(f"App instantiation failed with an exception: {e}")
    print("test_app_creation end")
