"""
数据源测试用例

测试各种数据源的功能和兼容性。

Author: wwind
Date: 2025.07.08
"""

import unittest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock

from ..models import DataSourceConfig, DataSourceType, TaskItem, TaskStatus
from ..data_sources import DataSourceFactory, MySQLDataSource, ExcelDataSource, RedisDataSource
from ..exceptions import DataSourceError, DatabaseConnectionError, FileOperationError


class TestDataSourceFactory(unittest.TestCase):
    """测试数据源工厂"""
    
    def test_get_supported_types(self):
        """测试获取支持的数据源类型"""
        supported_types = DataSourceFactory.get_supported_types()
        self.assertIn(DataSourceType.MYSQL, supported_types)
        self.assertIn(DataSourceType.EXCEL, supported_types)
        self.assertIn(DataSourceType.REDIS, supported_types)
    
    def test_create_mysql_data_source(self):
        """测试创建MySQL数据源"""
        config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params={
                'host': 'localhost',
                'user': 'test',
                'password': 'test',
                'database': 'test'
            }
        )
        
        data_source = DataSourceFactory.create(config)
        self.assertIsInstance(data_source, MySQLDataSource)
        self.assertEqual(data_source.source_type, DataSourceType.MYSQL)
    
    def test_create_excel_data_source(self):
        """测试创建Excel数据源"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = DataSourceConfig(
                source_type=DataSourceType.EXCEL,
                connection_params={'excel_dir': temp_dir}
            )
            
            data_source = DataSourceFactory.create(config)
            self.assertIsInstance(data_source, ExcelDataSource)
            self.assertEqual(data_source.source_type, DataSourceType.EXCEL)
    
    def test_create_redis_data_source(self):
        """测试创建Redis数据源"""
        config = DataSourceConfig(
            source_type=DataSourceType.REDIS,
            connection_params={
                'redis_params': {
                    'host': 'localhost',
                    'port': 6379
                }
            }
        )
        
        data_source = DataSourceFactory.create(config)
        self.assertIsInstance(data_source, RedisDataSource)
        self.assertEqual(data_source.source_type, DataSourceType.REDIS)
    
    def test_create_unsupported_data_source(self):
        """测试创建不支持的数据源类型"""
        # 创建一个不存在的数据源类型
        class UnsupportedType:
            value = "unsupported"
        
        config = DataSourceConfig(
            source_type=UnsupportedType(),
            connection_params={}
        )
        
        with self.assertRaises(DataSourceError):
            DataSourceFactory.create(config)


class TestMySQLDataSource(unittest.IsolatedAsyncioTestCase):
    """测试MySQL数据源"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params={
                'host': 'localhost',
                'user': 'test',
                'password': 'test',
                'database': 'test'
            }
        )
    
    def test_init_with_invalid_source_type(self):
        """测试使用错误的数据源类型初始化"""
        config = DataSourceConfig(
            source_type=DataSourceType.EXCEL,
            connection_params={}
        )
        
        with self.assertRaises(DataSourceError):
            MySQLDataSource(config)
    
    def test_init_with_missing_params(self):
        """测试缺少必需参数的初始化"""
        config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params={'host': 'localhost'}  # 缺少其他必需参数
        )
        
        with self.assertRaises(DatabaseConnectionError):
            MySQLDataSource(config)
    
    @patch('src.cpquery_scraper.utils.db.MysqlConnection')
    async def test_connect_success(self, mock_mysql_class):
        """测试成功连接"""
        mock_client = Mock()
        mock_mysql_class.return_value = mock_client
        
        data_source = MySQLDataSource(self.config)
        await data_source.connect()
        
        self.assertTrue(data_source.is_connected)
        mock_client.connect_mysql.assert_called_once()
    
    @patch('corutine_queue_scheduling.data_sources.mysql_source.MysqlConnection')
    async def test_connect_failure(self, mock_mysql_class):
        """测试连接失败"""
        mock_mysql_class.side_effect = Exception("Connection failed")
        
        data_source = MySQLDataSource(self.config)
        
        with self.assertRaises(DataSourceError):
            await data_source.connect()
        
        self.assertFalse(data_source.is_connected)
    
    @patch('src.cpquery_scraper.utils.db.MysqlConnection')
    async def test_fetch_tasks(self, mock_mysql_class):
        """测试获取任务"""
        mock_client = Mock()
        mock_client.query.return_value = [
            {'an': '2023123456789'},
            {'an': '2023987654321'}
        ]
        mock_mysql_class.return_value = mock_client
        
        data_source = MySQLDataSource(self.config)
        await data_source.connect()
        
        tasks = await data_source.fetch_tasks(limit=10)
        
        self.assertEqual(len(tasks), 2)
        self.assertIsInstance(tasks[0], TaskItem)
        self.assertEqual(tasks[0].task_id, '2023123456789')
        self.assertEqual(tasks[0].status, TaskStatus.PENDING)


class TestExcelDataSource(unittest.IsolatedAsyncioTestCase):
    """测试Excel数据源"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = DataSourceConfig(
            source_type=DataSourceType.EXCEL,
            connection_params={'excel_dir': self.temp_dir}
        )
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_init_with_invalid_source_type(self):
        """测试使用错误的数据源类型初始化"""
        config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params={}
        )
        
        with self.assertRaises(DataSourceError):
            ExcelDataSource(config)
    
    def test_init_with_nonexistent_directory(self):
        """测试使用不存在的目录初始化"""
        config = DataSourceConfig(
            source_type=DataSourceType.EXCEL,
            connection_params={'excel_dir': '/nonexistent/path'}
        )
        
        with self.assertRaises(FileOperationError):
            ExcelDataSource(config)
    
    async def test_connect_success(self):
        """测试成功连接"""
        data_source = ExcelDataSource(self.config)
        await data_source.connect()
        
        self.assertTrue(data_source.is_connected)
    
    async def test_fetch_tasks_no_files(self):
        """测试没有Excel文件时获取任务"""
        data_source = ExcelDataSource(self.config)
        await data_source.connect()
        
        tasks = await data_source.fetch_tasks()
        
        self.assertEqual(len(tasks), 0)
    
    async def test_health_check(self):
        """测试健康检查"""
        data_source = ExcelDataSource(self.config)
        
        # 目录存在时应该返回True
        result = await data_source.health_check()
        self.assertTrue(result)


class TestRedisDataSource(unittest.IsolatedAsyncioTestCase):
    """测试Redis数据源"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = DataSourceConfig(
            source_type=DataSourceType.REDIS,
            connection_params={
                'redis_params': {
                    'host': 'localhost',
                    'port': 6379
                }
            }
        )
    
    def test_init_with_invalid_source_type(self):
        """测试使用错误的数据源类型初始化"""
        config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params={}
        )
        
        with self.assertRaises(DataSourceError):
            RedisDataSource(config)
    
    def test_init_with_missing_params(self):
        """测试缺少必需参数的初始化"""
        config = DataSourceConfig(
            source_type=DataSourceType.REDIS,
            connection_params={
                'redis_params': {'host': 'localhost'}  # 缺少port
            }
        )
        
        with self.assertRaises(DataSourceError):
            RedisDataSource(config)
    
    @patch('src.cpquery_scraper.utils.db.RedisConnection')
    async def test_connect_success(self, mock_redis_class):
        """测试成功连接"""
        mock_redis_client = Mock()
        mock_redis_class.return_value = mock_redis_client
        
        data_source = RedisDataSource(self.config)
        await data_source.connect()
        
        self.assertTrue(data_source.is_connected)
        mock_redis_client.connect_redis.assert_called_once()


if __name__ == '__main__':
    unittest.main()
