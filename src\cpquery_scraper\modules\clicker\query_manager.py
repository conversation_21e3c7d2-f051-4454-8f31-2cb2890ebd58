"""
主页查询管理器
负责在cpquery主页执行查询操作和页面跳转
"""
import asyncio
# 移除未使用的Optional导入
from playwright.async_api import Page, expect

from .exceptions import QueryError, PageInitError, PageCrashedError
from src.cpquery_scraper.utils.helpers import check_page_exception_msg_is_crashed


class QueryManager:
    """主页查询管理器"""
    
    def __init__(self, config, logger):
        """
        初始化查询管理器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
    
    async def query_an(self, name: str, query_page: Page, an: str, retry_times: int = 0) -> dict:
        """
        在cpquery主页查询页面,执行查询an，以获取查询结果
        
        Args:
            name: 任务名称
            query_page: 查询页面对象
            an: 申请号
            retry_times: 重试次数
            
        Returns:
            dict: 查询结果
            
        Raises:
            QueryError: 查询失败时抛出
            PageCrashedError: 页面崩溃时抛出
        """
        if retry_times >= 5:
            error_msg = f"查询页查询 {an} 5次后失败，放弃查询"
            self.logger.error(f"--{name}: {error_msg}")
            raise QueryError(
                error_msg,
                error_code="QUERY_MAX_RETRIES",
                an=an,
                retry_count=retry_times
            )

        try:
            # 输入an，点击查询
            await query_page.get_by_placeholder("例如: 2010101995057").fill(an)
            async with query_page.expect_response(
                "**/api/search/undomestic/publicSearch?hHp4Kgam=**", timeout=30000
            ) as info:
                await query_page.get_by_role("button", name="查询", exact=True).click()
                await expect(query_page.get_by_text(an)).to_be_visible(timeout=30000)
            info = await info.value
            info_json = await info.json()
            
        except Exception as err:
            print(f"--{name}: 查询页查询{an}出错:{err}, 重试:")

            # 检查是否登录失效
            if "tysf.cponline.cnipa.gov.cn" in query_page.url:
                print("-！当前页面用户登录已失效，重新登录")
                # 与原模块保持一致，调用登录函数
                from auth import get_page_logined
                logged_page = await get_page_logined(name, query_page)
                if logged_page is None:
                    raise QueryError(
                        "重新登录失败，无法获取已登录页面",
                        error_code="LOGIN_FAILED",
                        an=an,
                        details={"retry_times": retry_times}
                    )
                query_page = logged_page
                retry_times += 1
                print(f"--main_page_query_an:重试第 {retry_times} 次")
                return await self.query_an(name, query_page, an, retry_times)

            # 检查页面是否崩溃
            if check_page_exception_msg_is_crashed(name, repr(err), 'query_an'):
                raise PageCrashedError(
                    "页面已崩溃",
                    error_code="PAGE_CRASHED",
                    page_url=query_page.url,
                    crash_reason=str(err)
                )

            # 其他错误，重试
            retry_times += 1
            print(f"--:查询页查询，重试第 {retry_times} 次")
            return await self.query_an(name, query_page, an, retry_times)
        
        # self.logger.debug(f"--{name}: {an}查询成功")
        # 与原模块保持一致，不输出成功日志
        return info_json

    async def click_an_result(self, name: str, query_page: Page, an: str, retry_times: int = 0) -> Page:
        """
        点击查询出的结果，弹出新的页面（数据详情页），获取该页面
        
        Args:
            name: 任务名称
            query_page: 查询页面对象
            an: 申请号
            retry_times: 重试次数
            
        Returns:
            Page: 数据详情页面对象
            
        Raises:
            PageInitError: 页面初始化失败时抛出
            PageCrashedError: 页面崩溃时抛出
        """
        # 重要：与原模块保持一致，每次调用都重置重试次数
        retry_times = 0

        if retry_times >= 5:
            error_msg = f"数据页面初始化失败，重试{retry_times}次后放弃"
            self.logger.error(f"--{name}: {error_msg}")
            raise PageInitError(
                error_msg,
                error_code="PAGE_INIT_MAX_RETRIES",
                details={"an": an, "retry_count": retry_times}
            )

        try:
            async with query_page.expect_popup(timeout=30000) as new_page_info:
                await query_page.get_by_text(an).click()
            detail_page = await new_page_info.value
            await detail_page.wait_for_url("**/detail/index?zhuanlisqh=**")
            
        except Exception as err:
            print(f"----{name}:数据页面初始化失败:{err}")

            # 检查页面是否崩溃
            if check_page_exception_msg_is_crashed(name, repr(err), 'click_an_result'):
                raise PageCrashedError(
                    "页面已崩溃",
                    error_code="PAGE_CRASHED",
                    crash_reason=str(err)
                )

            retry_times += 1
            print(f"----{name}:重试第 {retry_times} 次")
            return await self.click_an_result(name, query_page, an, retry_times)
        
        # 确保页面初始化成功
        print(f"--{name}:数据页面初始化成功,detail_page.url = {detail_page.url}")

        # 验证URL格式（与原模块保持一致，使用assert）
        assert "/detail/index?zhuanlisqh" in detail_page.url

        return detail_page

    async def get_page_with_retry(self, name: str, query_page: Page, an: str, max_retries: int = 3) -> Page:
        """
        获取数据页面，带重试机制
        
        Args:
            name: 任务名称
            query_page: 查询页面对象
            an: 申请号
            max_retries: 最大重试次数
            
        Returns:
            Page: 数据详情页面对象
            
        Raises:
            QueryError: 查询失败时抛出
            PageInitError: 页面初始化失败时抛出
        """
        for attempt in range(max_retries + 1):
            try:
                # 先查询
                query_result = await self.query_an(name, query_page, an)
                
                # 检查查询结果
                if query_result.get("code") != 200:
                    raise QueryError(
                        f"查询返回错误: {query_result.get('msg', '未知错误')}",
                        error_code=str(query_result.get("code", "UNKNOWN")),
                        an=an,
                        details={"query_result": query_result}
                    )
                
                # 点击结果获取详情页
                detail_page = await self.click_an_result(name, query_page, an)
                return detail_page
                
            except (QueryError, PageInitError) as e:
                if attempt < max_retries:
                    self.logger.warning(f"--{name}: 第{attempt + 1}次尝试失败: {e}, 等待后重试")
                    await asyncio.sleep(2)
                    continue
                else:
                    self.logger.error(f"--{name}: 所有尝试都失败，放弃获取页面")
                    raise
            except Exception as e:
                self.logger.error(f"--{name}: 获取页面时发生未预期错误: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(2)
                    continue
                else:
                    raise QueryError(
                        f"获取页面失败: {str(e)}",
                        error_code="UNEXPECTED_ERROR",
                        an=an,
                        details={"original_error": str(e)}
                    )

        # 这行代码理论上永远不会执行，但为了满足类型检查器要求
        raise QueryError(
            "获取页面失败：所有重试都已耗尽",
            error_code="MAX_RETRIES_EXCEEDED",
            an=an,
            details={"max_retries": max_retries}
        )
