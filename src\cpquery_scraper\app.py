import asyncio
import signal
import sys
import time
from playwright.async_api import async_playwright

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.core.browser_manager import BrowserManager
from src.cpquery_scraper.spiders.patent_spider import PatentSpider
from src.cpquery_scraper.pipelines.redis_pipeline import RedisPipeline
from src.cpquery_scraper.pipelines.mysql_pipeline import MySQLPipeline
# queuing 模块有一个工厂，我们用它。
from src.cpquery_scraper.queuing import TaskProcessorFactory

class App:
    """
    主应用程序类，用于协调爬虫。
    它初始化所有组件并管理主应用程序的生命周期。
    """
    def __init__(self):
        print("App.__init__ start")
        self.logger = get_logger("App")
        self.task_queue = asyncio.Queue(maxsize=config.TASK_QUEUE_MAX_SIZE)
        self.result_queue = asyncio.Queue(maxsize=5000)
        self.browser_manager = None
        self.pipelines = [RedisPipeline(), MySQLPipeline()]
        self.all_tasks: list[asyncio.Task] = []
        self.shutdown_event = asyncio.Event()
        self._signal_logged = False
        print("App.__init__ end")

    async def _start_background_tasks(self):
        """启动任务生产者和结果处理器（参照refactor_readme.md）。"""
        background_tasks: list[asyncio.Task] = []

        self.logger.info("开始启动后台任务...")

        # 1) 使用 queuing.TaskProcessorFactory 根据 TASK_SOURCE 创建任务处理器
        try:
            if config.TASK_SOURCE == 'redis':
                processor = TaskProcessorFactory.create_redis_processor(
                    redis_params=config.REDIS_PARAM,
                    task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
                    result_queue_max_size=5000,
                    monitor_interval=config.TASK_MONITOR_INTERVAL,
                    logger=self.logger,
                )
            elif config.TASK_SOURCE == 'mysql':
                processor = TaskProcessorFactory.create_mysql_processor(
                    mysql_params={**config.MYSQL_PARAM, 'task_table': 'epatent_0'},
                    task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
                    result_queue_max_size=5000,
                    monitor_interval=config.TASK_MONITOR_INTERVAL,
                    logger=self.logger,
                )
            else:  # excel
                processor = TaskProcessorFactory.create_excel_processor(
                    excel_dir=str(config.TASK_EXCEL_DIR),
                    task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
                    result_queue_max_size=5000,
                    logger=self.logger,
                )
        except Exception as e:
            self.logger.error(f"创建任务处理器失败: {e}")
            raise

        # Task queue management with FLAG_SPIDER_RUNNING gate and TASK_QUEUE_FROM_REDIS_SIZE threshold
        async def pump_tasks():
            last_monitor_time = time.time()

            while not self.shutdown_event.is_set() and config.FLAG_SPIDER_RUNNING:
                try:
                    # Monitor queue size at intervals as per original logic
                    current_time = time.time()
                    if current_time - last_monitor_time >= config.TASK_MONITOR_INTERVAL:
                        self.logger.info(f"Task queue size: {self.task_queue.qsize()}")
                        last_monitor_time = current_time

                    # Only supplement tasks if FLAG_SPIDER_RUNNING and queue below threshold
                    if (config.FLAG_SPIDER_RUNNING and
                        self.task_queue.qsize() < config.TASK_QUEUE_FROM_REDIS_SIZE):

                        async with processor:
                            fetch_limit = config.TASK_QUEUE_FROM_REDIS_SIZE - self.task_queue.qsize()
                            tasks_fetched = 0

                            for _ in range(fetch_limit):
                                try:
                                    task_item = await processor.get_task(timeout=1.0)
                                    if task_item is None:
                                        break
                                    await self.task_queue.put(task_item.task_id)
                                    tasks_fetched += 1
                                except asyncio.TimeoutError:
                                    break

                            if tasks_fetched > 0:
                                self.logger.info(f"Added {tasks_fetched} tasks to queue")

                    # Brief sleep to prevent tight loop
                    await asyncio.sleep(1)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"任务泵错误（将重试）: {e}")
                    await asyncio.sleep(5)

            self.logger.info("Task queue pump stopped")
        background_tasks.append(asyncio.create_task(pump_tasks()))

        # 2) Result queue processing with proper intervals and trans_format as per original logic
        redis_pipeline = self.pipelines[0]
        async def pump_results_batch():
            while not self.shutdown_event.is_set() and config.FLAG_SPIDER_RUNNING:
                try:
                    batch: list[dict] = []
                    # Collect batch over RESULT_TO_REDIS_INTERVAL period
                    end_time = time.time() + config.RESULT_TO_REDIS_INTERVAL

                    while time.time() < end_time and not self.shutdown_event.is_set() and config.FLAG_SPIDER_RUNNING:
                        try:
                            item = await asyncio.wait_for(self.result_queue.get(), timeout=1.0)
                            batch.append(item)
                            self.result_queue.task_done()
                        except asyncio.TimeoutError:
                            continue

                    # Process batch to Redis with trans_format conversion
                    if batch:
                        self.logger.info(f"从结果队列中获取 {len(batch)} 条数据，开始写入Redis")
                        success_count = await redis_pipeline.process_items_batch(batch)
                        self.logger.info(f"成功写入Redis {success_count} 条数据")
                    else:
                        self.logger.debug("队列中没有数据，跳过本次写入")

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"结果泵错误: {e}")
                    await asyncio.sleep(5)

            self.logger.info("Result queue pump stopped")
        background_tasks.append(asyncio.create_task(pump_results_batch()))

        # 3) Main node periodic Redis→MySQL sync as per original logic
        mysql_pipeline = self.pipelines[1]
        if isinstance(mysql_pipeline, MySQLPipeline) and mysql_pipeline.is_main_role:
            async def run_mysql_sync():
                self.logger.info("Starting Redis→MySQL sync task (main node)")

                while not self.shutdown_event.is_set() and config.FLAG_SPIDER_RUNNING:
                    try:
                        # Wait for sync interval
                        await asyncio.sleep(config.RESULT_TO_MYSQL_INTERVAL)

                        if not self.shutdown_event.is_set() and config.FLAG_SPIDER_RUNNING:
                            await mysql_pipeline.run_sync_task()

                    except asyncio.CancelledError:
                        break
                    except Exception as e:
                        self.logger.error(f"MySQL 同步任务失败: {e}", exc_info=True)
                        await asyncio.sleep(60)  # Wait longer on error

                self.logger.info("Redis→MySQL sync task stopped")
            background_tasks.append(asyncio.create_task(run_mysql_sync()))
        else:
            self.logger.info("Skipping MySQL sync (not main role or invalid pipeline)")

        return background_tasks

    async def _create_and_run_worker(self, name: str):
        """创建、登录并运行单个爬虫工作器。"""
        self.logger.info(f"初始化工作进程: {name}")
        try:
            device_info = config.IPAD_PRO_11
            context = await self.browser_manager.get_new_context(device_info, name)
            context = await self.browser_manager.login_context(context, name)

            spider = PatentSpider(name, context, self.task_queue, self.result_queue)
            # 将 shutdown_event 传给 spider，便于响应关停
            setattr(spider, 'shutdown_event', self.shutdown_event)
            await spider.start_scraping()
        except Exception as e:
            self.logger.error(f"工作进程 {name} 发生严重错误: {e}", exc_info=True)
        finally:
            self.logger.info(f"工作进程 {name} 正在关闭。")

    async def run(self):
        """主执行方法。"""
        self.logger.info(f"--- 爬虫应用启动中 (版本: {config.VERSION}, 角色: {config.ROLE.name}) ---")
        config.FLAG_SPIDER_RUNNING = True

        # 注册信号处理程序以实现优雅关闭（Windows 兼容）
        def signal_handler(signum, frame):  # noqa: ARG001
            if not self._signal_logged:
                self.logger.info(f"收到系统信号 {signum}，正在执行优雅关闭...")
                self._signal_logged = True
            self.shutdown_event.set()

        if sys.platform != 'win32':
            # Unix/Linux: 使用 asyncio 信号处理
            loop = asyncio.get_running_loop()
            for sig in (signal.SIGINT, signal.SIGTERM):
                loop.add_signal_handler(sig, lambda: signal_handler(sig, None))
        else:
            # Windows: 使用传统信号处理
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

        try:
            async with async_playwright() as playwright:
                self.browser_manager = BrowserManager(playwright)
                await self.browser_manager.launch_browser()

                for p in self.pipelines:
                    await p.open_spider(spider=None)

                self.all_tasks = await self._start_background_tasks()

                # Create workers with staggered startup as per original logic
                worker_tasks = []
                for i in range(config.MAX_TASK_NUMBER):
                    worker_coro = self._create_and_run_worker(f"worker-{i}")
                    worker_task = asyncio.create_task(worker_coro)
                    worker_tasks.append(worker_task)

                    # Add WORKER_START_DELAY between workers (except last one)
                    if i < config.MAX_TASK_NUMBER - 1:
                        await asyncio.sleep(getattr(config, 'WORKER_START_DELAY', 2))

                self.all_tasks.extend(worker_tasks)

                await asyncio.gather(*self.all_tasks)

        except Exception as e:
            self.logger.error(f"主程序运行出现严重错误: {e}", exc_info=True)
        finally:
            self.logger.info("--- 爬虫应用正在关闭 ---")
            config.FLAG_SPIDER_RUNNING = False
            self.shutdown_event.set()

            # Wait for queues to be processed as per original logic
            self.logger.info("等待任务队列完成...")
            await self.task_queue.join()

            self.logger.info("等待结果队列完成...")
            await self.result_queue.join()

            # Additional wait time as per original FINAL_WAIT_TIME
            final_wait_time = getattr(config, 'FINAL_WAIT_TIME', 30)
            self.logger.info(f"额外等待 {final_wait_time} 秒...")
            await asyncio.sleep(final_wait_time)

            # Cancel background tasks
            self.logger.info("取消后台任务...")
            for task in list(self.all_tasks):
                if not task.done():
                    task.cancel()
            await asyncio.gather(*self.all_tasks, return_exceptions=True)

            # Close browser and pipelines
            if self.browser_manager:
                await self.browser_manager.close_browser()

            for p in self.pipelines:
                await p.close_spider(spider=None)

            self.logger.info("关闭完成。")
