"""
切换验证脚本：验证新模块可以完全替代原模块
"""
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_direct_import_replacement():
    """测试直接导入替换"""
    print("=== 测试方案1：直接导入替换 ===")
    
    try:
        # 原有方式
        # from corutine_user_login import user_login, get_page_logined, get_user
        
        # 新方式：只需修改导入语句
        from src.cpquery_scraper.modules.auth import user_login, get_page_logined, get_user
        
        # 验证函数存在且可调用
        assert callable(user_login), "user_login函数不可调用"
        assert callable(get_page_logined), "get_page_logined函数不可调用"
        assert callable(get_user), "get_user函数不可调用"
        
        # 测试基本调用
        user_info = get_user()
        assert isinstance(user_info, dict), "get_user返回类型错误"
        assert 'id' in user_info, "用户信息缺少id字段"
        assert 'type' in user_info, "用户信息缺少type字段"
        
        print("[PASS] 直接导入替换测试通过")
        return True

    except Exception as e:
        print(f"[FAIL] 直接导入替换测试失败: {e}")
        return False


def test_module_import_replacement():
    """测试模块级导入替换"""
    print("\n=== 测试方案2：模块级导入替换 ===")
    
    try:
        # 原有方式
        # import corutine_user_login
        
        # 新方式
        import src.cpquery_scraper.modules.auth as auth
        
        # 验证属性存在
        assert hasattr(auth, 'user_login'), "auth模块缺少user_login属性"
        assert hasattr(auth, 'get_page_logined'), "auth模块缺少get_page_logined属性"
        assert hasattr(auth, 'get_user'), "auth模块缺少get_user属性"
        assert hasattr(auth, 'calculate_offset'), "auth模块缺少calculate_offset属性"
        
        # 测试调用
        user_info = auth.get_user()
        assert isinstance(user_info, dict), "auth.get_user返回类型错误"
        
        print("[PASS] 模块级导入替换测试通过")
        return True

    except Exception as e:
        print(f"[FAIL] 模块级导入替换测试失败: {e}")
        return False


def test_alias_import_replacement():
    """测试别名导入替换（无缝替换）"""
    print("\n=== 测试方案3：别名导入替换（无缝替换） ===")
    
    try:
        # 使用别名，原有代码完全不需要修改
        import src.cpquery_scraper.modules.auth as corutine_user_login
        
        # 原有代码调用方式完全不变
        user_info = corutine_user_login.get_user()
        assert isinstance(user_info, dict), "用户信息类型错误"
        
        # 验证所有原有接口都存在
        original_interfaces = [
            'user_login',
            'get_page_logined', 
            'get_user',
            'check_login_page_is_logined',
            'calculate_offset'
        ]
        
        for interface in original_interfaces:
            assert hasattr(corutine_user_login, interface), f"缺少接口: {interface}"
            assert callable(getattr(corutine_user_login, interface)), f"接口不可调用: {interface}"
        
        print("[PASS] 别名导入替换测试通过")
        print("   原有代码可以完全不修改！")
        return True

    except Exception as e:
        print(f"[FAIL] 别名导入替换测试失败: {e}")
        return False


def test_function_signature_compatibility():
    """测试函数签名兼容性"""
    print("\n=== 测试函数签名兼容性 ===")
    
    import inspect
    import src.cpquery_scraper.modules.auth as corutine_user_login
    import src.cpquery_scraper.modules.auth as auth
    
    # 要检查的函数对
    function_pairs = [
        ('get_user', corutine_user_login.get_user, auth.get_user),
        ('calculate_offset', corutine_user_login.calculate_offset, auth.calculate_offset),
    ]
    
    all_compatible = True
    
    for func_name, original_func, new_func in function_pairs:
        try:
            original_sig = inspect.signature(original_func)
            new_sig = inspect.signature(new_func)
            
            # 检查参数兼容性
            original_params = list(original_sig.parameters.keys())
            new_params = list(new_sig.parameters.keys())
            
            if original_params == new_params:
                print(f"[PASS] {func_name}: 签名完全兼容")
            else:
                print(f"[WARN] {func_name}: 签名有差异")
                print(f"   原函数: {original_params}")
                print(f"   新函数: {new_params}")
                all_compatible = False

        except Exception as e:
            print(f"[FAIL] {func_name}: 签名检查失败 - {e}")
            all_compatible = False
    
    return all_compatible


def test_error_handling_compatibility():
    """测试错误处理兼容性"""
    print("\n=== 测试错误处理兼容性 ===")
    
    try:
        # 测试原模块和新模块的异常处理
        import src.cpquery_scraper.modules.auth as corutine_user_login
        import src.cpquery_scraper.modules.auth as auth
        
        # 测试无效数据处理
        invalid_data = {"invalid": "data"}
        
        original_result = corutine_user_login.calculate_offset(invalid_data)
        new_result = auth.calculate_offset(invalid_data)
        
        # 都应该返回默认值
        from src.cpquery_scraper.config import config
        default_value = config.OFFSET_DEFAULT
        
        original_correct = original_result == default_value
        new_correct = new_result == default_value
        
        if original_correct and new_correct:
            print("[PASS] 错误处理兼容性测试通过")
            return True
        else:
            print(f"[FAIL] 错误处理不一致:")
            print(f"   原模块结果: {original_result} (期望: {default_value})")
            print(f"   新模块结果: {new_result} (期望: {default_value})")
            return False

    except Exception as e:
        print(f"[FAIL] 错误处理兼容性测试失败: {e}")
        return False


def generate_migration_examples():
    """生成迁移示例代码"""
    print("\n=== 迁移示例代码 ===")
    
    examples = {
        "原有代码": '''
# 原有代码示例
from src.cpquery_scraper.modules.auth import user_login, get_page_logined, get_user

async def my_login_function(name, page, url):
    user_info = get_user()
    result = await user_login(name, page, user_info, url)
    if result is None:
        page = await get_page_logined(name, page)
    return result
''',
        
        "方案1 - 直接替换导入": '''
# 只需修改导入语句，其他代码完全不变
from src.cpquery_scraper.modules.auth import user_login, get_page_logined, get_user

async def my_login_function(name, page, url):
    user_info = get_user()
    result = await user_login(name, page, user_info, url)
    if result is None:
        page = await get_page_logined(name, page)
    return result
''',
        
        "方案2 - 模块导入": '''
# 导入模块，调用时加前缀
import src.cpquery_scraper.modules.auth as auth

async def my_login_function(name, page, url):
    user_info = auth.get_user()
    result = await auth.user_login(name, page, user_info, url)
    if result is None:
        page = await auth.get_page_logined(name, page)
    return result
''',
        
        "方案3 - 别名导入（推荐）": '''
# 使用别名，原有代码完全不需要修改！
import src.cpquery_scraper.modules.auth as corutine_user_login

async def my_login_function(name, page, url):
    user_info = corutine_user_login.get_user()
    result = await corutine_user_login.user_login(name, page, user_info, url)
    if result is None:
        page = await corutine_user_login.get_page_logined(name, page)
    return result
'''
    }
    
    for title, code in examples.items():
        print(f"\n{title}:")
        print(code)


def main():
    """主测试函数"""
    print("开始切换验证测试...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_direct_import_replacement,
        test_module_import_replacement,
        test_alias_import_replacement,
        test_function_signature_compatibility,
        test_error_handling_compatibility
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append(False)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("切换验证结果汇总:")
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n[SUCCESS] 所有切换方案验证通过！")
        print("[OK] 新模块可以完全替代原模块")
        print("[OK] 推荐使用方案3（别名导入）实现无缝切换")
    else:
        print("\n[WARNING] 部分测试未通过，需要进一步检查")
    
    # 生成迁移示例
    generate_migration_examples()
    
    return passed == total


if __name__ == "__main__":
    main()
