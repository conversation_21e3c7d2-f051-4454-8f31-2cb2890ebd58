import re
import asyncio
from pathlib import Path
from playwright.async_api import <PERSON><PERSON><PERSON>, BrowserContext, Page

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
# 'auth' 模块现在位于这个新位置
from src.cpquery_scraper.modules.auth import user_login, get_user

class BrowserManager:
    """
    管理 Playwright 的浏览器和浏览器上下文对象。
    处理浏览器启动、上下文创建和登录。
    """
    def __init__(self, playwright):
        self.playwright = playwright
        self.browser: Browser | None = None
        self.logger = get_logger(__name__)

    async def launch_browser(self) -> Browser:
        """启动浏览器实例。"""
        self.logger.info(f"正在启动浏览器（无头模式: {config.BROWSER_HEADLESS}）...")

        browser_path = Path(config.BROWSER_PATH)
        launch_kwargs = {"headless": config.BROWSER_HEADLESS}

        if browser_path.exists():
            launch_kwargs.update({
                "channel": "msedge",
                "executable_path": str(browser_path),
            })
            self.logger.info(f"使用配置的浏览器可执行文件: {browser_path}")
        else:
            # 回退方案：不提供 executable_path，使用已安装的浏览器/Playwright 默认浏览器
            self.logger.warning(f"未找到配置的浏览器可执行文件: {browser_path}，将回退使用默认的 Playwright 浏览器。")
            launch_kwargs.update({"channel": "msedge"})

        self.browser = await self.playwright.chromium.launch(**launch_kwargs)
        return self.browser

    async def get_new_context(self, device_info: dict, name: str = "default") -> BrowserContext:
        """创建一个新的、干净的浏览器上下文。"""
        if not self.browser:
            raise ConnectionError("Browser is not launched. Call launch_browser() first.")

        self.logger.info(f"正在创建新的浏览器上下文（工作进程 '{name}'）...")
        context = await self.browser.new_context(
            **device_info, proxy=None, ignore_https_errors=True
        )

        # 应用 stealth 脚本
        script_path = Path(config.STEALTH_FILE_PATH)
        if script_path.exists():
            await context.add_init_script(path=script_path)
        else:
            self.logger.error(f"未找到 Stealth 脚本: {script_path}！")

        # 中止不必要的请求
        await context.route(
            re.compile(r"(\.png|\.jpg|\.jpeg|\.svg|\.woff|\.ttf)"),
            lambda route: route.abort(),
        )
        return context

    async def login_context(self, context: BrowserContext, name: str = "default") -> BrowserContext:
        """获取一个上下文并执行登录序列。"""
        self.logger.info(f"正在为工作进程 '{name}' 执行登录流程...")
        page = await context.new_page()
        user_info = get_user()

        # auth 模块中的 user_login 函数处理整个流程
        login_result = await user_login(name, page, user_info, config.CPQUERY_URL)

        if login_result is None:
            await context.close()
            raise ConnectionError(f"工作进程 '{name}' 登录失败，已关闭该上下文。")

        page, user_name = login_result

        # 验证和弹出窗口处理逻辑来自原始的 get_context_be_logined
        if (page.url == config.CPQUERY_MAINPAGE_URL and await page.title() == config.CPQUERY_MAINPAGE_TITLE):
            self.logger.info(f"--{name}: 已成功进入查询系统首页。")
            user_welcome_tip = await page.get_by_text("欢迎您，").inner_text()
            if user_name in user_welcome_tip:
                self.logger.info(f"--{name}: 登录状态验证通过。")
            else:
                self.logger.warning(f"--{name}: 欢迎语中未包含用户名，可能需要重新登录。")
        else:
            self.logger.warning(f"--{name}: 登录后未到达预期页面。URL: {page.url}")

        # 处理主页上潜在的弹出窗口/通知
        try:
            self.logger.info(f"--{name}: 检查首页是否有通知弹窗...")
            await page.get_by_role("button", name="确定", exact=True).wait_for(timeout=2000)
            self.logger.info(f"--{name}: 发现通知弹窗，点击“确定”。")
            await page.get_by_role("button", name="确定", exact=True).click()
        except Exception:
            self.logger.info(f"--{name}: 未发现通知弹窗。")

        self.logger.info(f"工作进程 '{name}' 的浏览器上下文登录成功。")
        return context

    async def close_browser(self):
        if self.browser:
            await self.browser.close()
            self.logger.info("浏览器已关闭。")
