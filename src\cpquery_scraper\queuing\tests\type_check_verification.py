"""
类型检查验证脚本

验证重构后的任务调度模块中所有类型提示是否正确。

Author: wwind
Date: 2025.07.08
"""

import logging
from typing import Optional

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_task_item_types():
    """测试TaskItem的类型提示"""
    logger.info("=== 测试TaskItem类型提示 ===")
    
    try:
        from ..models import TaskItem, TaskStatus
        
        # 测试正常创建
        task1 = TaskItem(task_id="test123")
        assert isinstance(task1.created_at, float), f"created_at应该是float类型，实际是{type(task1.created_at)}"
        logger.info("✓ TaskItem正常创建，created_at自动设置为当前时间")
        
        # 测试显式设置None（应该被__post_init__处理）
        task2 = TaskItem(task_id="test456", created_at=None)
        assert isinstance(task2.created_at, float), f"created_at应该被自动设置为float，实际是{type(task2.created_at)}"
        logger.info("✓ TaskItem的created_at=None被正确处理")
        
        # 测试显式设置时间戳
        import time
        timestamp = time.time()
        task3 = TaskItem(task_id="test789", created_at=timestamp)
        assert task3.created_at == timestamp, "显式设置的时间戳应该被保留"
        logger.info("✓ TaskItem显式设置时间戳正常")
        
        # 测试from_dict方法
        task_dict = {
            'task_id': 'test_dict',
            'created_at': None,  # 这应该能正常处理
            'priority': 1
        }
        task4 = TaskItem.from_dict(task_dict)
        assert isinstance(task4.created_at, (float, type(None))), "from_dict应该正确处理None值"
        logger.info("✓ TaskItem.from_dict正确处理None值")
        
        logger.info("✓ TaskItem类型提示测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ TaskItem类型提示测试失败: {e}")
        return False


def test_processing_stats_types():
    """测试ProcessingStats的类型提示"""
    logger.info("=== 测试ProcessingStats类型提示 ===")
    
    try:
        from ..models import ProcessingStats
        
        # 测试正常创建
        stats1 = ProcessingStats()
        assert isinstance(stats1.start_time, float), f"start_time应该是float类型，实际是{type(stats1.start_time)}"
        assert isinstance(stats1.last_update_time, float), f"last_update_time应该是float类型，实际是{type(stats1.last_update_time)}"
        logger.info("✓ ProcessingStats正常创建，时间字段自动设置")
        
        # 测试显式设置None（应该被__post_init__处理）
        stats2 = ProcessingStats(start_time=None, last_update_time=None)
        assert isinstance(stats2.start_time, float), "start_time=None应该被自动设置"
        assert isinstance(stats2.last_update_time, float), "last_update_time=None应该被自动设置"
        logger.info("✓ ProcessingStats的None值被正确处理")
        
        # 测试方法返回类型
        success_rate = stats1.get_success_rate()
        assert isinstance(success_rate, float), f"success_rate应该是float类型，实际是{type(success_rate)}"
        
        processing_time = stats1.get_processing_time()
        assert isinstance(processing_time, float), f"processing_time应该是float类型，实际是{type(processing_time)}"
        
        avg_time = stats1.get_average_processing_time()
        assert isinstance(avg_time, float), f"average_processing_time应该是float类型，实际是{type(avg_time)}"
        
        logger.info("✓ ProcessingStats方法返回类型正确")
        
        logger.info("✓ ProcessingStats类型提示测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ ProcessingStats类型提示测试失败: {e}")
        return False


def test_optional_types():
    """测试Optional类型的正确使用"""
    logger.info("=== 测试Optional类型使用 ===")
    
    try:
        from ..models import TaskItem, ProcessingStats
        
        # 测试TaskItem的Optional字段
        task = TaskItem(task_id="test", data=None)
        assert task.data is None, "data字段应该可以为None"
        logger.info("✓ TaskItem的Optional[Dict]字段正常")
        
        # 测试带数据的情况
        task_with_data = TaskItem(task_id="test2", data={"key": "value"})
        assert isinstance(task_with_data.data, dict), "data字段应该可以是dict"
        logger.info("✓ TaskItem的data字段支持dict类型")
        
        logger.info("✓ Optional类型使用测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ Optional类型使用测试失败: {e}")
        return False


def test_enum_types():
    """测试枚举类型"""
    logger.info("=== 测试枚举类型 ===")
    
    try:
        from ..models import DataSourceType, QueueType, TaskStatus, OperationType
        
        # 测试所有枚举类型
        enums_to_test = [
            (DataSourceType, ['mysql', 'excel', 'redis']),
            (QueueType, ['task_queue', 'result_queue']),
            (TaskStatus, [0, 1, 2, 3]),  # 数值枚举
            (OperationType, ['read', 'write', 'update', 'delete'])
        ]
        
        for enum_class, expected_values in enums_to_test:
            enum_values = [item.value for item in enum_class]
            for expected in expected_values:
                assert expected in enum_values, f"{enum_class.__name__}应该包含值{expected}"
            logger.info(f"✓ {enum_class.__name__}枚举值正确")
        
        logger.info("✓ 枚举类型测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 枚举类型测试失败: {e}")
        return False


def run_all_type_checks():
    """运行所有类型检查测试"""
    logger.info("开始类型检查验证...")
    
    tests = [
        ("TaskItem类型提示", test_task_item_types),
        ("ProcessingStats类型提示", test_processing_stats_types),
        ("Optional类型使用", test_optional_types),
        ("枚举类型", test_enum_types),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- 开始测试: {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现未捕获异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("类型检查结果汇总:")
    logger.info("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info("-"*50)
    logger.info(f"总计: {len(results)} 个测试")
    logger.info(f"通过: {passed} 个")
    logger.info(f"失败: {failed} 个")
    logger.info(f"成功率: {passed/len(results)*100:.1f}%")
    
    if failed == 0:
        logger.info("\n🎉 所有类型检查通过！类型提示修复成功！")
    else:
        logger.warning(f"\n⚠️  有 {failed} 个类型检查失败，需要进一步修复")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_type_checks()
    exit(0 if success else 1)
