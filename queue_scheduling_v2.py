"""
Legacy compatibility shim for queue_scheduling_v2.py

This module provides backward compatibility for the queue scheduling functionality.
It re-exports the key functions that were in the original queue_scheduling_v2.py:
- gen_task_queue
- result_queue_save_to_Mysql

The original queue_scheduling_v2.py provided:
1. Task queue generation from various sources (mysql/excel/redis)
2. Result queue processing and MySQL synchronization
3. Compatibility with the original corutine_queue_scheduling.py interface
4. Support for FLAG_SPIDER_RUNNING gate and TASK_QUEUE_FROM_REDIS_SIZE threshold
"""

import asyncio
import time
from typing import Optional

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.queuing import TaskProcessorFactory
from src.cpquery_scraper.pipelines.mysql_pipeline import MySQLPipeline

logger = get_logger(__name__)

async def gen_task_queue(task_queue: asyncio.Queue, source: Optional[str] = None):
    """
    Generate tasks and populate task queue from configured source
    
    This function replicates the original gen_task_queue behavior:
    - Uses config.FLAG_SPIDER_RUNNING as gate for task supplementation
    - Monitors queue size against TASK_QUEUE_FROM_REDIS_SIZE threshold
    - Fetches tasks from configured source (mysql/excel/redis)
    - Logs queue monitoring at TASK_MONITOR_INTERVAL intervals
    - Supports batch task fetching and queue population
    
    Args:
        task_queue: asyncio.Queue to populate with tasks
        source: Optional source override (defaults to config.TASK_SOURCE)
    """
    source = source or config.TASK_SOURCE
    logger.info(f"Starting task queue generation from source: {source}")
    
    # Create appropriate task processor based on source
    try:
        if source == 'redis':
            processor = TaskProcessorFactory.create_redis_processor(
                redis_params=config.REDIS_PARAM,
                task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
                result_queue_max_size=5000,
                monitor_interval=config.TASK_MONITOR_INTERVAL,
                logger=logger,
            )
        elif source == 'mysql':
            processor = TaskProcessorFactory.create_mysql_processor(
                mysql_params={**config.MYSQL_PARAM, 'task_table': 'epatent_0'},
                task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
                result_queue_max_size=5000,
                monitor_interval=config.TASK_MONITOR_INTERVAL,
                logger=logger,
            )
        else:  # excel
            processor = TaskProcessorFactory.create_excel_processor(
                excel_dir=str(config.TASK_EXCEL_DIR),
                task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
                result_queue_max_size=5000,
                logger=logger,
            )
    except Exception as e:
        logger.error(f"Failed to create task processor for source {source}: {e}")
        return
    
    last_monitor_time = time.time()
    
    try:
        async with processor:
            while config.FLAG_SPIDER_RUNNING:
                try:
                    # Monitor queue size at intervals
                    current_time = time.time()
                    if current_time - last_monitor_time >= config.TASK_MONITOR_INTERVAL:
                        logger.info(f"Task queue size: {task_queue.qsize()}")
                        last_monitor_time = current_time
                    
                    # Check if queue needs supplementation
                    if task_queue.qsize() < config.TASK_QUEUE_FROM_REDIS_SIZE:
                        # Fetch tasks from processor
                        fetch_limit = config.TASK_QUEUE_FROM_REDIS_SIZE - task_queue.qsize()
                        tasks_fetched = 0
                        
                        for _ in range(fetch_limit):
                            try:
                                task_item = await processor.get_task(timeout=1.0)
                                if task_item is None:
                                    break
                                await task_queue.put(task_item.task_id)
                                tasks_fetched += 1
                            except asyncio.TimeoutError:
                                break
                        
                        if tasks_fetched > 0:
                            logger.info(f"Added {tasks_fetched} tasks to queue from {source}")
                    
                    # Brief sleep to prevent tight loop
                    await asyncio.sleep(1)
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"Error in gen_task_queue loop: {e}")
                    await asyncio.sleep(5)
                    
    except Exception as e:
        logger.error(f"Fatal error in gen_task_queue: {e}")
    finally:
        logger.info("Task queue generation stopped")

async def result_queue_save_to_Mysql(result_queue: asyncio.Queue):
    """
    Process result queue and save to MySQL
    
    This function replicates the original result_queue_save_to_Mysql behavior:
    - Periodically reads all messages from result_queue
    - Calls trans_format to convert to business table structure
    - Writes to Redis (db=2) with proper key naming
    - Main node periodically syncs Redis data to MySQL in batches
    - Updates synced=1 and deletes synced data from Redis
    - Updates task table epatent_0.state=1 after sync
    
    Args:
        result_queue: asyncio.Queue containing result data
    """
    logger.info("Starting result queue processing to MySQL")
    
    # Import pipelines
    from src.cpquery_scraper.pipelines.redis_pipeline import RedisPipeline
    
    redis_pipeline = RedisPipeline()
    mysql_pipeline = MySQLPipeline()
    
    # Initialize pipelines
    await redis_pipeline.open_spider(spider=None)
    await mysql_pipeline.open_spider(spider=None)
    
    try:
        while config.FLAG_SPIDER_RUNNING:
            try:
                # Batch collect results over the interval period
                batch = []
                end_time = time.time() + config.RESULT_TO_REDIS_INTERVAL
                
                while time.time() < end_time and config.FLAG_SPIDER_RUNNING:
                    try:
                        item = await asyncio.wait_for(result_queue.get(), timeout=1.0)
                        batch.append(item)
                        result_queue.task_done()
                    except asyncio.TimeoutError:
                        continue
                
                # Process batch to Redis
                if batch:
                    logger.info(f"Processing {len(batch)} results to Redis")
                    await redis_pipeline.process_items_batch(batch)
                
                # Main node: sync Redis to MySQL
                if mysql_pipeline.is_main_role:
                    await mysql_pipeline.run_sync_task()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in result_queue_save_to_Mysql loop: {e}")
                await asyncio.sleep(5)
                
    except Exception as e:
        logger.error(f"Fatal error in result_queue_save_to_Mysql: {e}")
    finally:
        await redis_pipeline.close_spider(spider=None)
        await mysql_pipeline.close_spider(spider=None)
        logger.info("Result queue processing stopped")

# Legacy compatibility exports
__all__ = [
    'gen_task_queue',
    'result_queue_save_to_Mysql'
]
