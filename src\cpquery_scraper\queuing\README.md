# 任务调度模块 v2.0

重构后的任务调度模块，提供更好的代码结构、错误处理和可扩展性，同时保持与原有代码的完全兼容性。

## 主要特性

- **模块化设计**：清晰的职责分离，易于维护和扩展
- **统一异常处理**：完善的异常处理机制和错误信息
- **类型安全**：完整的类型注解，提高代码可靠性
- **可扩展性**：易于添加新的数据源类型
- **向后兼容**：保持与原有代码的完全兼容性
- **资源管理**：正确的连接池和资源管理
- **监控统计**：内置的性能监控和统计功能

## 目录结构

```
queue_scheduling/
├── __init__.py              # 主模块入口
├── exceptions.py            # 异常类定义
├── models.py               # 数据模型和枚举
├── compatibility.py        # 兼容性接口
├── data_sources/           # 数据源模块
│   ├── __init__.py
│   ├── base.py            # 抽象基类
│   ├── mysql_source.py    # MySQL数据源
│   ├── excel_source.py    # Excel数据源
│   └── redis_source.py    # Redis数据源
├── queue_managers/         # 队列管理器
│   ├── __init__.py
│   ├── task_queue_manager.py    # 任务队列管理
│   └── result_queue_manager.py  # 结果队列管理
├── processors/             # 处理器
│   ├── __init__.py
│   └── task_processor.py  # 任务处理器
├── tests/                  # 测试文件
│   ├── __init__.py
│   ├── test_data_sources.py
│   ├── test_queue_managers.py
│   └── verification_script.py
└── examples/               # 使用示例
    ├── __init__.py
    └── basic_usage.py
```

## 快速开始

### 兼容性模式（推荐用于现有代码）

```python
# 直接替换原有的import语句
from queue_scheduling_v2 import gen_task_queue, result_queue_save_to_Mysql

# 其余代码保持不变
async def main():
    task_queue = asyncio.Queue(5000)
    result_queue = asyncio.Queue(10000)
    
    t1 = asyncio.create_task(gen_task_queue(task_queue, source='mysql'))
    t2 = asyncio.create_task(result_queue_save_to_Mysql(result_queue))
    
    await task_queue.join()
    await result_queue.join()
```

### 新接口模式（推荐用于新代码）

```python
from queue_scheduling_v2 import TaskProcessorFactory

async def main():
    # 创建任务处理器
    processor = TaskProcessorFactory.create_mysql_processor(
        mysql_params={'host': 'localhost', 'user': 'root', 'password': 'password', 'database': 'test'},
        task_queue_max_size=5000
    )
    
    async with processor:
        while True:
            # 获取任务
            task = await processor.get_task(timeout=10.0)
            if task is None:
                break
            
            try:
                # 处理任务
                result = process_task(task)
                
                # 提交结果
                await processor.submit_result(result)
                await processor.task_done(task, success=True)
                
            except Exception as e:
                await processor.task_done(task, success=False)
```

## 支持的数据源

### MySQL数据源
- 从MySQL数据库读取任务列表
- 支持任务状态更新
- 自动连接池管理

### Excel数据源
- 从Excel文件读取任务列表
- 支持多种列名格式
- 自动文件移动到已处理目录

### Redis数据源
- 从Redis队列读取任务
- 支持任务补充机制
- 高性能任务分发

## 配置选项

### 数据源配置
```python
from queue_scheduling_v2 import DataSourceConfig, DataSourceType

config = DataSourceConfig(
    source_type=DataSourceType.MYSQL,
    connection_params={
        'host': 'localhost',
        'user': 'root',
        'password': 'password',
        'database': 'test'
    },
    max_connections=20,
    timeout=60,
    retry_times=3
)
```

### 队列配置
```python
from queue_scheduling_v2 import QueueConfig, QueueType

config = QueueConfig(
    queue_type=QueueType.TASK_QUEUE,
    max_size=10000,
    monitor_interval=30,
    low_threshold=1000,
    batch_size=200
)
```

## 错误处理

模块提供了完善的异常处理机制：

```python
from queue_scheduling_v2 import (
    TaskSchedulingError, DataSourceError,
    QueueOperationError, ConfigurationError
)

try:
    processor = TaskProcessorFactory.create_mysql_processor(invalid_params)
except DataSourceError as e:
    print(f"数据源错误: {e}")
    print(f"错误代码: {e.error_code}")
    print(f"错误详情: {e.details}")
except ConfigurationError as e:
    print(f"配置错误: {e}")
```

## 监控和统计

```python
# 获取处理器状态
status = await processor.get_status()
print(f"任务队列大小: {status['task_queue']['queue_size']}")
print(f"处理统计: {status['task_queue']['stats']}")

# 获取详细统计信息
stats = processor.task_queue_manager.stats
print(f"成功率: {stats.get_success_rate():.2%}")
print(f"平均处理时间: {stats.get_average_processing_time():.2f}秒")
```

## 测试

运行测试套件：

```bash
# 运行单元测试
python -m unittest queue_scheduling.tests.test_data_sources
python -m unittest queue_scheduling.tests.test_queue_managers

# 运行验证脚本
python queue_scheduling/tests/verification_script.py

# 运行使用示例
python queue_scheduling/examples/basic_usage.py
```

## 迁移指南

### 从原有模块迁移

1. **最简单的方式**：直接替换import语句
   ```python
   # 原有代码
   from corutine_queue_scheduling import gen_task_queue

   # 新代码
   from queue_scheduling_v2 import gen_task_queue
   ```

2. **推荐方式**：逐步迁移到新接口
   ```python
   # 使用新的TaskProcessor替代原有的函数
   processor = TaskProcessorFactory.create_mysql_processor(mysql_params)
   ```

### 配置迁移

原有的配置参数可以直接使用：
```python
# 原有配置
from corutine_config import config

# 新接口中使用
processor = TaskProcessorFactory.create_mysql_processor(
    mysql_params=config.MYSQL_PARAM,
    task_queue_max_size=config.TASK_QUEUE_MAX_SIZE,
    monitor_interval=config.TASK_MONITOR_INTERVAL
)
```

## 版本信息

- **版本**: 2.0.0
- **作者**: wwind
- **日期**: 2025.07.08
- **兼容性**: 与原有corutine_queue_scheduling.py完全兼容

## 许可证

与原项目保持一致。
