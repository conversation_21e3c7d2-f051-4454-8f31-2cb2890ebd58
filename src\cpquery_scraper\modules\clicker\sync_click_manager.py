"""
同步点击管理器
负责同步模式的点击操作和数据获取
"""
import asyncio
import json
import time
from typing import List, Optional
from playwright.async_api import Page, expect

from .exceptions import (
    DataExtractionError, RetryExhaustedError, PageCrashedError,
    RouteInterceptError, ResponseTimeoutError
)
from .file_handler import FileHandler
from src.cpquery_scraper.utils.helpers import (
    event_to_request_url, check_page_exception_msg_is_crashed,
    correct_filename_discrepancy, has_attachments
)


class SyncClickManager:
    """同步点击管理器"""
    
    def __init__(self, config, logger):
        """
        初始化同步点击管理器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.file_handler = FileHandler(config, logger)
    
    async def click_events_sync(
        self,
        name: str,
        data_page: Page,
        an: str,
        url_init_page: str,
    ) -> List[dict[str, dict]]:
        """
        同步模式：逐个点击、获取各个事件的按钮，以及提取数据
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            an: 申请号
            url_init_page: 初始化页面URL
            
        Returns:
            list: 事件数据列表
            
        Raises:
            DataExtractionError: 数据提取失败时抛出
            PageCrashedError: 页面崩溃时抛出
        """
        # 获取需要处理的事件列表
        event_list = [event for event, mask in self.config.DATA_SCOPE_MASK.items() if mask]
        
        # 逐个点击事件(文件夹)按钮，获取数据
        result: List[dict[str, dict]] = []
        
        for event in event_list:
            self.logger.debug(f"--{name}: 开始获取 {an} 的 '{event}'")
            
            try:
                event_info = await self._click_event(data_page, event, name, an, url_init_page)
            except ValueError as e:
                if "退出" in repr(e):
                    raise PageCrashedError(
                        "页面崩溃，需要退出",
                        error_code="PAGE_CRASHED",
                        crash_reason=str(e)
                    )
                else:
                    raise DataExtractionError(
                        f"点击事件失败: {str(e)}",
                        error_code="CLICK_EVENT_FAILED",
                        event=event,
                        an=an
                    )
            
            # 处理审查信息的特殊逻辑
            if event == "审查信息" and event_info:
                event_info = await self._process_examination_info(
                    name, data_page, event_info, an, url_init_page
                )
            
            if event_info is not None:
                result.append(event_info.copy())
                event_info.clear()
        
        # 验证结果
        if len(result) != len(event_list):
            raise DataExtractionError(
                f"结果数量与事件数量不匹配：{len(result)} != {len(event_list)}",
                error_code="RESULT_COUNT_MISMATCH",
                details={"expected": len(event_list), "actual": len(result)}
            )
        
        return result
    
    async def _click_event(
        self, 
        data_page: Page, 
        event: str, 
        name: str, 
        an: str, 
        url_init_page: str,
        retry_times: int = 0, 
        should_mock_nodeId: bool = False
    ) -> Optional[dict]:
        """
        点击事件按钮
        
        点击页面上的某一个具体事件对应的导航按钮，截获其后端接口响应数据
        
        Args:
            data_page: 数据页面对象
            event: 事件名称
            name: 任务名称
            an: 申请号
            url_init_page: 初始化页面URL
            retry_times: 重试次数
            should_mock_nodeId: 是否需要模拟nodeId
            
        Returns:
            dict: 事件数据或None
            
        Raises:
            RetryExhaustedError: 重试次数耗尽时抛出
            PageCrashedError: 页面崩溃时抛出
        """
        if retry_times >= self.config.MAX_DATA_PAGE_CLICK_RETRY_TIMES:
            error_msg = f"{an} {event} 获取{self.config.MAX_DATA_PAGE_INIT_RETRY_TIMES}次失败，放弃请求"
            self.logger.error(f"--{name}: {error_msg}")
            
            if event == "申请信息":
                # 对于申请信息，返回特殊代码表示需要通过查询页补充
                return {
                    "code": 201,
                    "data": None,
                    "msg": "需要通过查询页补充申请信息",
                }
            else:
                return {
                    "code": 504,
                    "data": None,
                    "msg": error_msg,
                }
        
        # 设置路由拦截器
        async def handle_route(route):
            if route.request.resource_type == "xhr" and "zhuanlisqh" in route.request.post_data:
                mock_post_data = json.loads(route.request.post_data)
                mock_post_data['zhuanlisqh'] = an
                
                # 特殊处理某些事件的nodeId
                if event in ("审查信息", "申请文件", "中间文件", "通知书", "复审文件", "无效文件"):
                    if should_mock_nodeId:
                        mock_post_data['nodeId'] = "aj_wgk_scxx"
                
                await route.continue_(post_data=mock_post_data)
            else:
                raise RouteInterceptError(
                    f"请求所含数据错误：route.request={route.request}",
                    error_code="INVALID_REQUEST_DATA",
                    route_pattern=event_to_request_url(event)
                )
        
        # 注册路由拦截器
        await data_page.route(event_to_request_url(event), handle_route)
        
        try:
            # 点击事件导航标签，截取响应数据
            async with data_page.expect_response(
                event_to_request_url(event), 
                timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_SYNC
            ) as info:
                if event == "申请信息":
                    # 申请信息页面，刷新替代点击
                    data_page = await self._refresh_page(data_page, event, name, an)
                else:
                    await data_page.get_by_role("treeitem").get_by_text(event).click()
            
            info_response = await info.value
            result = await info_response.json()
            
            # 数据校验
            if event == "申请信息" and result.get('code') == 200:
                data = result.get('data', {})
                zhuluxmxx = data.get('zhuluxmxx', {}).get('zhuluxmxx', {})
                if zhuluxmxx.get('zhuanlisqh') != an:
                    raise DataExtractionError(
                        f"申请信息数据中zhuanlisqh字段与请求参数不符",
                        error_code="DATA_VALIDATION_FAILED",
                        event=event,
                        an=an,
                        details={"expected": an, "actual": zhuluxmxx.get('zhuanlisqh')}
                    )
            
            # 调试输出
            if self.config.TEST_MODE:
                self.logger.debug(f"--{name}: event={event} result={result}")
                
        except Exception as err:
            return await self._handle_click_error(
                data_page, event, name, an, url_init_page, retry_times, err, should_mock_nodeId
            )
        else:
            # 成功获取数据，移除路由拦截器
            await data_page.unroute(event_to_request_url(event), handle_route)
            
            # 特殊处理审查信息的空数据情况
            if (event == "审查信息" and result.get('code') == 200 and 
                len(result.get('data', [])) == 0):
                if self.config.TEST_MODE:
                    self.logger.debug(f"--{name}: {an} {event} 获取子文件夹信息为空: {result}")
                
                self.logger.info(f"--{name}: {an} {event} 获取子文件夹信息为空，本申请对当前用户未公开，修改nodeId后重试")
                should_mock_nodeId = True
                retry_times += 1
                data_page = await self._refresh_page(data_page, event, name, an, retry=True)
                result = await self._click_event(
                    data_page, event, name, an, url_init_page, retry_times, should_mock_nodeId
                )
        
        return result
    
    async def _refresh_page(
        self, 
        data_page: Page, 
        event: str, 
        name: str, 
        an: str, 
        retry: bool = False
    ) -> Page:
        """
        刷新当前页面，适应网站规则
        
        Args:
            data_page: 数据页面对象
            event: 事件名称
            name: 任务名称
            an: 申请号
            retry: 是否为重试刷新
            
        Returns:
            Page: 刷新后的页面对象
            
        Raises:
            PageCrashedError: 页面崩溃时抛出
        """
        if not retry:
            try:
                await data_page.reload(
                    wait_until="networkidle", 
                    timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_REFRESH
                )
                await expect(data_page.get_by_role("treeitem").get_by_text(event)).to_be_visible()
            except Exception as err:
                self.logger.warning(f"--{name}: {an} 页面初次刷新不成功，开始重试：{err}")
                return await self._refresh_page(data_page, event, name, an, retry=True)
            else:
                return data_page
        else:
            i = 1
            while True:
                if i > self.config.MAX_DATA_PAGE_REFRESH_RETRY_TIMES:
                    self.logger.error(f"--{name}: {an} 页面刷新重试 {self.config.MAX_DATA_PAGE_REFRESH_RETRY_TIMES} 次不成功，退出")
                    break
                
                self.logger.info(f"--{name}: {an} 页面刷新重试，第 {i} 次")
                try:
                    await data_page.reload(
                        wait_until="networkidle", 
                        timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_REFRESH
                    )
                    await expect(data_page.get_by_role("treeitem").get_by_text(event)).to_be_visible()
                except Exception as err:
                    if check_page_exception_msg_is_crashed(name, repr(err), "refresh_page"):
                        self.logger.error(f"--{name}: {an} 页面刷新重试，发现页面关闭或崩溃，退出")
                        raise PageCrashedError(
                            "页面刷新失败，页面已崩溃",
                            error_code="PAGE_CRASHED_DURING_REFRESH",
                            crash_reason=str(err)
                        )
                    else:
                        self.logger.warning(f"--{name}: {an} 页面刷新重试，出错：{err}")
                        i += 1
                        await asyncio.sleep(2)
                        continue
                else:
                    self.logger.info(f"--{name}: {an} 页面刷新重试成功")
                    break
        
        return data_page
    
    async def _handle_click_error(
        self, 
        data_page: Page, 
        event: str, 
        name: str, 
        an: str, 
        url_init_page: str,
        retry_times: int, 
        err: Exception, 
        should_mock_nodeId: bool
    ) -> Optional[dict]:
        """
        处理点击错误
        
        Args:
            data_page: 数据页面对象
            event: 事件名称
            name: 任务名称
            an: 申请号
            url_init_page: 初始化页面URL
            retry_times: 重试次数
            err: 异常对象
            should_mock_nodeId: 是否需要模拟nodeId
            
        Returns:
            dict: 处理结果或None
        """
        if "/detail/index?zhuanlisqh=" not in data_page.url:
            self.logger.warning(f"--{name}: 当前url={data_page.url} 显示当前页面非数据页，重新初始化为数据页")
            
            # 重新初始化页面
            i = 0
            while True:
                if i > self.config.MAX_DATA_PAGE_INIT_RETRY_TIMES:
                    error_msg = f"重新初始化{self.config.MAX_DATA_PAGE_INIT_RETRY_TIMES}次仍不成功，退出并放弃{an}:{event}的数据请求"
                    self.logger.error(f"--{name}: {error_msg}")
                    
                    if event == "申请信息":
                        return {
                            "code": 201,
                            "data": None,
                            "msg": "需要通过查询页补充申请信息",
                        }
                    else:
                        return {
                            "code": 504,
                            "data": None,
                            "msg": error_msg,
                        }
                else:
                    try:
                        self.logger.info(f"--{name}: 重新初始化第 {i} 次")
                        await data_page.goto(
                            url_init_page, wait_until="networkidle", timeout=30000
                        )
                    except Exception as init_err:
                        self.logger.warning(f"--{name}: 重新初始化当前页面出错，10秒后重试：{init_err}")
                        await asyncio.sleep(10)
                        i += 1
                        continue
                    else:
                        self.logger.info(f"--{name}: 页面重新初始化第 {i} 次成功")
                        break
            
            retry_times += 1
            page = await self._refresh_page(data_page, event, name, an, retry=True)
            return await self._click_event(
                page, event, name, an, url_init_page, retry_times, should_mock_nodeId
            )
        else:
            retry_times += 1
            page = await self._refresh_page(data_page, event, name, an, retry=True)
            return await self._click_event(
                page, event, name, an, url_init_page, retry_times, should_mock_nodeId
            )

    async def _process_examination_info(
        self,
        name: str,
        data_page: Page,
        event_info: dict,
        an: str,
        url_init_page: str
    ) -> dict:
        """
        处理审查信息的特殊逻辑

        Args:
            name: 任务名称
            data_page: 数据页面对象
            event_info: 事件信息
            an: 申请号
            url_init_page: 初始化页面URL

        Returns:
            dict: 处理后的审查信息

        Raises:
            DataExtractionError: 数据处理失败时抛出
        """
        if event_info.get('code') != 200:
            self.logger.error(f"--{name}: 审查信息 点击后未返回正确数据，返回数据 = {event_info}")
            raise DataExtractionError(
                "审查信息点击后未返回正确code",
                error_code="INVALID_RESPONSE_CODE",
                event="审查信息",
                an=an,
                details={"response": event_info}
            )

        time_start_all = time.time()

        # 验证获取到的审查信息，是否包含了5个子文件夹目录
        data = event_info.get('data', [])
        if len(data) != 5:
            raise DataExtractionError(
                f"审查信息点击后返回的数据格式错误，子文件夹数量不正确: {len(data)}",
                error_code="INVALID_SUBFOLDER_COUNT",
                event="审查信息",
                an=an,
                details={"expected": 5, "actual": len(data), "data": data}
            )

        # 获取每个子目录下的文件信息
        for sub_dir in data:
            sub_dir_name = sub_dir.get('name', '')
            if sub_dir_name not in ("申请文件", "中间文件", "通知书", "复审文件", "无效文件"):
                raise DataExtractionError(
                    f"审查信息子文件夹名称不正确: {sub_dir_name}",
                    error_code="INVALID_SUBFOLDER_NAME",
                    event="审查信息",
                    an=an,
                    details={"invalid_name": sub_dir_name, "data": data}
                )

            # 点击子目录名称，获取子目录下的文件列表信息
            try:
                time_start = time.time()
                files_info = await self._click_event(data_page, sub_dir_name, name, an, url_init_page)
            except ValueError as e:
                raise DataExtractionError(
                    f'获取子目录信息（子目录中的文件列表信息）时发生错误：{repr(e)}',
                    error_code="SUBFOLDER_ACCESS_FAILED",
                    event="审查信息",
                    an=an,
                    details={"sub_dir": sub_dir_name, "error": str(e)}
                )

            if files_info is not None and files_info.get('code') == 200:
                sub_dir['files'] = files_info['data']
                self.logger.info(f"--{name}: 审查信息 -> 列出子目录 '{sub_dir_name}' 下的文件列表成功，耗时：{time.time() - time_start:.2f}秒")

                # 调试输出文件列表
                for file_item in files_info['data']:
                    self.logger.debug(f"文件: {file_item.get('name', 'Unknown')}")
            else:
                self.logger.warning(f"--{name}: 审查信息 -> {sub_dir_name} 点击后未返回正确数据，返回数据 = {files_info}")
                sub_dir['files'] = None
                continue

            # 获取每个文件的详细信息
            await self._process_files_in_subdir(name, data_page, sub_dir, an)

        # 记录总耗时
        time_end_all = time.time()
        self.logger.info(f"--{name}: 审查信息 事件获取成功，耗时：{time_end_all - time_start_all:.2f}秒")

        return event_info

    async def _process_files_in_subdir(
        self,
        name: str,
        data_page: Page,
        sub_dir: dict,
        an: str
    ) -> None:
        """
        处理子目录中的文件

        Args:
            name: 任务名称
            data_page: 数据页面对象
            sub_dir: 子目录信息
            an: 申请号
        """
        if not sub_dir.get('files'):
            return

        for file in sub_dir['files']:
            if not file.get('isLeaf', True):
                # 子目录情况，暂时跳过
                continue

            # 处理文件名称不一致的问题
            filename = file.get('name', '')
            file_code = file.get('additionalData', {}).get('wenjiandm', '')
            file['name'] = correct_filename_discrepancy(filename, file_code)

            self.logger.debug(f"--{name}: 审查信息 -> {sub_dir['name']} -> {file['name']} 开始获取静态URL")

            # 点击文件名称，获取文件url信息
            try:
                file_info = await self.file_handler.click_file_name(name, data_page, file, an)
            except Exception as e:
                self.logger.error(f'获取文件: {file["name"]} 信息时发生错误：{repr(e)}')
                file['fetch_info'] = {
                    "code": 500,
                    "msg": f"获取文件信息失败: {str(e)}"
                }
                continue

            file['fetch_info'] = file_info

            # 处理附件
            await self._process_file_attachments(name, data_page, file, an)

    async def _process_file_attachments(
        self,
        name: str,
        data_page: Page,
        file: dict,
        an: str
    ) -> None:
        """
        处理文件附件

        Args:
            name: 任务名称
            data_page: 数据页面对象
            file: 文件信息
            an: 申请号
        """
        fetch_info = file.get('fetch_info', {})
        if not fetch_info or fetch_info.get('code') != 200:
            return

        # 检查是否有附件
        attachments = has_attachments(fetch_info)
        if not isinstance(attachments, tuple) or len(attachments) != 2:
            fetch_info['attachment'] = None
            return

        tzs_attach_list, jsbg_attach = attachments
        fetch_info['attachment'] = {'tzs_attach': [], 'jsbg_attach': jsbg_attach}

        # 处理通知书附件
        if len(tzs_attach_list) > 0:
            self.logger.info(f"--{name}: {file['name']} 存在{len(tzs_attach_list)}个通知书附件")

            for tzs_attach in tzs_attach_list:
                tzs_attach['name'] = tzs_attach['fujianmc']
                self.logger.debug(f"--获取附件静态URL: {tzs_attach['name']}")

                try:
                    attachment_info = await self.file_handler.click_file_name(name, data_page, tzs_attach, an)
                    fetch_info['attachment']['tzs_attach'].append(attachment_info)
                except Exception as e:
                    self.logger.error(f'获取附件: {tzs_attach["name"]} 信息时发生错误：{repr(e)}')
                    fetch_info['attachment']['tzs_attach'].append({
                        "code": 500,
                        "msg": f"获取附件信息失败: {str(e)}"
                    })
        else:
            self.logger.debug(f"--{name}: 审查信息 -> {file['name']} 无通知书附件")

        # 整理检索报告数据
        if "jsbg_data" in fetch_info and fetch_info["jsbg_data"]:
            fetch_info["attachment"]["jsbg_attach"] = fetch_info["jsbg_data"]
            fetch_info.pop("jsbg_data")
