import asyncio
import time

from datetime import datetime

from src.cpquery_scraper.utils.db import RedisConnection
from src.cpquery_scraper.utils.formatters import trans_format
from src.cpquery_scraper.utils.logger import get_logger
from .base_pipeline import BasePipeline

class RedisPipeline(BasePipeline):
    """
    一个处理抓取项目并将其保存到 Redis 的管道。
    """
    def __init__(self):
        self.logger = get_logger(__name__)
        self.redis_conn = RedisConnection()
        self.redis_key_prefix = "patent_id:"
        self.redis_index_key = "patent_index"

    async def open_spider(self, spider):
        """在爬虫打开时连接到 Redis。"""
        self.logger.info("正在打开 Redis 连接（管道）。")
        self.redis_conn.connect_redis(db=2)

    async def close_spider(self, spider):
        """在爬虫关闭时关闭 Redis 连接。"""
        self.logger.info("正在关闭 Redis 连接（管道）。")
        self.redis_conn.close_redis()

    async def process_item(self, item, spider):
        """
        处理单个抓取的项目。
        1. 使用 trans_format 格式化原始项目。
        2. 将格式化后的项目保存到 Redis 哈希中。
        """
        try:
            # 1. 格式化原始数据
            formatted_item = trans_format(item)

            if formatted_item is None:
                self.logger.warning(f"格式化过程中丢弃了该条目: {list(item.keys())[0]}")
                return None # 丢弃项目

            # 2. 保存到 Redis
            patent_id = formatted_item.get('patent_id')
            if not patent_id:
                self.logger.error(f"格式化后的条目缺少 patent_id: {formatted_item}")
                return None # 丢弃项目

            redis_key = f"{self.redis_key_prefix}{patent_id}"

            pipe = self.redis_conn.pipeline()
            if pipe is None:
                raise ConnectionError("Failed to get Redis pipeline.")

            for key, value in formatted_item.items():
                pipe.hset(redis_key, key, str(value))

            pipe.hset(redis_key, "timestamp", datetime.now().isoformat())
            pipe.hset(redis_key, "synced", "0")  # 0 = 未同步到 MySQL
            pipe.sadd(self.redis_index_key, patent_id)

            await asyncio.to_thread(pipe.execute)
            # self.logger.info(f"已成功将 {patent_id} 保存到 Redis。")
            return formatted_item # 将项目传递到下一个管道

        except Exception as e:
            self.logger.error(f"RedisPipeline 处理条目 {list(item.keys())[0]} 时出错: {e}")
            # 根据错误，您可能希望丢弃项目或引发异常
            return None

    async def process_items_batch(self, items: list[dict]) -> int:
        """批量处理多条抓取项目，返回成功写入条数。"""
        if not items:
            return 0
        ts = time.time()
        success = 0
        for item in items:
            try:
                res = await self.process_item(item, spider=None)
                if res is not None:
                    success += 1
            except Exception as e:
                self.logger.error(f"批量写入时单条处理失败: {e}")
        elapsed = time.time() - ts
        self.logger.info(f"将 {success} 条数据写入Redis，耗时 {elapsed:.2f} 秒")
        return success
