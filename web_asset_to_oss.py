"""
Legacy compatibility shim for web_asset_to_oss.py

This module provides backward compatibility for static asset processing.
It re-exports the StaticProcessor class with process_url method for
downloading and uploading PDF files to Alibaba Cloud OSS.

The original web_asset_to_oss.py provided:
1. StaticProcessor class with process_url method
2. PDF download from cpquery system
3. File naming and organization
4. Upload to Alibaba Cloud OSS
5. Return of OSS URL for result injection
"""

import asyncio
import hashlib
import os
import tempfile
import time
from typing import Optional
from urllib.parse import urlparse

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.core.asset_processor import StaticProcessor as RefactoredStaticProcessor

logger = get_logger(__name__)

class StaticProcessor:
    """
    Static asset processor for PDF files
    
    This class replicates the original StaticProcessor behavior:
    - Downloads PDF files from cpquery system
    - Generates consistent file names
    - Uploads to Alibaba Cloud OSS
    - Returns OSS URL for result injection as "oss_pdfUrl"
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self._refactored_processor = RefactoredStaticProcessor()
    
    async def process_url(self, url: str) -> Optional[str]:
        """
        Process PDF URL: download → name → upload → return OSS URL
        
        This method replicates the original process_url behavior:
        - Downloads PDF from the provided URL
        - Generates consistent filename based on content/URL
        - Uploads to OSS with proper organization
        - Returns OSS URL for injection into result structure
        
        Args:
            url: PDF URL to process
            
        Returns:
            str: OSS URL if successful, None if failed
        """
        if not url or not url.strip():
            self.logger.warning("Empty URL provided to process_url")
            return None
        
        try:
            # Use the refactored processor for actual processing
            oss_url = await self._refactored_processor.process_url(url)
            
            if oss_url:
                self.logger.info(f"Successfully processed PDF: {url} → {oss_url}")
                return oss_url
            else:
                self.logger.warning(f"Failed to process PDF: {url}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error processing PDF URL {url}: {e}")
            return None
    
    async def close(self):
        """Close the processor and cleanup resources"""
        try:
            await self._refactored_processor.close()
        except Exception as e:
            self.logger.error(f"Error closing StaticProcessor: {e}")
    
    def generate_filename(self, url: str, content: bytes = None) -> str:
        """
        Generate consistent filename for PDF
        
        Args:
            url: Original PDF URL
            content: PDF content bytes (optional)
            
        Returns:
            str: Generated filename
        """
        try:
            # Parse URL to get base name
            parsed_url = urlparse(url)
            path_parts = parsed_url.path.split('/')
            base_name = path_parts[-1] if path_parts else 'document'
            
            # Remove extension if present
            if '.' in base_name:
                base_name = base_name.rsplit('.', 1)[0]
            
            # Generate hash for uniqueness
            if content:
                content_hash = hashlib.md5(content).hexdigest()[:8]
            else:
                url_hash = hashlib.md5(url.encode('utf-8')).hexdigest()[:8]
                content_hash = url_hash
            
            # Create filename with timestamp and hash
            timestamp = int(time.time())
            filename = f"{base_name}_{timestamp}_{content_hash}.pdf"
            
            return filename
            
        except Exception as e:
            self.logger.error(f"Error generating filename for {url}: {e}")
            # Fallback filename
            fallback_hash = hashlib.md5(url.encode('utf-8')).hexdigest()[:8]
            return f"document_{int(time.time())}_{fallback_hash}.pdf"
    
    def validate_pdf_content(self, content: bytes) -> bool:
        """
        Validate that content is a valid PDF
        
        Args:
            content: File content bytes
            
        Returns:
            bool: True if valid PDF
        """
        if not content:
            return False
        
        # Check PDF magic number
        return content.startswith(b'%PDF-')
    
    async def download_with_retry(self, url: str, max_retries: int = 3) -> Optional[bytes]:
        """
        Download file with retry logic
        
        Args:
            url: URL to download
            max_retries: Maximum retry attempts
            
        Returns:
            bytes: Downloaded content or None if failed
        """
        for attempt in range(max_retries):
            try:
                # Use the refactored processor's download method
                content = await self._refactored_processor._download_resource(url)
                
                if content and self.validate_pdf_content(content):
                    return content
                else:
                    self.logger.warning(f"Invalid PDF content from {url}")
                    return None
                    
            except Exception as e:
                self.logger.warning(f"Download attempt {attempt + 1} failed for {url}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    self.logger.error(f"Failed to download {url} after {max_retries} attempts")
                    return None
        
        return None

# Legacy compatibility - allow direct instantiation
def create_static_processor() -> StaticProcessor:
    """Create a new static processor instance"""
    return StaticProcessor()

# Legacy compatibility exports
__all__ = [
    'StaticProcessor',
    'create_static_processor'
]
