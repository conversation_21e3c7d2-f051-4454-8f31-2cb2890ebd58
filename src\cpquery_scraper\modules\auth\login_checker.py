"""
登录状态检查模块
"""
from typing import Optional, Any
from playwright.async_api import Page, expect

# from .exceptions import NetworkError
# from .utils import check_page_crashed

class LoginChecker:
    """登录状态检查类"""
    
    def __init__(self, config: Any, logger: Any):
        """
        初始化登录状态检查器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
    
    async def check_login_status(self, name: str, page: Page) -> Optional[bool]:
        """
        检查当前页面是否已登录
        
        Args:
            name: 协程名称，用于日志
            page: Playwright页面对象
            
        Returns:
            True: 已登录
            False: 未登录
            None: 无法确定状态
            
        Raises:
            NetworkError: 当网络问题导致无法检查时
        """
        if "welcome" in page.url:
            self.logger.info(f"--{name}:错误的url:{page.url}")
            await page.reload()

        # 根据页面内容，判断页面是否已登录
        phone_or_id_locator = page.locator("input[placeholder='手机号/证件号码']")
        credit_code_locator = page.locator("input[placeholder='统一社会信用代码']")
        agency_code_locator = page.locator("input[placeholder='代理机构代码']")

        combined_locator = phone_or_id_locator.or_(credit_code_locator).or_(agency_code_locator)

        try:
            # 2024.12.24 change:
            await expect(combined_locator).to_be_visible(timeout=3000)
        except Exception as err:
            self.logger.info(f"--{name}:未发现输入框:{err}")
            try:
                await expect(page.get_by_text("账户管理", exact=True)).to_be_visible()
                self.logger.info(f"--{name}:页面为已登录状态。")
                welcome_tip = await page.get_by_text("您好，").inner_text()
            except Exception as err:
                page_content = await page.content()
                if "检查 Internet 连接" in page_content:
                    self.logger.info(f"--{name}:无法访问登录系统首页，请核实服务器或本地internet网络连接是否正常（10秒后重试）！")
                    return None   # 因网络问题，不能判断
                else:
                    self.logger.info(f"--{name}:判定为页面登录状态时出错：{err}，再次判定")
                    return None   # 因网络问题，不能判断
            else:
                self.logger.info(f"--{name}:当前登录欢迎信息为: {welcome_tip}")
                return True    # 页面已登录
        else:
            self.logger.info(f"--{name}:当前页面内容识别为：用户未登录状态")
            return False  # 页面未登录