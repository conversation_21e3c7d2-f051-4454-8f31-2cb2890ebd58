import re

# Corrected import paths
from src.cpquery_scraper.utils.notice_mapping import NOTICE_MAPPING
from src.cpquery_scraper.utils.logger import get_logger

# 此文件使用日志记录器，因此创建别名会很有帮助
print_info = get_logger(__name__).info


def event_to_request_url(event: str) -> str:
    '''
    根据所爬取的事件名称，确定对应的数据接口url
    根据事件参数，得出request_url
    2023.6.29
    '''

    if event == "申请信息":
        request_url = "**/api/view/gn/sqxx?hHp4Kgam**"
    elif event == "费用信息":
        request_url = "**/api/view/gn/fyxx?hHp4Kgam=**"
    elif event == "发文信息":
        request_url = "**/api/view/gn/fwxx?hHp4Kgam=**"
    elif event == "公告信息":
        request_url = "**/api/view/gn/gbggxx?hHp4Kgam=**"
    elif event == "审查信息":
        request_url = "**/api/view/gn/scxx**"
    elif event == "申请文件":
        request_url = "**/api/view/gn/scxx/sqwj?hHp4Kgam=**"
    elif event == "中间文件":
        request_url = "**/api/view/gn/scxx/zjwj?hHp4Kgam=**"
    elif event == "通知书":
        request_url = "**/api/view/gn/scxx/tzs?hHp4Kgam=**"
    elif event == "复审文件":
        request_url = "**/api/view/gn/scxx/fswj?hHp4Kgam=**"
    elif event == "无效文件":
        request_url = "**/api/view/gn/scxx/wxwj?hHp4Kgam=**"
    else:
        raise ValueError(f"传入函数“event_to_request_url”的事件未定义：{event}")

    return request_url


def check_page_exception_msg_is_crashed(name:str, err_msg:str, func_name:str) -> bool:
    '''
    检查传入的页面异常报错提示信息，是否是页面异常关闭或崩溃的报错，返回True（页面关闭或崩溃）或False（页面正常）
    2023.09.21
    '''
    err_msg = repr(err_msg)
    if "Page" in err_msg and  "crashed" in err_msg:
        print_info(f"--{name}:函数{func_name}的错误信息显示页面已崩溃或关闭")
        return True
    elif "Target" in err_msg and "closed" in err_msg:
        print_info(f"--{name}:函数{func_name}的错误信息显示页面已崩溃或关闭")
        return True
    elif "Page" in err_msg and  "crashed" in err_msg:
        print_info(f"--{name}:函数{func_name}的错误信息显示页面已崩溃或关闭")
        return True
    elif "501" in err_msg and  "退出" in err_msg:
        print_info(f"--{name}:函数{func_name}的错误信息显示页面已崩溃或关闭")
        return True
    else:
        return False


def correct_filename_discrepancy(filename:str, file_code:str) -> str:
    '''校正:web页面通知书文件名与接口返回文件名不一致

    @param filecode: 文件编码
    @return: 修正后的文件名(如无偏差则返回原文件名)
    '''
    def replace_notice_title(filename:str, correct_notice_name=""):
        pattern = r'(\\d{4}-\\d{2}-\\d{2}\\s+).*'
        return re.sub(pattern, r'\\1' + correct_notice_name, filename)

    # 修正通知书名称
    correct_notice_name = NOTICE_MAPPING.get(file_code, "")

    # 修正文件名
    new_filename = replace_notice_title(filename, correct_notice_name)

    return new_filename


def has_attachments(file_fetch_info:dict) -> bool|tuple[list, dict]:
    '''判断通知书是否有附件

    @param file_fetch_info: 接口返回的通知书文件获取信息("fetch_info")
    @return: TZS_attach_list, Jsbg_attach/False
    '''
    if "attachment" not in file_fetch_info or "main_file" not in file_fetch_info:
        return False
    else:
        if file_fetch_info["attachment"]['code'] == 200:
            TZS_attach_list:list = file_fetch_info["attachment"]['data']['listSvTzsFj']
            Jsbg_attach:dict = file_fetch_info["attachment"]['data']['svJsbgJb']

            if len(TZS_attach_list) > 0 :
                TZS_attach_list = TZS_attach_list.copy()
            elif Jsbg_attach is not None:
                Jsbg_attach = Jsbg_attach.copy()
            else:
                return False
        else:
            return False

    return TZS_attach_list, Jsbg_attach
