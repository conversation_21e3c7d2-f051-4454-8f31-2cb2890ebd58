"""
Click模块异常类定义
提供详细的异常信息和错误处理能力
"""
from typing import Optional, Dict, Any


class ClickError(Exception):
    """
    点击操作中的基础异常类
    
    所有点击相关异常的基类，提供统一的错误处理接口
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        """
        初始化点击异常
        
        Args:
            message: 错误消息
            error_code: 错误代码（如API返回的错误码）
            details: 详细错误信息字典
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        """返回格式化的错误信息"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def __repr__(self) -> str:
        """返回详细的异常表示"""
        return f"{self.__class__.__name__}(message='{self.message}', error_code='{self.error_code}')"


class QueryError(ClickError):
    """
    查询操作异常
    
    当主页查询操作失败时抛出
    """
    
    def __init__(self, message: str, an: Optional[str] = None,
                 retry_count: Optional[int] = None, **kwargs):
        """
        初始化查询异常
        
        Args:
            message: 错误消息
            an: 申请号
            retry_count: 已重试次数
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.an = an
        self.retry_count = retry_count
        
        # 添加到详细信息中
        if an:
            self.details['an'] = an
        if retry_count is not None:
            self.details['retry_count'] = retry_count


class PageInitError(ClickError):
    """
    页面初始化异常
    
    当数据页面初始化失败时抛出
    """
    
    def __init__(self, message: str, page_url: Optional[str] = None,
                 expected_url_pattern: Optional[str] = None, **kwargs):
        """
        初始化页面异常
        
        Args:
            message: 错误消息
            page_url: 当前页面URL
            expected_url_pattern: 期望的URL模式
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.page_url = page_url
        self.expected_url_pattern = expected_url_pattern
        
        # 添加到详细信息中
        if page_url:
            self.details['page_url'] = page_url
        if expected_url_pattern:
            self.details['expected_url_pattern'] = expected_url_pattern


class DataExtractionError(ClickError):
    """
    数据提取异常
    
    当从页面提取数据失败时抛出
    """
    
    def __init__(self, message: str, event: Optional[str] = None,
                 an: Optional[str] = None, data_type: Optional[str] = None, **kwargs):
        """
        初始化数据提取异常
        
        Args:
            message: 错误消息
            event: 事件名称
            an: 申请号
            data_type: 数据类型
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.event = event
        self.an = an
        self.data_type = data_type
        
        # 添加到详细信息中
        if event:
            self.details['event'] = event
        if an:
            self.details['an'] = an
        if data_type:
            self.details['data_type'] = data_type


class FileAccessError(ClickError):
    """
    文件访问异常
    
    当访问文件信息失败时抛出
    """
    
    def __init__(self, message: str, file_name: Optional[str] = None,
                 file_type: Optional[str] = None, permission_denied: bool = False, **kwargs):
        """
        初始化文件访问异常
        
        Args:
            message: 错误消息
            file_name: 文件名
            file_type: 文件类型
            permission_denied: 是否权限被拒绝
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.file_name = file_name
        self.file_type = file_type
        self.permission_denied = permission_denied
        
        # 添加到详细信息中
        if file_name:
            self.details['file_name'] = file_name
        if file_type:
            self.details['file_type'] = file_type
        if permission_denied:
            self.details['permission_denied'] = permission_denied


class RouteInterceptError(ClickError):
    """
    路由拦截异常
    
    当设置或处理路由拦截失败时抛出
    """
    
    def __init__(self, message: str, route_pattern: Optional[str] = None,
                 intercept_type: Optional[str] = None, **kwargs):
        """
        初始化路由拦截异常
        
        Args:
            message: 错误消息
            route_pattern: 路由模式
            intercept_type: 拦截类型
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.route_pattern = route_pattern
        self.intercept_type = intercept_type
        
        # 添加到详细信息中
        if route_pattern:
            self.details['route_pattern'] = route_pattern
        if intercept_type:
            self.details['intercept_type'] = intercept_type


class ResponseTimeoutError(ClickError):
    """
    响应超时异常
    
    当等待页面响应超时时抛出
    """
    
    def __init__(self, message: str, timeout_seconds: Optional[float] = None,
                 operation: Optional[str] = None, **kwargs):
        """
        初始化响应超时异常
        
        Args:
            message: 错误消息
            timeout_seconds: 超时时间（秒）
            operation: 超时的操作名称
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.timeout_seconds = timeout_seconds
        self.operation = operation
        
        # 添加到详细信息中
        if timeout_seconds:
            self.details['timeout_seconds'] = timeout_seconds
        if operation:
            self.details['operation'] = operation


class PageCrashedError(ClickError):
    """
    页面崩溃异常
    
    当浏览器页面崩溃或上下文关闭时抛出
    """
    
    def __init__(self, message: str, page_url: Optional[str] = None,
                 crash_reason: Optional[str] = None, **kwargs):
        """
        初始化页面崩溃异常
        
        Args:
            message: 错误消息
            page_url: 崩溃页面的URL
            crash_reason: 崩溃原因
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.page_url = page_url
        self.crash_reason = crash_reason
        
        # 添加到详细信息中
        if page_url:
            self.details['page_url'] = page_url
        if crash_reason:
            self.details['crash_reason'] = crash_reason


class RetryExhaustedError(ClickError):
    """
    重试次数耗尽异常
    
    当操作重试次数达到上限时抛出
    """
    
    def __init__(self, message: str, max_retries: Optional[int] = None,
                 operation: Optional[str] = None, **kwargs):
        """
        初始化重试耗尽异常
        
        Args:
            message: 错误消息
            max_retries: 最大重试次数
            operation: 操作名称
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.max_retries = max_retries
        self.operation = operation
        
        # 添加到详细信息中
        if max_retries:
            self.details['max_retries'] = max_retries
        if operation:
            self.details['operation'] = operation


# 为了向后兼容，保留原有的简单异常类
class ClickTimeoutError(ResponseTimeoutError):
    """点击超时异常（向后兼容）"""
    pass


class DataValidationError(DataExtractionError):
    """数据验证异常（向后兼容）"""
    pass
