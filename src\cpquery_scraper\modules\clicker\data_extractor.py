"""
数据提取管理器
负责从页面提取各种类型的数据
"""
import asyncio
from typing import Optional, Union
from playwright.async_api import Page, expect

from .exceptions import (
    DataExtractionError, RouteInterceptError,
    PageCrashedError, RetryExhaustedError
)
from src.cpquery_scraper.utils.helpers import (
    event_to_request_url, check_page_exception_msg_is_crashed
)


class DataExtractor:
    """数据提取管理器"""
    
    def __init__(self, config, logger):
        """
        初始化数据提取管理器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
    
    async def extract_application_data(
        self, 
        name: str, 
        data_page: Page, 
        an: str, 
        fut: Optional[asyncio.Future] = None, 
        retry_times: int = 0
    ) -> Optional[asyncio.Future[dict[str, Union[int, str, None]]]]:
        """
        从初始化好的数据页面查询申请信息
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            an: 申请号
            fut: 异步Future对象
            retry_times: 重试次数
            
        Returns:
            Future对象或None
            
        Raises:
            RetryExhaustedError: 重试次数耗尽时抛出
            RouteInterceptError: 路由拦截失败时抛出
            DataExtractionError: 数据提取失败时抛出
        """
        # 重试次数检测
        if retry_times >= self.config.MAX_DATA_PAGE_CLICK_RETRY_TIMES + 5:
            error_msg = f"{an}申请信息获取{self.config.MAX_DATA_PAGE_INIT_RETRY_TIMES}次失败，放弃请求"
            self.logger.error(f"--{name}: {error_msg}")
            
            if fut:
                fut.set_result({
                    "code": 504,
                    "data": None,
                    "msg": error_msg,
                })
                return fut
            else:
                raise RetryExhaustedError(
                    error_msg,
                    error_code="MAX_RETRIES_EXCEEDED",
                    max_retries=self.config.MAX_DATA_PAGE_CLICK_RETRY_TIMES + 5,
                    operation="extract_application_data"
                )

        # 设置页面路由，拦截、修改"申请信息"请求
        rsq_url = None

        async def handle_route(route):
            nonlocal rsq_url
            rsq_url = route.request.url
            if "zhuanlisqh" in route.request.post_data:
                await route.continue_(post_data={"zhuanlisqh": an})
            else:
                raise RouteInterceptError(
                    f"请求所含数据错误：route.request={route.request}",
                    error_code="INVALID_REQUEST_DATA",
                    route_pattern="**/api/view/gn/sqxx?hHp4Kgam=**"
                )

        try:
            await data_page.route("**/api/view/gn/sqxx?hHp4Kgam=**", handle_route)
        except Exception as e:
            self.logger.error(f"--{name}: 设置路由拦截失败: {e}")
            raise RouteInterceptError(
                f"设置路由拦截失败: {str(e)}",
                error_code="ROUTE_SETUP_FAILED",
                route_pattern="**/api/view/gn/sqxx?hHp4Kgam=**"
            )

        # 捕获信息页的响应信息（接口数据）
        try:
            async with data_page.expect_response(
                lambda response: response.url == rsq_url,
                timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_ASYNC,
            ) as sqxx:
                await data_page.get_by_role("treeitem").get_by_text("申请信息").click()
            sqxx_r = await sqxx.value
            sqxx = await sqxx_r.json()

            if fut:
                fut.set_result(sqxx)
                return fut
            else:
                return sqxx
                
        except Exception as err:
            self.logger.warning(f"--{name}: 获取申请信息数据过程中出现错误: {err}")
            
            # 暂留代码，为了捕捉一个未知错误
            if "Expecting value:" in repr(err):
                try:
                    error_body = await sqxx_r.body()  # type: ignore
                    self.logger.error(f"--{name}: JSON解析错误, 响应体: {error_body}")
                except Exception:
                    pass

            # 查看页面是否登录失效
            if "/detail/index?zhuanlisqh=" not in data_page.url:
                self.logger.warning(f"--{name}: 当前页面url非数据页，需要重新初始化")
                raise DataExtractionError(
                    "当前页面非数据页，需要重新初始化",
                    error_code="PAGE_NOT_DATA_PAGE",
                    event="申请信息",
                    an=an,
                    details={"current_url": data_page.url}
                )
            
            # 检查页面是否崩溃
            elif "Target" in repr(err) and "closed" in repr(err):
                raise PageCrashedError(
                    "浏览器或页面已关闭",
                    error_code="PAGE_CRASHED",
                    crash_reason=str(err)
                )
            
            else:
                retry_times += 1
                self.logger.info(f"--{name}: 当前页面没有被重定向，但无响应，重试{an}：获取申请信息 第{retry_times}次")
                return await self.extract_application_data(name, data_page, an, fut, retry_times)

    async def extract_event_data(
        self,
        name: str,
        data_page: Page,
        event: str,
        an: str,
        fut: Optional[asyncio.Future] = None,
        retry_times: int = 0,
    ) -> Optional[asyncio.Future[dict[str, Union[int, str, None]]]]:
        """
        异步模式：同时点击、获取各个事件的按钮，以及提取数据
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            event: 事件名称
            an: 申请号
            fut: Future对象
            retry_times: 重试次数
            
        Returns:
            Future对象或None
            
        Raises:
            RetryExhaustedError: 重试次数耗尽时抛出
            DataExtractionError: 数据提取失败时抛出
        """
        # 重试次数检测
        if retry_times >= self.config.MAX_DATA_PAGE_CLICK_RETRY_TIMES:
            error_msg = f"{an} {event} 获取{self.config.MAX_DATA_PAGE_INIT_RETRY_TIMES}次失败，放弃请求"
            self.logger.error(f"--{name}: {error_msg}")
            
            # 如果是申请信息，尝试通过查询页补充
            if event == "申请信息":
                self.logger.info(f"--{name}: 通过查询页补充{an}的申请信息")
                # 这里需要调用查询管理器，但为了避免循环依赖，返回特殊错误码
                result = {
                    "code": 201,  # 特殊码，表示需要通过查询页补充
                    "data": None,
                    "msg": "需要通过查询页补充申请信息",
                }
            else:
                result = {
                    "code": 504,
                    "data": None,
                    "msg": error_msg,
                }
            
            if fut:
                fut.set_result(result)
                return fut
            else:
                raise RetryExhaustedError(
                    error_msg,
                    error_code="MAX_RETRIES_EXCEEDED",
                    max_retries=self.config.MAX_DATA_PAGE_CLICK_RETRY_TIMES,
                    operation=f"extract_event_data_{event}"
                )

        # 设置页面路由，拦截、修改请求
        rsq_url = None

        async def handle_route(route):
            nonlocal rsq_url
            rsq_url = route.request.url
            if "zhuanlisqh" in route.request.post_data:
                await route.continue_(post_data={"zhuanlisqh": an})
            else:
                raise RouteInterceptError(
                    f"请求所含数据错误：route.request={route.request}",
                    error_code="INVALID_REQUEST_DATA",
                    route_pattern=event_to_request_url(event)
                )

        try:
            await data_page.route(event_to_request_url(event), handle_route)
        except Exception as e:
            self.logger.error(f"--{name}: 因浏览器意外关闭（{e}），无法设置route，应重启任务")
            raise PageCrashedError(
                "浏览器意外关闭，无法设置路由",
                error_code="BROWSER_CRASHED",
                crash_reason=str(e)
            )

        # 捕获data_page页面响应信息（接口数据）
        try:
            async with data_page.expect_response(
                lambda response: response.url == rsq_url,
                timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_ASYNC,
            ) as event_data:
                
                # 页面刷新动作（如果需要）
                if retry_times == 0 and event in ("申请信息",):
                    await self._refresh_page(data_page, event, name)
                elif retry_times > 0:
                    await self._refresh_page(data_page, event, name, retry=True)
                
                # 点击动作
                if event != "申请信息":
                    await data_page.get_by_role("treeitem").get_by_text(event).click()

            event_data_dict = await event_data.value
            event_data_result = await event_data_dict.json()
            
            # 移除路由拦截
            await data_page.unroute(event_to_request_url(event), handle_route)
            
        except Exception as err:
            self.logger.error(f"--{name}: 获取 {an} ：{event} 时发生错误：{err}")
            return await self._handle_extraction_error(name, data_page, event, an, fut, retry_times, err)
        
        # 验证数据完整性
        if self._is_data_empty(event, event_data_result):
            self.logger.warning(f"--{name}: {event} 数据为空，重试:{event_data_result}")
            await asyncio.sleep(self.config.SLEEP_TIME_SEC_ASYNC)
            retry_times += 1
            return await self.extract_event_data(name, data_page, event, an, fut, retry_times)
        
        if fut:
            fut.set_result(event_data_result)
            return fut
        else:
            return event_data_result

    async def _refresh_page(self, data_page: Page, event: str, name: str, retry: bool = False) -> Page:
        """
        刷新当前页面，适应网站规则（获取新的fresh页面），提取数据
        
        Args:
            data_page: 数据页面对象
            event: 事件名称
            name: 任务名称
            retry: 是否为重试刷新
            
        Returns:
            Page: 刷新后的页面对象
            
        Raises:
            PageCrashedError: 页面崩溃时抛出
        """
        if not retry:
            # 页面首次刷新（只刷新"申请信息"数据页面）
            if event in ("申请信息",):
                await data_page.reload(
                    wait_until="networkidle", 
                    timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_REFRESH
                )
                await expect(data_page.get_by_role("treeitem").get_by_text(event)).to_be_visible()
        else:
            # 重试刷新（每个数据页面都刷新）
            i = 0
            while True:
                if i > self.config.MAX_DATA_PAGE_REFRESH_RETRY_TIMES:
                    self.logger.error(f"--{name}: 重试页面刷新{self.config.MAX_DATA_PAGE_REFRESH_RETRY_TIMES}次仍不成功，退出")
                    raise PageCrashedError(
                        f"页面刷新重试{self.config.MAX_DATA_PAGE_REFRESH_RETRY_TIMES}次后仍失败",
                        error_code="PAGE_REFRESH_MAX_RETRIES",
                        details={"retry_count": i, "event": event}
                    )
                
                self.logger.info(f"--{name}: 页面刷新第 {i} 次")
                try:
                    await data_page.reload(
                        wait_until="networkidle", 
                        timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_REFRESH
                    )
                    await expect(data_page.get_by_role("treeitem").get_by_text(event)).to_be_visible()
                except Exception as err:
                    if check_page_exception_msg_is_crashed(name, repr(err), "data_page_refresh"):
                        self.logger.error(f"--{name}: 页面刷新时发现页面崩溃，终止刷新")
                        raise PageCrashedError(
                            "页面刷新时发现页面崩溃",
                            error_code="PAGE_CRASHED_DURING_REFRESH",
                            crash_reason=str(err)
                        )
                    else:
                        self.logger.warning(f"--{name}: 页面刷新出错：{err}")
                        await asyncio.sleep(2)
                        i += 1
                        continue
                else:
                    break

        return data_page

    def _is_data_empty(self, event: str, event_data: dict) -> bool:
        """
        检查事件数据是否为空
        
        Args:
            event: 事件名称
            event_data: 事件数据
            
        Returns:
            bool: 数据是否为空
        """
        if event_data.get("code") != 200:
            return False
            
        data = event_data.get("data", {})
        
        if event == "申请信息":
            return not data.get("shenqingren", {}).get("isShow", True)
        elif event == "费用信息":
            return not data.get("yijiaofei", {}).get("isShow", True)
        elif event == "发文信息":
            return (not data.get("tongzhishufw", {}).get("isShow", True) and 
                   not data.get("zhuanlizsfw", {}).get("isShow", True))
        elif event == "公告信息":
            return not data.get("faminggbsqgg", {}).get("isShow", True)
        
        return False

    async def _handle_extraction_error(
        self, name: str, data_page: Page, event: str, an: str, 
        fut: Optional[asyncio.Future], retry_times: int, err: Exception
    ) -> Optional[asyncio.Future]:
        """
        处理数据提取错误
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            event: 事件名称
            an: 申请号
            fut: Future对象
            retry_times: 重试次数
            err: 异常对象
            
        Returns:
            Future对象或None
        """
        # 检查页面URL是否正确
        if "/detail/index?zhuanlisqh=" not in data_page.url:
            self.logger.warning(f"--{name}: 当前页面非数据页，需要重新初始化")
            raise DataExtractionError(
                "当前页面非数据页，需要重新初始化",
                error_code="PAGE_NOT_DATA_PAGE",
                event=event,
                an=an,
                details={"current_url": data_page.url}
            )
        
        # 检查特定错误类型
        if "Expecting value:" in repr(err):
            self.logger.warning(f"--{name}: 405错误（WAF，可能是阿里云盾），请求被拦截:{repr(err)}")
            retry_times += 1
            self.logger.info(f"--{name}: 请求被拦截后，重试{an}：获取 {event} 第{retry_times}次")
            return await self.extract_event_data(name, data_page, event, an, fut, retry_times)
        
        elif "Target" in repr(err) and "closed" in repr(err):
            raise PageCrashedError(
                "浏览器或页面已关闭",
                error_code="PAGE_CRASHED",
                crash_reason=str(err)
            )
        
        elif "Timeout" in repr(err):
            retry_times += 1
            self.logger.info(f"--{name}: 超时原因未获取到数据:{err}，重试{an}：获取 {event} 第{retry_times}次")
            return await self.extract_event_data(name, data_page, event, an, fut, retry_times)
        
        else:
            retry_times += 1
            self.logger.info(f"--{name}: 其它原因未获取到数据，重试{an}：获取 {event} 第{retry_times}次")
            return await self.extract_event_data(name, data_page, event, an, fut, retry_times)
