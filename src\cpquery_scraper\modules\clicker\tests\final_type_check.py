#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终类型注解检查脚本
"""
import ast
import os

def check_all_files():
    """检查所有文件的类型注解"""
    click_dir = os.path.dirname(os.path.dirname(__file__))
    
    files_to_check = [
        "__init__.py",
        "query_manager.py",
        "data_extractor.py",
        "file_handler.py",
        "sync_click_manager.py",
        "exceptions.py"
    ]
    
    print("最终类型注解检查:")
    print("=" * 50)
    
    all_good = True
    
    for filename in files_to_check:
        file_path = os.path.join(click_dir, filename)
        
        if not os.path.exists(file_path):
            print(f"❌ {filename}: 文件不存在")
            all_good = False
            continue
        
        # 语法检查
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            ast.parse(content)
            print(f"✅ {filename}: 语法正确")
            
            # 检查潜在的类型问题
            issues = []
            
            # 检查是否有未处理的Optional返回值
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'get_page_logined' in line and '=' in line and 'if' not in line:
                    # 检查是否有None检查
                    next_lines = lines[i:i+5] if i < len(lines)-5 else lines[i:]
                    has_none_check = any('is None' in next_line for next_line in next_lines)
                    if not has_none_check:
                        issues.append(f"第{i}行: get_page_logined调用可能需要None检查")
                
                # 检查函数返回类型
                if 'async def' in line and '-> Page' in line:
                    # 这是一个返回Page的async函数，检查是否所有路径都有返回值
                    func_name = line.split('def ')[1].split('(')[0].strip()
                    issues.append(f"第{i}行: 函数{func_name}返回Page类型，请确保所有代码路径都有返回值")
            
            if issues:
                print(f"⚠️  {filename}: 发现 {len(issues)} 个潜在问题")
                for issue in issues:
                    print(f"    - {issue}")
            
        except SyntaxError as e:
            print(f"❌ {filename}: 语法错误 - {e}")
            all_good = False
        except Exception as e:
            print(f"❌ {filename}: 检查失败 - {e}")
            all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 所有文件检查通过！")
    else:
        print("⚠️  部分文件存在问题")
    
    return all_good

if __name__ == "__main__":
    check_all_files()
