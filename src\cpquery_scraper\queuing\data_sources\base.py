"""
数据源抽象基类

定义了数据源的统一接口，所有具体的数据源实现都应该继承此基类。

Author: wwind
Date: 2025.07.08
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager
import logging

from ..models import TaskItem, DataSourceConfig, DataSourceType
from ..exceptions import DataSourceError


class BaseDataSource(ABC):
    """
    数据源抽象基类
    
    定义了所有数据源必须实现的接口方法
    """
    
    def __init__(self, config: DataSourceConfig, logger: Optional[logging.Logger] = None):
        """
        初始化数据源
        
        Args:
            config: 数据源配置
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self._is_connected = False
        self._connection = None
        
        # 验证配置
        if not config.validate():
            raise DataSourceError(
                "Invalid data source configuration",
                source_type=config.source_type.value,
                error_code="INVALID_CONFIG"
            )
    
    @property
    def source_type(self) -> DataSourceType:
        """获取数据源类型"""
        return self.config.source_type
    
    @property
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._is_connected
    
    @abstractmethod
    async def connect(self) -> None:
        """
        连接到数据源
        
        Raises:
            DataSourceError: 连接失败时抛出
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """
        断开数据源连接
        
        Raises:
            DataSourceError: 断开连接失败时抛出
        """
        pass
    
    @abstractmethod
    async def fetch_tasks(self, limit: Optional[int] = None) -> List[TaskItem]:
        """
        从数据源获取任务列表
        
        Args:
            limit: 限制返回的任务数量
            
        Returns:
            任务项列表
            
        Raises:
            DataSourceError: 获取任务失败时抛出
        """
        pass
    
    @abstractmethod
    async def update_task_status(self, task_id: str, status: int) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            
        Returns:
            是否更新成功
            
        Raises:
            DataSourceError: 更新失败时抛出
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            数据源是否健康
        """
        pass
    
    async def batch_update_task_status(self, task_updates: List[Dict[str, Any]]) -> int:
        """
        批量更新任务状态
        
        Args:
            task_updates: 任务更新列表，每个元素包含task_id和status
            
        Returns:
            成功更新的任务数量
            
        Raises:
            DataSourceError: 批量更新失败时抛出
        """
        success_count = 0
        for update in task_updates:
            try:
                task_id = update.get('task_id')
                status = update.get('status')
                if task_id and status is not None:
                    if await self.update_task_status(task_id, status):
                        success_count += 1
            except Exception as e:
                self.logger.warning(f"Failed to update task {update.get('task_id')}: {e}")
                continue
        
        return success_count
    
    async def get_task_count(self, status: Optional[int] = None) -> int:
        """
        获取任务数量
        
        Args:
            status: 任务状态过滤条件，None表示获取所有任务数量
            
        Returns:
            任务数量
            
        Raises:
            DataSourceError: 获取任务数量失败时抛出
        """
        # 默认实现：通过获取任务列表来计算数量
        # 子类可以重写此方法以提供更高效的实现
        try:
            tasks = await self.fetch_tasks()
            if status is None:
                return len(tasks)
            else:
                return len([task for task in tasks if task.status.value == status])
        except Exception as e:
            raise DataSourceError(
                f"Failed to get task count: {e}",
                source_type=self.source_type.value,
                error_code="FETCH_FAILED"
            )
    
    @asynccontextmanager
    async def connection_context(self):
        """
        连接上下文管理器
        
        确保在使用数据源时正确管理连接的生命周期
        """
        try:
            if not self.is_connected:
                await self.connect()
            yield self
        except Exception as e:
            self.logger.error(f"Error in connection context: {e}")
            raise
        finally:
            # 注意：这里不自动断开连接，因为连接可能被其他地方使用
            # 连接的生命周期应该由调用者管理
            pass
    
    async def validate_connection(self) -> bool:
        """
        验证连接是否有效
        
        Returns:
            连接是否有效
        """
        try:
            return await self.health_check()
        except Exception as e:
            self.logger.warning(f"Connection validation failed: {e}")
            return False
    
    def _log_operation(self, operation: str, details: Optional[Dict[str, Any]] = None) -> None:
        """
        记录操作日志
        
        Args:
            operation: 操作名称
            details: 操作详细信息
        """
        log_msg = f"[{self.source_type.value}] {operation}"
        if details:
            log_msg += f" - {details}"
        self.logger.info(log_msg)
    
    def _handle_error(self, error: Exception, operation: str, 
                     details: Optional[Dict[str, Any]] = None) -> DataSourceError:
        """
        处理错误并转换为数据源异常
        
        Args:
            error: 原始异常
            operation: 操作名称
            details: 错误详细信息
            
        Returns:
            数据源异常
        """
        error_msg = f"Failed to {operation}: {error}"
        self.logger.error(error_msg)
        
        return DataSourceError(
            error_msg,
            source_type=self.source_type.value,
            source_config=self.config.connection_params,
            details=details or {}
        )
    
    def __str__(self) -> str:
        """返回数据源的字符串表示"""
        return f"{self.__class__.__name__}({self.source_type.value})"
    
    def __repr__(self) -> str:
        """返回数据源的详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"source_type={self.source_type.value}, "
                f"connected={self.is_connected})")


class DataSourceFactory:
    """
    数据源工厂类
    
    用于创建不同类型的数据源实例
    """
    
    _registry: Dict[DataSourceType, type] = {}
    
    @classmethod
    def register(cls, source_type: DataSourceType, source_class: type) -> None:
        """
        注册数据源类型
        
        Args:
            source_type: 数据源类型
            source_class: 数据源类
        """
        cls._registry[source_type] = source_class
    
    @classmethod
    def create(cls, config: DataSourceConfig, 
               logger: Optional[logging.Logger] = None) -> BaseDataSource:
        """
        创建数据源实例
        
        Args:
            config: 数据源配置
            logger: 日志记录器
            
        Returns:
            数据源实例
            
        Raises:
            DataSourceError: 创建失败时抛出
        """
        source_class = cls._registry.get(config.source_type)
        if not source_class:
            raise DataSourceError(
                f"Unsupported data source type: {config.source_type.value}",
                source_type=config.source_type.value,
                error_code="UNSUPPORTED_TYPE"
            )
        
        return source_class(config, logger)
    
    @classmethod
    def get_supported_types(cls) -> List[DataSourceType]:
        """
        获取支持的数据源类型列表
        
        Returns:
            支持的数据源类型列表
        """
        return list(cls._registry.keys())
