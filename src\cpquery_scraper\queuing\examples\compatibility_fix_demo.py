"""
兼容性层修复演示

展示修复后的兼容性层行为，解决重复处理问题
"""

import asyncio
import logging
from queue_scheduling.compatibility import setup_task_queue_from_source

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

class MockConfig:
    """模拟配置类"""
    TASK_QUEUE_MAX_SIZE = 1000
    TASK_QUEUE_FROM_REDIS_SIZE = 100
    TASK_MONITOR_INTERVAL = 30
    FLAG_SPIDER_RUNNING = True
    
    # Redis配置（需要根据实际情况修改）
    REDIS_PARAM = {
        'host': 'localhost',
        'port': 6379,
        'db': 0
    }

async def demo_fixed_compatibility():
    """演示修复后的兼容性层"""
    print("=== 兼容性层修复演示 ===")
    print("修复内容：")
    print("1. 停止新系统的自动监控，避免重复补充")
    print("2. 兼容性层直接从数据源获取任务")
    print("3. 避免任务重复处理和队列大小不一致")
    print()
    
    # 创建原有系统的任务队列
    task_queue = asyncio.Queue(maxsize=1000)
    
    # 模拟配置
    config = MockConfig()
    
    try:
        print("启动兼容性层...")
        
        # 注意：这里需要有可用的Redis连接才能真正运行
        # 在实际环境中取消注释下面的代码
        """
        await setup_task_queue_from_source(
            task_queue=task_queue,
            source='redis',
            config=config
        )
        """
        
        print("兼容性层启动成功")
        print("\n预期的日志输出：")
        print("--兼容性层：已停止新系统的自动监控，由兼容性层接管任务补充")
        print("--兼容性层：检测到队列大小(0)低于阈值(100)，开始补充任务")
        print("--兼容性层：成功补充 100 个任务到队列，当前队列大小: 100")
        print("--兼容性层：30秒周期监控，当前队列大小: 100")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        print("注意：需要可用的Redis连接才能完整运行")

def show_fix_comparison():
    """显示修复前后的对比"""
    print("\n=== 修复前后对比 ===")
    
    print("\n修复前的问题：")
    print("1. 新系统监控：检测到队列为空 → 自动补充200个任务到内部队列")
    print("2. 兼容性层：从内部队列获取200个任务 → 转移到原有队列")
    print("3. 结果：任务重复处理，队列大小不一致（内部200 + 原有200 = 总计400）")
    
    print("\n修复后的行为：")
    print("1. 兼容性层：停止新系统的自动监控")
    print("2. 兼容性层：直接从数据源获取任务")
    print("3. 兼容性层：只负责任务转换，不重复获取")
    print("4. 结果：避免重复处理，队列大小一致")
    
    print("\n关键修复点：")
    print("✓ 添加了 stop_monitoring() 方法停止新系统监控")
    print("✓ 兼容性层直接调用 data_source.fetch_tasks()")
    print("✓ 使用原有系统的阈值逻辑进行补充判断")
    print("✓ 避免了双重队列管理的冲突")

def show_expected_logs():
    """显示预期的日志输出"""
    print("\n=== 预期的日志输出 ===")
    
    print("\n启动阶段：")
    print("--兼容性层：已停止新系统的自动监控，由兼容性层接管任务补充")
    
    print("\n监控阶段：")
    print("--兼容性层：检测到队列大小(0)低于阈值(100)，开始补充任务")
    print("[redis] Fetched tasks from Redis - {'requested': 100, 'actual': 100, 'time': '2.5s'}")
    print("--兼容性层：成功补充 100 个任务到队列，当前队列大小: 100")
    
    print("\n稳定运行阶段：")
    print("--兼容性层：30秒周期监控，当前队列大小: 95")
    print("--兼容性层：检测到队列大小(45)低于阈值(100)，开始补充任务")
    print("--兼容性层：成功补充 100 个任务到队列，当前队列大小: 145")
    
    print("\n对比修复前的混乱日志：")
    print("❌ 修复前：")
    print("   --任务队列：补充完成，补充前=0, 补充后=200, 实际添加=200个任务")
    print("   --任务队列：已添加 200 个任务到队列 (批量大小: 50, 当前队列大小: 394)")
    print("✓ 修复后：")
    print("   --兼容性层：成功补充 100 个任务到队列，当前队列大小: 100")

async def main():
    """主函数"""
    print("兼容性层重复处理问题修复演示")
    print("=" * 50)
    
    # 显示修复对比
    show_fix_comparison()
    
    # 显示预期日志
    show_expected_logs()
    
    print("\n" + "=" * 50)
    print("注意：实际运行需要Redis服务器连接")
    
    # 如果需要运行实际演示，取消下面的注释
    # await demo_fixed_compatibility()

if __name__ == "__main__":
    asyncio.run(main())
