"""
Legacy compatibility shim for corutine_main.py

This module provides backward compatibility for code that imports from corutine_main.py.
It re-exports the main functionality from the refactored application structure.

The original corutine_main.py was the main orchestrator that:
1. Initialized queues and browser
2. Created worker coroutines 
3. Started background tasks (gen_task_queue, ResultQueueProcessor)
4. Managed cleanup and shutdown

This shim maintains the same interface while delegating to the refactored App class.
"""

import asyncio
import time
import os
from src.cpquery_scraper.app import App
from src.cpquery_scraper.config import config

# Re-export main function for compatibility
async def main():
    """
    Main orchestrator function - compatible with original corutine_main.py
    
    This function maintains the same behavior as the original:
    - Initializes queues and browser
    - Creates worker coroutines
    - Starts background tasks
    - Handles cleanup
    """
    app = App()
    await app.run()

# Legacy compatibility - allow direct execution
if __name__ == "__main__":
    # Set NODE_OPTIONS for Playwright memory management
    os.environ['NODE_OPTIONS'] = '--max-old-space-size=2048'
    
    if config.RUN_ONCE:
        asyncio.run(main())
    else:
        while True:
            asyncio.run(main())
            print("------------10秒后，换用户登录，再次开启任务------------")
            time.sleep(10)
