"""
任务处理器

提供统一的任务处理接口和工厂方法。

Author: wwind
Date: 2025.07.08
"""

# import asyncio
from typing import Optional, Dict, Any, Callable
import logging

from ..models import DataSourceConfig, QueueConfig, DataSourceType, QueueType
from ..data_sources import DataSourceFactory
from ..queue_managers import TaskQueueManager, ResultQueueManager
from ..exceptions import ConfigurationError, TaskSchedulingError


class TaskProcessor:
    """
    任务处理器
    
    整合数据源和队列管理器，提供统一的任务处理接口
    """
    
    def __init__(self, 
                 data_source_config: DataSourceConfig,
                 task_queue_config: QueueConfig,
                 result_queue_config: QueueConfig,
                 result_processor: Optional[Callable] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化任务处理器
        
        Args:
            data_source_config: 数据源配置
            task_queue_config: 任务队列配置
            result_queue_config: 结果队列配置
            result_processor: 结果处理器函数
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        
        # 验证配置
        if task_queue_config.queue_type != QueueType.TASK_QUEUE:
            raise ConfigurationError(
                "Invalid queue type for task queue config",
                config_key="task_queue_config.queue_type",
                config_value=task_queue_config.queue_type.value
            )
        
        if result_queue_config.queue_type != QueueType.RESULT_QUEUE:
            raise ConfigurationError(
                "Invalid queue type for result queue config",
                config_key="result_queue_config.queue_type",
                config_value=result_queue_config.queue_type.value
            )
        
        # 创建数据源
        self.data_source = DataSourceFactory.create(data_source_config, logger)
        
        # 创建队列管理器
        self.task_queue_manager = TaskQueueManager(
            data_source=self.data_source,
            queue_config=task_queue_config,
            logger=logger
        )
        
        self.result_queue_manager = ResultQueueManager(
            queue_config=result_queue_config,
            result_processor=result_processor,
            logger=logger
        )
        
        self._is_running = False
        
        self.logger.info(f"TaskProcessor initialized with {data_source_config.source_type.value} data source")
    
    @property
    def is_running(self) -> bool:
        """检查处理器是否正在运行"""
        return self._is_running
    
    async def start(self) -> None:
        """启动任务处理器"""
        if self._is_running:
            self.logger.warning("TaskProcessor is already running")
            return
        
        try:
            # 启动队列管理器
            await self.task_queue_manager.start()
            await self.result_queue_manager.start()
            
            self._is_running = True
            self.logger.info("TaskProcessor started successfully")
            
        except Exception as e:
            self._is_running = False
            # 清理已启动的组件
            try:
                await self.task_queue_manager.stop()
                await self.result_queue_manager.stop()
            except Exception:
                pass
            
            raise TaskSchedulingError(
                f"Failed to start TaskProcessor: {e}",
                error_code="START_FAILED"
            )
    
    async def stop(self) -> None:
        """停止任务处理器"""
        if not self._is_running:
            return
        
        try:
            self._is_running = False
            
            # 停止队列管理器
            await self.task_queue_manager.stop()
            await self.result_queue_manager.stop()
            
            self.logger.info("TaskProcessor stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping TaskProcessor: {e}")
    
    async def get_task(self, timeout: Optional[float] = None):
        """
        获取一个任务
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            任务项
        """
        if not self._is_running:
            raise TaskSchedulingError(
                "TaskProcessor is not running",
                error_code="NOT_RUNNING"
            )
        
        return await self.task_queue_manager.get_task(timeout=timeout)
    
    async def submit_result(self, result_data: Any) -> None:
        """
        提交处理结果
        
        Args:
            result_data: 结果数据
        """
        if not self._is_running:
            raise TaskSchedulingError(
                "TaskProcessor is not running",
                error_code="NOT_RUNNING"
            )
        
        await self.result_queue_manager.put_result(result_data)
    
    def submit_result_nowait(self, result_data: Any) -> None:
        """
        非阻塞方式提交处理结果
        
        Args:
            result_data: 结果数据
        """
        if not self._is_running:
            raise TaskSchedulingError(
                "TaskProcessor is not running",
                error_code="NOT_RUNNING"
            )
        
        self.result_queue_manager.put_result_nowait(result_data)
    
    async def task_done(self, task, success: bool = True) -> None:
        """
        标记任务完成
        
        Args:
            task: 完成的任务
            success: 是否成功完成
        """
        if not self._is_running:
            return
        
        await self.task_queue_manager.task_done(task, success)
    
    async def get_status(self) -> Dict[str, Any]:
        """
        获取处理器状态
        
        Returns:
            状态信息字典
        """
        task_queue_info = await self.task_queue_manager.get_queue_info()
        result_queue_info = await self.result_queue_manager.get_queue_info()
        
        return {
            'is_running': self.is_running,
            'data_source_type': self.data_source.source_type.value,
            'task_queue': task_queue_info,
            'result_queue': result_queue_info
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()
    
    def __str__(self) -> str:
        """返回处理器的字符串表示"""
        return f"TaskProcessor({self.data_source.source_type.value})"


class TaskProcessorFactory:
    """
    任务处理器工厂类
    
    提供便捷的方法创建不同配置的任务处理器
    """
    
    @staticmethod
    def create_mysql_processor(
        mysql_params: Dict[str, Any],
        task_queue_max_size: int = 5000,
        result_queue_max_size: int = 10000,
        monitor_interval: int = 100,
        logger: Optional[logging.Logger] = None
    ) -> TaskProcessor:
        """
        创建MySQL数据源的任务处理器
        
        Args:
            mysql_params: MySQL连接参数
            task_queue_max_size: 任务队列最大大小
            result_queue_max_size: 结果队列最大大小
            monitor_interval: 监控间隔（秒）
            logger: 日志记录器
            
        Returns:
            任务处理器实例
        """
        data_source_config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params=mysql_params
        )
        
        task_queue_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=task_queue_max_size,
            monitor_interval=monitor_interval,
            low_threshold=500,
            batch_size=150  # 优化：从默认100增加到150
        )
        
        result_queue_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=result_queue_max_size,
            monitor_interval=monitor_interval,
            low_threshold=0
        )
        
        return TaskProcessor(
            data_source_config=data_source_config,
            task_queue_config=task_queue_config,
            result_queue_config=result_queue_config,
            logger=logger
        )
    
    @staticmethod
    def create_excel_processor(
        excel_dir: str,
        task_queue_max_size: int = 5000,
        result_queue_max_size: int = 10000,
        logger: Optional[logging.Logger] = None
    ) -> TaskProcessor:
        """
        创建Excel数据源的任务处理器
        
        Args:
            excel_dir: Excel文件目录
            task_queue_max_size: 任务队列最大大小
            result_queue_max_size: 结果队列最大大小
            logger: 日志记录器
            
        Returns:
            任务处理器实例
        """
        data_source_config = DataSourceConfig(
            source_type=DataSourceType.EXCEL,
            connection_params={'excel_dir': excel_dir}
        )
        
        task_queue_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=task_queue_max_size,
            monitor_interval=300,  # 优化：Excel监控间隔增加到300秒
            low_threshold=0,  # Excel是一次性读取，不需要补充
            batch_size=500  # 优化：Excel可以使用更大的批处理
        )
        
        result_queue_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=result_queue_max_size,
            monitor_interval=60,
            low_threshold=0
        )
        
        return TaskProcessor(
            data_source_config=data_source_config,
            task_queue_config=task_queue_config,
            result_queue_config=result_queue_config,
            logger=logger
        )
    
    @staticmethod
    def create_redis_processor(
        redis_params: Dict[str, Any],
        task_queue_max_size: int = 5000,
        result_queue_max_size: int = 10000,
        monitor_interval: int = 120,  # 优化：从60秒增加到120秒
        logger: Optional[logging.Logger] = None
    ) -> TaskProcessor:
        """
        创建Redis数据源的任务处理器
        
        Args:
            redis_params: Redis连接参数
            task_queue_max_size: 任务队列最大大小
            result_queue_max_size: 结果队列最大大小
            monitor_interval: 监控间隔（秒）
            logger: 日志记录器
            
        Returns:
            任务处理器实例
        """
        data_source_config = DataSourceConfig(
            source_type=DataSourceType.REDIS,
            connection_params={'redis_params': redis_params}
        )
        
        task_queue_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=task_queue_max_size,
            monitor_interval=monitor_interval,
            low_threshold=100,
            batch_size=200  # 优化：从默认100增加到200
        )
        
        result_queue_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=result_queue_max_size,
            monitor_interval=monitor_interval,
            low_threshold=0
        )
        
        return TaskProcessor(
            data_source_config=data_source_config,
            task_queue_config=task_queue_config,
            result_queue_config=result_queue_config,
            logger=logger
        )
