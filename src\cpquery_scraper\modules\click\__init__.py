"""
Compatibility alias for the refactored Click module.

This package exposes the same API as the refactored `clicker` module but
is importable as `import click` to maintain backward compatibility with
legacy spider code and tests.
"""

# First, try absolute import (when project root is on sys.path)
try:  # pragma: no cover - simple import shim
    from src.cpquery_scraper.modules.clicker import *  # type: ignore  # noqa: F401,F403
except Exception:  # Fallback when absolute path isn't importable
    import os, sys, importlib.util  # noqa: E401
    current_dir = os.path.dirname(__file__)
    clicker_init = os.path.join(current_dir, '..', 'clicker', '__init__.py')
    clicker_init = os.path.abspath(clicker_init)
    spec = importlib.util.spec_from_file_location("_refactored_clicker", clicker_init)
    if spec and spec.loader:
        _module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(_module)  # type: ignore
        # Re-export public attributes
        for _name in dir(_module):
            if not _name.startswith('_'):
                globals()[_name] = getattr(_module, _name)
    else:
        raise ImportError("Unable to load refactored clicker module for compatibility shim")
