"""
配置工厂模块

提供根据数据源类型自动优化的配置创建功能
"""

from typing import Dict, Any, Optional
import logging

from .models import (
    DataSourceConfig, QueueConfig, DataSourceType, QueueType, Constants
)


class OptimizedConfigFactory:
    """优化配置工厂"""
    
    @staticmethod
    def create_optimized_data_source_config(
        source_type: DataSourceType,
        connection_params: Dict[str, Any],
        **kwargs
    ) -> DataSourceConfig:
        """
        创建优化的数据源配置
        
        Args:
            source_type: 数据源类型
            connection_params: 连接参数
            **kwargs: 其他配置参数
            
        Returns:
            优化的数据源配置
        """
        # 基础配置
        config_params = {
            'source_type': source_type,
            'connection_params': connection_params,
            'max_connections': kwargs.get('max_connections', Constants.DEFAULT_MAX_CONNECTIONS),
            'timeout': kwargs.get('timeout', Constants.DEFAULT_TIMEOUT),
            'retry_times': kwargs.get('retry_times', Constants.DEFAULT_RETRY_TIMES)
        }
        
        # 根据数据源类型优化配置
        if source_type == DataSourceType.REDIS:
            # Redis优化：增加连接池大小，减少超时时间
            config_params['max_connections'] = kwargs.get('max_connections', 20)
            config_params['timeout'] = kwargs.get('timeout', 15)
            
        elif source_type == DataSourceType.MYSQL:
            # MySQL优化：适中的连接池大小，增加超时时间
            config_params['max_connections'] = kwargs.get('max_connections', 15)
            config_params['timeout'] = kwargs.get('timeout', 45)
            
        elif source_type == DataSourceType.EXCEL:
            # Excel优化：不需要连接池，快速超时
            config_params['max_connections'] = 1
            config_params['timeout'] = kwargs.get('timeout', 10)
        
        return DataSourceConfig(**config_params)
    
    @staticmethod
    def create_optimized_queue_config(
        queue_type: QueueType,
        data_source_type: DataSourceType,
        **kwargs
    ) -> QueueConfig:
        """
        创建优化的队列配置
        
        Args:
            queue_type: 队列类型
            data_source_type: 数据源类型
            **kwargs: 其他配置参数
            
        Returns:
            优化的队列配置
        """
        # 基础配置
        config_params = {
            'queue_type': queue_type,
            'max_size': kwargs.get('max_size', Constants.DEFAULT_QUEUE_MAX_SIZE),
            'monitor_interval': kwargs.get('monitor_interval', Constants.DEFAULT_MONITOR_INTERVAL),
            'low_threshold': kwargs.get('low_threshold', Constants.DEFAULT_LOW_THRESHOLD),
            'batch_size': kwargs.get('batch_size', Constants.DEFAULT_BATCH_SIZE)
        }
        
        # 根据数据源类型优化配置
        if data_source_type == DataSourceType.REDIS:
            config_params.update({
                'monitor_interval': kwargs.get('monitor_interval', Constants.REDIS_MONITOR_INTERVAL),
                'batch_size': kwargs.get('batch_size', Constants.REDIS_BATCH_SIZE),
                'low_threshold': kwargs.get('low_threshold', 100)
            })
            
        elif data_source_type == DataSourceType.MYSQL:
            config_params.update({
                'monitor_interval': kwargs.get('monitor_interval', Constants.DEFAULT_MONITOR_INTERVAL),
                'batch_size': kwargs.get('batch_size', Constants.MYSQL_BATCH_SIZE),
                'low_threshold': kwargs.get('low_threshold', 500)
            })
            
        elif data_source_type == DataSourceType.EXCEL:
            config_params.update({
                'monitor_interval': kwargs.get('monitor_interval', Constants.EXCEL_MONITOR_INTERVAL),
                'batch_size': kwargs.get('batch_size', Constants.EXCEL_BATCH_SIZE),
                'low_threshold': 0  # Excel是一次性读取，不需要补充
            })
        
        # 结果队列的特殊优化
        if queue_type == QueueType.RESULT_QUEUE:
            config_params.update({
                'max_size': kwargs.get('max_size', Constants.DEFAULT_QUEUE_MAX_SIZE * 2),  # 结果队列更大
                'monitor_interval': kwargs.get('monitor_interval', 60),  # 结果队列监控间隔更长
                'low_threshold': 0,  # 结果队列不需要补充
                'batch_size': kwargs.get('batch_size', 100)  # 结果队列批处理适中
            })
        
        return QueueConfig(**config_params)
    
    @staticmethod
    def get_performance_recommendations(data_source_type: DataSourceType) -> Dict[str, Any]:
        """
        获取性能优化建议
        
        Args:
            data_source_type: 数据源类型
            
        Returns:
            性能优化建议
        """
        recommendations = {
            'general': [
                "使用批量处理减少I/O开销",
                "根据系统负载调整监控间隔",
                "定期监控性能指标"
            ]
        }
        
        if data_source_type == DataSourceType.REDIS:
            recommendations['redis'] = [
                "增加批处理大小到200以减少Redis访问频率",
                "使用连接池复用Redis连接",
                "监控Redis内存使用情况",
                "考虑使用Redis管道提高性能"
            ]
            
        elif data_source_type == DataSourceType.MYSQL:
            recommendations['mysql'] = [
                "使用连接池管理数据库连接",
                "优化SQL查询和索引",
                "监控数据库连接数和查询性能",
                "考虑读写分离"
            ]
            
        elif data_source_type == DataSourceType.EXCEL:
            recommendations['excel'] = [
                "一次性读取所有数据到内存",
                "使用较大的批处理大小",
                "考虑将Excel数据预处理到数据库",
                "监控文件I/O性能"
            ]
        
        return recommendations


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.profiles = {}
    
    def profile_data_source(self, data_source_type: DataSourceType) -> Dict[str, Any]:
        """
        分析数据源性能特征
        
        Args:
            data_source_type: 数据源类型
            
        Returns:
            性能特征分析
        """
        profile = {
            'data_source_type': data_source_type.value,
            'characteristics': {},
            'recommendations': OptimizedConfigFactory.get_performance_recommendations(data_source_type)
        }
        
        if data_source_type == DataSourceType.REDIS:
            profile['characteristics'] = {
                'io_pattern': 'network_intensive',
                'latency': 'low',
                'throughput': 'high',
                'scalability': 'excellent',
                'optimal_batch_size': Constants.REDIS_BATCH_SIZE,
                'optimal_monitor_interval': Constants.REDIS_MONITOR_INTERVAL
            }
            
        elif data_source_type == DataSourceType.MYSQL:
            profile['characteristics'] = {
                'io_pattern': 'network_and_disk_intensive',
                'latency': 'medium',
                'throughput': 'medium',
                'scalability': 'good',
                'optimal_batch_size': Constants.MYSQL_BATCH_SIZE,
                'optimal_monitor_interval': Constants.DEFAULT_MONITOR_INTERVAL
            }
            
        elif data_source_type == DataSourceType.EXCEL:
            profile['characteristics'] = {
                'io_pattern': 'disk_intensive',
                'latency': 'high_initial_low_subsequent',
                'throughput': 'high_after_load',
                'scalability': 'limited',
                'optimal_batch_size': Constants.EXCEL_BATCH_SIZE,
                'optimal_monitor_interval': Constants.EXCEL_MONITOR_INTERVAL
            }
        
        self.profiles[data_source_type] = profile
        return profile
    
    def get_optimization_suggestions(self, data_source_type: DataSourceType) -> Dict[str, Any]:
        """
        获取优化建议
        
        Args:
            data_source_type: 数据源类型
            
        Returns:
            优化建议
        """
        if data_source_type not in self.profiles:
            self.profile_data_source(data_source_type)
        
        profile = self.profiles[data_source_type]
        
        suggestions = {
            'config_optimizations': {
                'batch_size': profile['characteristics']['optimal_batch_size'],
                'monitor_interval': profile['characteristics']['optimal_monitor_interval']
            },
            'performance_tips': profile['recommendations'],
            'monitoring_focus': []
        }
        
        # 根据数据源特征添加监控重点
        if data_source_type == DataSourceType.REDIS:
            suggestions['monitoring_focus'] = [
                'network_latency', 'redis_memory_usage', 'connection_pool_utilization'
            ]
        elif data_source_type == DataSourceType.MYSQL:
            suggestions['monitoring_focus'] = [
                'query_execution_time', 'connection_pool_utilization', 'database_locks'
            ]
        elif data_source_type == DataSourceType.EXCEL:
            suggestions['monitoring_focus'] = [
                'file_io_performance', 'memory_usage', 'file_processing_time'
            ]
        
        return suggestions
