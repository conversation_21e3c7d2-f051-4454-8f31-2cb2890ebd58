"""
测试运行器：运行所有auth模块相关的测试
"""
import asyncio
import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def run_test_script(script_name, description):
    """运行单个测试脚本"""
    print(f"\n{'='*60}")
    print(f"运行测试: {description}")
    print(f"脚本: {script_name}")
    print('='*60)
    
    script_path = Path(__file__).parent / script_name
    
    try:
        # 使用subprocess运行测试脚本
        result = subprocess.run(
            [sys.executable, str(script_path)],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"[PASS] {description} - 测试通过")
            return True
        else:
            print(f"[FAIL] {description} - 测试失败 (返回码: {result.returncode})")
            return False

    except subprocess.TimeoutExpired:
        print(f"[TIMEOUT] {description} - 测试超时")
        return False
    except Exception as e:
        print(f"[ERROR] {description} - 测试执行异常: {e}")
        return False

def main():
    """主函数：运行所有测试"""
    print("开始运行Auth模块所有测试...")
    
    # 定义要运行的测试
    tests = [
        ("test_exceptions.py", "异常类功能测试"),
        ("test_auth_module.py", "Auth模块兼容性测试"),
        ("detailed_comparison_test.py", "详细对比测试"),
        ("switch_verification.py", "切换验证测试"),
    ]
    
    results = []
    
    # 运行每个测试
    for script_name, description in tests:
        success = run_test_script(script_name, description)
        results.append((description, success))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print('='*60)
    
    passed = 0
    total = len(results)
    
    for description, success in results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"{status} {description}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n[SUCCESS] 所有测试通过！Auth模块重构成功！")
        return True
    else:
        print(f"\n[WARNING] 有 {total-passed} 个测试失败，请检查相关问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
