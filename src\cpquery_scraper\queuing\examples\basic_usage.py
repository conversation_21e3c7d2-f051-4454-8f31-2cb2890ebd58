"""
基本使用示例

展示如何使用重构后的任务调度模块。

Author: wwind
Date: 2025.07.08
"""

import asyncio
import tempfile
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def example_1_compatibility_mode():
    """示例1：兼容性模式使用"""
    logger.info("=== 示例1：兼容性模式 ===")
    
    try:
        # 导入兼容性接口
        from queue_scheduling_v2 import gen_task_queue, result_queue_save_to_Mysql
        
        # 创建队列（与原有代码完全相同）
        task_queue = asyncio.Queue(1000)
        result_queue = asyncio.Queue(5000)
        
        logger.info("✓ 兼容性模式导入成功")
        logger.info("✓ 可以直接替换原有的import语句")
        
    except Exception as e:
        logger.error(f"兼容性模式示例失败: {e}")


async def example_2_new_interface_excel():
    """示例2：新接口 - Excel数据源"""
    logger.info("=== 示例2：新接口 - Excel数据源 ===")
    
    try:
        from queue_scheduling_v2 import TaskProcessorFactory
        
        # 创建临时目录作为Excel文件目录
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info(f"使用临时目录: {temp_dir}")
            
            # 创建Excel任务处理器
            processor = TaskProcessorFactory.create_excel_processor(
                excel_dir=temp_dir,
                task_queue_max_size=1000,
                result_queue_max_size=2000
            )
            
            # 使用上下文管理器自动管理生命周期
            async with processor:
                logger.info("✓ Excel任务处理器启动成功")
                
                # 获取处理器状态
                status = await processor.get_status()
                logger.info(f"处理器状态: {status['is_running']}")
                logger.info(f"数据源类型: {status['data_source_type']}")
                logger.info(f"任务队列大小: {status['task_queue']['queue_size']}")
                
                # 尝试获取任务（由于目录为空，应该很快返回None）
                task = await processor.get_task(timeout=1.0)
                if task is None:
                    logger.info("✓ 没有任务可处理（符合预期）")
                else:
                    logger.info(f"获取到任务: {task.task_id}")
            
            logger.info("✓ Excel处理器示例完成")
            
    except Exception as e:
        logger.error(f"Excel数据源示例失败: {e}")


async def example_3_custom_configuration():
    """示例3：自定义配置"""
    logger.info("=== 示例3：自定义配置 ===")
    
    try:
        from queue_scheduling_v2 import (
            TaskProcessor, DataSourceConfig, QueueConfig,
            DataSourceType, QueueType
        )
        
        # 创建自定义数据源配置
        with tempfile.TemporaryDirectory() as temp_dir:
            data_source_config = DataSourceConfig(
                source_type=DataSourceType.EXCEL,
                connection_params={
                    'excel_dir': temp_dir,
                    'file_pattern': '*.xlsx',
                    'column_names': ['申请号', '专利号']
                },
                max_connections=5,
                timeout=30
            )
            
            # 创建自定义任务队列配置
            task_queue_config = QueueConfig(
                queue_type=QueueType.TASK_QUEUE,
                max_size=2000,
                monitor_interval=30,
                low_threshold=200,
                batch_size=100
            )
            
            # 创建自定义结果队列配置
            result_queue_config = QueueConfig(
                queue_type=QueueType.RESULT_QUEUE,
                max_size=5000,
                monitor_interval=60,
                low_threshold=0,
                batch_size=50
            )
            
            # 创建任务处理器
            processor = TaskProcessor(
                data_source_config=data_source_config,
                task_queue_config=task_queue_config,
                result_queue_config=result_queue_config
            )
            
            async with processor:
                logger.info("✓ 自定义配置处理器启动成功")
                
                status = await processor.get_status()
                logger.info(f"任务队列最大大小: {status['task_queue']['max_size']}")
                logger.info(f"结果队列最大大小: {status['result_queue']['max_size']}")
            
            logger.info("✓ 自定义配置示例完成")
            
    except Exception as e:
        logger.error(f"自定义配置示例失败: {e}")


async def example_4_error_handling():
    """示例4：错误处理"""
    logger.info("=== 示例4：错误处理 ===")
    
    try:
        from queue_scheduling_v2 import (
            TaskProcessorFactory, DataSourceError,
            ConfigurationError, QueueOperationError
        )
        
        # 测试错误处理
        try:
            # 尝试使用不存在的目录
            processor = TaskProcessorFactory.create_excel_processor(
                excel_dir="/nonexistent/directory"
            )
            logger.error("应该抛出异常")
        except DataSourceError as e:
            logger.info(f"✓ 正确捕获数据源错误: {e}")
            logger.info(f"错误代码: {e.error_code}")
            logger.info(f"错误详情: {e.details}")
        
        # 测试异常信息转换
        try:
            raise ConfigurationError(
                "测试配置错误",
                config_key="test_key",
                config_value="test_value"
            )
        except ConfigurationError as e:
            error_dict = e.to_dict()
            logger.info(f"✓ 异常信息字典: {error_dict}")
        
        logger.info("✓ 错误处理示例完成")
        
    except Exception as e:
        logger.error(f"错误处理示例失败: {e}")


async def example_5_data_models():
    """示例5：数据模型使用"""
    logger.info("=== 示例5：数据模型使用 ===")
    
    try:
        from queue_scheduling_v2 import TaskItem, TaskStatus, ProcessingStats
        
        # 创建任务项
        task = TaskItem(
            task_id="2023123456789",
            priority=1,
            max_retries=5
        )
        
        logger.info(f"任务ID: {task.task_id}")
        logger.info(f"任务状态: {task.status}")
        logger.info(f"是否有效: {task.is_valid()}")
        logger.info(f"可以重试: {task.can_retry()}")
        
        # 测试任务验证
        invalid_task = TaskItem(task_id="invalid.task.id")
        logger.info(f"无效任务是否有效: {invalid_task.is_valid()}")
        
        # 转换为字典
        task_dict = task.to_dict()
        logger.info(f"任务字典: {task_dict}")
        
        # 从字典创建任务
        restored_task = TaskItem.from_dict(task_dict)
        logger.info(f"恢复的任务ID: {restored_task.task_id}")
        
        # 处理统计
        stats = ProcessingStats()
        stats.update_stats(completed=10, failed=2)
        logger.info(f"成功率: {stats.get_success_rate():.2%}")
        logger.info(f"统计信息: {stats.to_dict()}")
        
        logger.info("✓ 数据模型示例完成")
        
    except Exception as e:
        logger.error(f"数据模型示例失败: {e}")


async def run_all_examples():
    """运行所有示例"""
    logger.info("开始运行使用示例...")
    
    examples = [
        example_1_compatibility_mode,
        example_2_new_interface_excel,
        example_3_custom_configuration,
        example_4_error_handling,
        example_5_data_models,
    ]
    
    for i, example in enumerate(examples, 1):
        logger.info(f"\n{'='*60}")
        try:
            await example()
        except Exception as e:
            logger.error(f"示例 {i} 执行失败: {e}")
        
        # 在示例之间稍作停顿
        await asyncio.sleep(0.5)
    
    logger.info(f"\n{'='*60}")
    logger.info("所有示例执行完成！")


if __name__ == "__main__":
    asyncio.run(run_all_examples())
