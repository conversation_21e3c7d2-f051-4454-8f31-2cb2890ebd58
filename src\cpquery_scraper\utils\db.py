from dbutils.pooled_db import PooledDB
import pymysql
from redis import Redis

# Corrected import paths
from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger

class MysqlConnection:
    '''
    Mysql服务器连接管理器
    '''
    def __init__(self):
        self.__logger = get_logger(__name__)
        self.__pool = None
        self.__mysql_client = None

    def connect_mysql(self, mysql_param=None, max_connections=10):
        if mysql_param is None:
            mysql_param = config.MYSQL_PARAM
        self.__logger.info(f"连接到mysql[size: {max_connections}]客户端。")
        self.__pool = PooledDB(pymysql, maxconnections=max_connections, **mysql_param)

    def close_mysql(self):
        if self.__pool:
            self.__pool.close()

    def query(self, sql, values=None):
        connection = self.__pool.connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql, values)
        return cursor.fetchall()

    def query_one(self, sql, values=None):
        connection = self.__pool.connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql, values)
        return cursor.fetchone()

    def insert_or_update(self, sql, values=None):
        connection = self.__pool.connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        cursor.execute(sql, values)
        connection.commit()

    def insert_or_update_batch(self, sql:str, values: list):
        connection = self.__pool.connection()
        cursor = None
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            result = cursor.executemany(sql, values)
            connection.commit()
            return result
        except pymysql.MySQLError as e:
            self.__logger.error(f"MySQL 错误: {e}")
            connection.rollback()
            raise
        except Exception as e:
            self.__logger.error(f"其他错误: {e}")
            connection.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            connection.close()


class RedisConnection:
    '''
    从老爬虫借鉴来的redis操作类
    '''
    def __init__(self):
        self.__logger = get_logger(__name__)
        self.__redis_client = None
        self.__pipeline = None

    def connect_redis(self, redis_param=None, db=1, max_connections=10):
        if redis_param is None:
            redis_param = config.REDIS_PARAM
        self.__redis_client = Redis(**redis_param, db=db, max_connections=max_connections)

    def close_redis(self):
        if self.__redis_client:
            self.__redis_client.close()

    def spop(self, key):
        return self.__redis_client.spop(key)

    def lpop(self, key):
        return self.__redis_client.lpop(key)

    def scard(self, key):
        return self.__redis_client.scard(key)

    def llen(self, key):
        return self.__redis_client.llen(key)

    def sadd(self, key, *values):
        self.__redis_client.sadd(key, *values)

    def rpush(self, key, *values):
        self.__redis_client.rpush(key, *values)

    def smembers(self, key):
        return self.__redis_client.smembers(key)

    def srem(self, key, value):
        self.__redis_client.srem(key, value)

    def Lrem(self, key, value):
        self.__redis_client.lrem(key, 0, value)

    def srandmember(self, key, count=1):
        return self.__redis_client.srandmember(key, count)

    def lock(self, name):
        return self.__redis_client.lock(name)

    def hget(self, name, key):
        return self.__redis_client.hget(name, key)

    def hincrby(self, name, key, amount=1):
        self.__redis_client.hincrby(name, key, amount)

    def hset(self, name, key, value):
        return self.__redis_client.hset(name, key, value)

    def delete(self, name):
        return self.__redis_client.delete(name)

    def ping(self):
        if self.__redis_client:
            return self.__redis_client.ping()
        else:
            self.__logger.error("Redis client is not initialized.")
            return False

    def pipeline(self, transaction=True):
        if not self.__redis_client:
            self.__logger.error("Redis 客户端未连接，无法创建 pipeline")
            return None
        self.__pipeline = self.__redis_client.pipeline(transaction=transaction)
        return self.__pipeline

    def get_pipeline(self):
        if not self.__pipeline:
            return self.pipeline()
        return self.__pipeline

    def execute_pipeline(self):
        if not self.__pipeline:
            self.__logger.warning("Pipeline 未创建，无法执行")
            return None
        try:
            results = self.__pipeline.execute()
            self.__pipeline = None
            return results
        except Exception as e:
            self.__logger.error(f"Pipeline 执行失败: {str(e)}")
            self.__pipeline = None
            raise

    def __enter__(self):
        return self.pipeline()

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.__pipeline:
            if exc_type is None:
                self.execute_pipeline()
            else:
                self.__pipeline = None

    def hkeys(self, name):
        if self.__redis_client:
            return self.__redis_client.hkeys(name)
        else:
            self.__logger.error("Redis client is not initialized.")
            return None

    def hgetall(self, key):
        if self.__redis_client:
            return self.__redis_client.hgetall(key)
        else:
            self.__logger.error("Redis client is not initialized.")
            return None

    def hlen(self, key):
        if self.__redis_client:
            return self.__redis_client.hlen(key)
        else:
            self.__logger.error("Redis client is not initialized.")
            return 0
