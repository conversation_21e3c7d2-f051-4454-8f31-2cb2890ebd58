"""
用户登录模块
提供用户认证、登录状态管理等功能
"""

from .login_manager import LoginManager
from .user_provider import UserProvider
from .login_checker import LoginChecker
from .captcha_solver import CaptchaSolver
from .exceptions import (
    LoginError, CaptchaError, CredentialsError, NetworkError,
    PageCrashedError, TimeoutError, ConfigurationError,
    LoginTimeoutError, CaptchaTimeoutError
)

# 为保持与原模块兼容，导出相同的接口函数
from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger

# 创建全局实例
_logger = get_logger(__name__)
_login_manager = LoginManager(config, _logger)
_user_provider = UserProvider(config, _logger)
_login_checker = LoginChecker(config, _logger)

# 导出兼容函数
async def user_login(name, page, user_info, before_redirect_url):
    """兼容原模块的用户登录函数"""
    return await _login_manager.login(name, page, user_info, before_redirect_url)

async def get_page_logined(name, page, retry_times=0):
    """兼容原模块的获取已登录页面函数"""
    return await _login_manager.get_page_logined(name, page, retry_times)

async def check_login_page_is_logined(name, page):
    """兼容原模块的检查页面是否已登录函数"""
    return await _login_checker.check_login_status(name, page)

def get_user():
    """兼容原模块的获取用户函数"""
    return _user_provider.get_user()

def calculate_offset(base64_str):
    """兼容原模块的计算偏移量函数"""
    return _login_manager.captcha_solver.calculate_offset(base64_str)
