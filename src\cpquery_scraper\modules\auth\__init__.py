"""
用户登录模块
提供用户认证、登录状态管理等功能
"""

from .login_manager import LoginManager
from .user_provider import UserProvider
from .login_checker import <PERSON>ginChecker
from .captcha_solver import CaptchaSolver
from .exceptions import (
    LoginError, CaptchaError, CredentialsError, NetworkError,
    PageCrashedError, TimeoutError, ConfigurationError,
    LoginTimeoutError, CaptchaTimeoutError
)

# 为保持与原模块兼容，导出相同的接口函数
from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger

# 创建全局实例
_logger = get_logger(__name__)
_login_manager = LoginManager(config, _logger)
_user_provider = UserProvider(config, _logger)
_login_checker = LoginChecker(config, _logger)

# 导出兼容函数
async def user_login(name, page, user_info, before_redirect_url):
    """兼容原模块的用户登录函数"""
    return await _login_manager.login(name, page, user_info, before_redirect_url)

async def get_page_logined(name, page, retry_times=0):
    """兼容原模块的获取已登录页面函数"""
    return await _login_manager.get_page_logined(name, page, retry_times)

async def get_context_be_logined(context, name):
    """
    Ensure context is logged in as per original specification

    This function handles three page states:
    - Already redirected to unified auth: re-login
    - Already on query homepage: validate "欢迎您，" contains current user
    - Other URL: raise ValueError

    Args:
        context: Browser context
        name: Context name for logging

    Returns:
        BrowserContext: Logged in context

    Raises:
        ValueError: If unable to determine page state or login fails
    """
    if not context.pages:
        raise ValueError("Context has no pages")

    page = context.pages[0]
    current_url = page.url

    # Check current page state and handle accordingly
    if "tysf.cponline.cnipa.gov.cn" in current_url:
        # Already redirected to unified auth - need to login
        _logger.info(f"--{name}: Context redirected to unified auth, performing login")
        user_info = _user_provider.get_user()
        result = await user_login(name, page, user_info, current_url)
        if result is None:
            raise ValueError("Login failed")
        return context

    elif "cpquery.cponline.cnipa.gov.cn" in current_url:
        # Already on query homepage - validate login status
        _logger.info(f"--{name}: Context on query homepage, validating login status")
        try:
            # Check for "欢迎您，" text indicating logged in user
            welcome_text = await page.text_content("body")
            if "欢迎您，" in welcome_text:
                _logger.info(f"--{name}: Context login validation successful")
                return context
            else:
                # Not logged in, need to redirect to login
                _logger.warning(f"--{name}: Context not logged in, redirecting to auth")
                await page.goto(config.AUTH_MAINPAGE_URL)
                return await get_context_be_logined(context, name)
        except Exception as e:
            _logger.error(f"--{name}: Error validating login status: {e}")
            raise ValueError(f"Failed to validate login status: {e}")
    else:
        # Unknown URL state
        raise ValueError(f"Unknown page state for context login: {current_url}")

async def check_login_page_is_logined(name, page):
    """兼容原模块的检查页面是否已登录函数"""
    return await _login_checker.check_login_status(name, page)

def get_user():
    """兼容原模块的获取用户函数"""
    return _user_provider.get_user()

def calculate_offset(base64_str):
    """兼容原模块的计算偏移量函数"""
    return _login_manager.captcha_solver.calculate_offset(base64_str)

# Export all compatibility functions
__all__ = [
    'user_login',
    'get_page_logined',
    'get_context_be_logined',
    'check_login_page_is_logined',
    'get_user',
    'calculate_offset'
]
