import asyncio
import os
import time
# import gc

from src.cpquery_scraper.app import App
from src.cpquery_scraper.config import config

def main():
    """
    应用程序的主入口点。
    初始化并运行爬虫应用。
    """
    # 这个循环允许应用程序重新启动，这是原始主脚本的一个功能。
    if config.RUN_ONCE:
        app = App()
        asyncio.run(app.run())
    else:
        while True:
            app = App()
            asyncio.run(app.run())
            # 这条打印语句来自旧脚本，推测是用于操作反馈
            print("------------10秒后，换用户登录，再次开启任务------------")
            time.sleep(10)  # Fixed: should be 10 seconds as per documentation

if __name__ == "__main__":
    # 解决 Playwright 的 node 进程可能存在的内存问题
    # 这在原始脚本中就有，似乎很重要。
    os.environ['NODE_OPTIONS'] = '--max-old-space-size=2048'
    main()
