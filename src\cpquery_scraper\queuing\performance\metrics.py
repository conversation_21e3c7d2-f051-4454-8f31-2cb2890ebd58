"""
性能监控模块

提供任务队列调度系统的性能监控和指标收集功能
"""

import time
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from collections import deque
import statistics


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    
    # 任务获取性能
    task_fetch_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    task_fetch_count: int = 0
    task_fetch_errors: int = 0
    
    # 队列操作性能
    queue_put_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    queue_get_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    queue_operations_count: int = 0
    
    # 批处理性能
    batch_sizes: deque = field(default_factory=lambda: deque(maxlen=100))
    batch_processing_times: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # 内存使用（简单统计）
    memory_snapshots: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # 线程锁
    _lock: threading.Lock = field(default_factory=threading.Lock)
    
    def record_task_fetch(self, duration: float, success: bool = True) -> None:
        """记录任务获取性能"""
        with self._lock:
            self.task_fetch_times.append(duration)
            self.task_fetch_count += 1
            if not success:
                self.task_fetch_errors += 1
    
    def record_queue_operation(self, operation_type: str, duration: float) -> None:
        """记录队列操作性能"""
        with self._lock:
            if operation_type == 'put':
                self.queue_put_times.append(duration)
            elif operation_type == 'get':
                self.queue_get_times.append(duration)
            self.queue_operations_count += 1
    
    def record_batch_processing(self, batch_size: int, duration: float) -> None:
        """记录批处理性能"""
        with self._lock:
            self.batch_sizes.append(batch_size)
            self.batch_processing_times.append(duration)
    
    def record_memory_usage(self, memory_mb: float) -> None:
        """记录内存使用"""
        with self._lock:
            self.memory_snapshots.append(memory_mb)
    
    def get_task_fetch_stats(self) -> Dict[str, Any]:
        """获取任务获取统计"""
        with self._lock:
            if not self.task_fetch_times:
                return {'avg': 0, 'min': 0, 'max': 0, 'count': 0, 'error_rate': 0}
            
            times = list(self.task_fetch_times)
            return {
                'avg': statistics.mean(times),
                'min': min(times),
                'max': max(times),
                'median': statistics.median(times),
                'count': self.task_fetch_count,
                'error_rate': self.task_fetch_errors / max(self.task_fetch_count, 1)
            }
    
    def get_queue_operation_stats(self) -> Dict[str, Any]:
        """获取队列操作统计"""
        with self._lock:
            put_times = list(self.queue_put_times)
            get_times = list(self.queue_get_times)
            
            return {
                'put_avg': statistics.mean(put_times) if put_times else 0,
                'get_avg': statistics.mean(get_times) if get_times else 0,
                'total_operations': self.queue_operations_count
            }
    
    def get_batch_processing_stats(self) -> Dict[str, Any]:
        """获取批处理统计"""
        with self._lock:
            sizes = list(self.batch_sizes)
            times = list(self.batch_processing_times)
            
            if not sizes or not times:
                return {'avg_batch_size': 0, 'avg_processing_time': 0, 'throughput': 0}
            
            avg_batch_size = statistics.mean(sizes)
            avg_processing_time = statistics.mean(times)
            throughput = avg_batch_size / avg_processing_time if avg_processing_time > 0 else 0
            
            return {
                'avg_batch_size': avg_batch_size,
                'avg_processing_time': avg_processing_time,
                'throughput': throughput,
                'total_batches': len(sizes)
            }
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存使用统计"""
        with self._lock:
            if not self.memory_snapshots:
                return {'current': 0, 'avg': 0, 'max': 0}
            
            snapshots = list(self.memory_snapshots)
            return {
                'current': snapshots[-1],
                'avg': statistics.mean(snapshots),
                'max': max(snapshots),
                'min': min(snapshots)
            }
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            'task_fetch': self.get_task_fetch_stats(),
            'queue_operations': self.get_queue_operation_stats(),
            'batch_processing': self.get_batch_processing_stats(),
            'memory': self.get_memory_stats(),
            'timestamp': time.time()
        }
    
    def reset(self) -> None:
        """重置所有指标"""
        with self._lock:
            self.task_fetch_times.clear()
            self.task_fetch_count = 0
            self.task_fetch_errors = 0
            self.queue_put_times.clear()
            self.queue_get_times.clear()
            self.queue_operations_count = 0
            self.batch_sizes.clear()
            self.batch_processing_times.clear()
            self.memory_snapshots.clear()


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, logger=None):
        self.metrics = PerformanceMetrics()
        self.logger = logger
        self._monitoring = False
        self._monitor_thread = None
    
    def start_monitoring(self, interval: int = 60) -> None:
        """开始性能监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        
        if self.logger:
            self.logger.info(f"Performance monitoring started with {interval}s interval")
    
    def stop_monitoring(self) -> None:
        """停止性能监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        
        if self.logger:
            self.logger.info("Performance monitoring stopped")
    
    def _monitor_loop(self, interval: int) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                # 记录内存使用（简单实现）
                try:
                    import psutil
                    process = psutil.Process()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    self.metrics.record_memory_usage(memory_mb)
                except ImportError:
                    # psutil不可用时跳过内存监控
                    pass
                
                # 记录性能摘要
                if self.logger:
                    summary = self.metrics.get_summary()
                    self.logger.info(f"Performance Summary: {summary}")
                
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error in performance monitoring: {e}")
            
            time.sleep(interval)
    
    def get_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        return self.metrics


# 全局性能监控器实例
_global_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor(logger=None) -> PerformanceMonitor:
    """获取全局性能监控器"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor(logger)
    return _global_monitor


def record_task_fetch_time(duration: float, success: bool = True) -> None:
    """记录任务获取时间的便捷函数"""
    monitor = get_performance_monitor()
    monitor.metrics.record_task_fetch(duration, success)


def record_queue_operation_time(operation_type: str, duration: float) -> None:
    """记录队列操作时间的便捷函数"""
    monitor = get_performance_monitor()
    monitor.metrics.record_queue_operation(operation_type, duration)


def record_batch_processing_time(batch_size: int, duration: float) -> None:
    """记录批处理时间的便捷函数"""
    monitor = get_performance_monitor()
    monitor.metrics.record_batch_processing(batch_size, duration)
