"""
Click模块兼容性测试
验证重构后的模块与原模块的兼容性
"""
import asyncio
import inspect
import sys
import os
try:
    from unittest.mock import Mock, AsyncMock, patch
except ImportError:
    # Python < 3.8 compatibility
    from unittest.mock import Mock, patch

    class AsyncMock:
        """Simple AsyncMock for Python < 3.8"""
        def __init__(self, return_value=None):
            self.return_value = return_value
            self.call_count = 0

        async def __call__(self, *args, **kwargs):
            self.call_count += 1
            return self.return_value

        def __getattr__(self, name):
            return AsyncMock()
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

# 导入原模块和新模块
try:
    import corutine_click as original_module
    print("[OK] 成功导入原模块 corutine_click")
except ImportError as e:
    print(f"[FAIL] 导入原模块失败: {e}")
    original_module = None

try:
    # 导入重构后的click模块
    sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))
    import click as new_module
    print("[OK] 成功导入新模块 click")
except ImportError as e:
    print(f"[FAIL] 导入新模块失败: {e}")
    new_module = None
    sys.exit(1)


class ClickCompatibilityTester:
    """Click模块兼容性测试器"""
    
    def __init__(self):
        self.original_module = original_module
        self.new_module = new_module
        self.test_results = []
    
    def test_interface_compatibility(self) -> bool:
        """测试接口兼容性"""
        print("\n=== 测试接口兼容性 ===")
        
        # 需要检查的核心函数
        core_functions = [
            'main_page_query_an',
            'main_page_click_an',
            'get_appl_data',
            'click_event_button_and_get_data',
            'click_event_button_and_get_data_sync'
        ]
        
        all_compatible = True
        
        for func_name in core_functions:
            # 检查新模块是否有该函数
            if hasattr(self.new_module, func_name):
                new_func = getattr(self.new_module, func_name)
                print(f"  [OK] {func_name} 存在于新模块")

                # 检查函数签名
                if self.original_module and hasattr(self.original_module, func_name):
                    original_func = getattr(self.original_module, func_name)
                    if self._compare_function_signatures(original_func, new_func, func_name):
                        print(f"  [OK] {func_name} 函数签名兼容")
                    else:
                        print(f"  [FAIL] {func_name} 函数签名不兼容")
                        all_compatible = False
                else:
                    print(f"  [SKIP] {func_name} 无法与原模块比较（原模块不可用）")
            else:
                print(f"  [FAIL] {func_name} 不存在于新模块")
                all_compatible = False
        
        return all_compatible
    
    def _compare_function_signatures(self, original_func, new_func, func_name: str) -> bool:
        """比较函数签名"""
        try:
            original_sig = inspect.signature(original_func)
            new_sig = inspect.signature(new_func)
            
            # 比较参数名称和默认值
            original_params = list(original_sig.parameters.keys())
            new_params = list(new_sig.parameters.keys())
            
            # 检查必需参数是否一致
            original_required = [
                name for name, param in original_sig.parameters.items()
                if param.default == inspect.Parameter.empty
            ]
            new_required = [
                name for name, param in new_sig.parameters.items()
                if param.default == inspect.Parameter.empty
            ]
            
            if original_required == new_required:
                return True
            else:
                print(f"    原模块必需参数: {original_required}")
                print(f"    新模块必需参数: {new_required}")
                return False
                
        except Exception as e:
            print(f"    签名比较失败: {e}")
            return False
    
    def test_exception_handling(self) -> bool:
        """测试异常处理"""
        print("\n=== 测试异常处理 ===")
        
        # 检查异常类是否存在
        exception_classes = [
            'ClickError',
            'QueryError', 
            'PageInitError',
            'DataExtractionError',
            'FileAccessError',
            'ResponseTimeoutError',
            'PageCrashedError'
        ]
        
        all_present = True
        for exc_name in exception_classes:
            if hasattr(self.new_module, exc_name):
                exc_class = getattr(self.new_module, exc_name)
                if issubclass(exc_class, Exception):
                    print(f"  [OK] {exc_name} 异常类存在且继承自Exception")
                else:
                    print(f"  [FAIL] {exc_name} 不是有效的异常类")
                    all_present = False
            else:
                print(f"  [FAIL] {exc_name} 异常类不存在")
                all_present = False
        
        return all_present
    
    async def test_mock_functionality(self) -> bool:
        """测试模拟功能"""
        print("\n=== 测试模拟功能 ===")
        
        try:
            # 创建模拟对象
            mock_page = AsyncMock()
            mock_page.url = "https://cpquery.cponline.cnipa.gov.cn/detail/index?zhuanlisqh=test"
            mock_page.get_by_placeholder.return_value.fill = AsyncMock()
            mock_page.get_by_role.return_value.click = AsyncMock()
            mock_page.get_by_text.return_value.to_be_visible = AsyncMock()
            
            # 模拟响应
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "code": 200,
                "data": {"test": "data"},
                "msg": "success"
            }
            
            mock_page.expect_response.return_value.__aenter__.return_value = mock_response
            mock_page.expect_response.return_value.__aexit__.return_value = None
            
            # 测试查询功能
            try:
                result = await self.new_module.main_page_query_an("test", mock_page, "test123")
                print("  [OK] main_page_query_an 模拟测试通过")
            except Exception as e:
                print(f"  [FAIL] main_page_query_an 模拟测试失败: {e}")
                return False

            # 测试页面点击功能
            try:
                mock_page.expect_popup.return_value.__aenter__.return_value = mock_page
                mock_page.expect_popup.return_value.__aexit__.return_value = None
                mock_page.wait_for_url = AsyncMock()

                result_page = await self.new_module.main_page_click_an("test", mock_page, "test123")
                print("  [OK] main_page_click_an 模拟测试通过")
            except Exception as e:
                print(f"  [FAIL] main_page_click_an 模拟测试失败: {e}")
                return False
            
            return True
            
        except Exception as e:
            print(f"  [FAIL] 模拟功能测试失败: {e}")
            return False
    
    def test_module_structure(self) -> bool:
        """测试模块结构"""
        print("\n=== 测试模块结构 ===")
        
        # 检查模块属性
        required_attributes = ['__version__', '__all__']
        all_present = True
        
        for attr in required_attributes:
            if hasattr(self.new_module, attr):
                print(f"  [OK] {attr} 属性存在")
            else:
                print(f"  [FAIL] {attr} 属性不存在")
                all_present = False
        
        # 检查__all__列表
        if hasattr(self.new_module, '__all__'):
            all_list = getattr(self.new_module, '__all__')
            if isinstance(all_list, list) and len(all_list) > 0:
                print(f"  [OK] __all__ 列表包含 {len(all_list)} 个导出项")

                # 验证导出项是否真实存在
                missing_exports = []
                for item in all_list:
                    if not hasattr(self.new_module, item):
                        missing_exports.append(item)

                if missing_exports:
                    print(f"  [FAIL] 以下导出项不存在: {missing_exports}")
                    all_present = False
                else:
                    print("  [OK] 所有导出项都存在")
            else:
                print("  [FAIL] __all__ 不是有效的列表")
                all_present = False
        
        return all_present
    
    async def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("开始Click模块兼容性测试...")
        print("=" * 60)
        
        tests = [
            ("接口兼容性", self.test_interface_compatibility),
            ("异常处理", self.test_exception_handling),
            ("模块结构", self.test_module_structure),
            ("模拟功能", self.test_mock_functionality),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"测试 {test_name} 执行失败: {e}")
                results.append((test_name, False))
        
        # 输出测试结果汇总
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "[PASS] 通过" if result else "[FAIL] 失败"
            print(f"{status} {test_name}")
            if result:
                passed += 1

        print(f"\n总计: {passed}/{total} 测试通过")
        success_rate = (passed / total) * 100
        print(f"成功率: {success_rate:.1f}%")

        if passed == total:
            print("\n[SUCCESS] 所有测试通过！Click模块重构成功！")
            return True
        else:
            print(f"\n[WARNING] 有 {total-passed} 个测试失败，请检查相关问题")
            return False


async def main():
    """主函数"""
    tester = ClickCompatibilityTester()
    success = await tester.run_all_tests()
    return success


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试执行失败: {e}")
        sys.exit(1)
