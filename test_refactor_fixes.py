#!/usr/bin/env python3
"""
Test script to verify the refactoring fixes

This script tests the key behavioral consistencies that were restored:
1. Legacy interface compatibility
2. Data extraction logic
3. Task queue and result processing
4. Configuration and runtime behavior
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_legacy_imports():
    """Test that all legacy interfaces can be imported"""
    print("Testing legacy interface imports...")
    
    try:
        # Test main entry points
        import corutine_main
        import corutine_spider
        print("✓ Main entry points imported successfully")
        
        # Test queue scheduling
        import queue_scheduling_v2
        from queue_scheduling_v2 import gen_task_queue, result_queue_save_to_Mysql
        print("✓ Queue scheduling interfaces imported successfully")
        
        # Test result processor
        import result_processor
        from result_processor import ResultQueueProcessor
        print("✓ Result processor imported successfully")
        
        # Test utilities
        import corutine_utility
        from corutine_utility import get_logger, trans_format, RedisConnection, MysqlConnection
        print("✓ Utilities imported successfully")
        
        # Test static processor
        import web_asset_to_oss
        from web_asset_to_oss import StaticProcessor
        print("✓ Static processor imported successfully")
        
        # Test auth module
        from src.cpquery_scraper.modules.auth import (
            user_login, get_context_be_logined, get_page_logined, get_user
        )
        print("✓ Auth module imported successfully")
        
        # Test click module
        from src.cpquery_scraper.modules.clicker import (
            click_event_button_and_get_data_sync, main_page_query_an, main_page_click_an
        )
        print("✓ Click module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error during import: {e}")
        return False

def test_configuration():
    """Test configuration parameters"""
    print("\nTesting configuration parameters...")
    
    try:
        from src.cpquery_scraper.config import config
        
        # Test key parameters exist
        required_params = [
            'FLAG_SPIDER_RUNNING', 'MAX_TASK_NUMBER', 'MAX_TIME_USER_BEEN_USED',
            'WORKER_START_DELAY', 'TASK_QUEUE_MAX_SIZE', 'TASK_QUEUE_FROM_REDIS_SIZE',
            'RESULT_TO_REDIS_INTERVAL', 'RESULT_TO_MYSQL_INTERVAL', 'FINAL_WAIT_TIME',
            'EVENT_CLICK_MODE', 'DATA_SCOPE_MASK', 'ROLE'
        ]
        
        for param in required_params:
            if hasattr(config, param):
                value = getattr(config, param)
                print(f"✓ {param} = {value}")
            else:
                print(f"✗ Missing parameter: {param}")
                return False
        
        # Test role enum
        if hasattr(config, 'Role'):
            print(f"✓ Role enum available: {config.Role.MAIN}, {config.Role.DISTRIBUTED}")
        else:
            print("✗ Role enum missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_data_format():
    """Test data format transformation"""
    print("\nTesting data format transformation...")

    try:
        from corutine_utility import trans_format

        # Test that the function can be imported
        print("✓ trans_format function imported successfully")

        # Test that the function exists and is callable
        if callable(trans_format):
            print("✓ trans_format is callable")
        else:
            print("✗ trans_format is not callable")
            return False

        print("✓ Data format transformation function is available")
        return True

    except Exception as e:
        print(f"✗ Data format test failed: {e}")
        return False

async def test_queue_operations():
    """Test queue operations"""
    print("\nTesting queue operations...")
    
    try:
        # Test queue creation
        task_queue = asyncio.Queue(maxsize=100)
        result_queue = asyncio.Queue(maxsize=100)
        
        # Test adding tasks
        test_tasks = ["2017104799411", "202130674002X", "2024114351717"]
        for task in test_tasks:
            await task_queue.put(task)
        
        print(f"✓ Added {len(test_tasks)} tasks to queue")
        print(f"✓ Task queue size: {task_queue.qsize()}")
        
        # Test consuming tasks
        consumed_tasks = []
        while not task_queue.empty():
            task = await task_queue.get()
            consumed_tasks.append(task)
            task_queue.task_done()
        
        print(f"✓ Consumed {len(consumed_tasks)} tasks from queue")
        
        if consumed_tasks == test_tasks:
            print("✓ Task queue operations working correctly")
            return True
        else:
            print("✗ Task queue order mismatch")
            return False
            
    except Exception as e:
        print(f"✗ Queue operations test failed: {e}")
        return False

def test_static_processor():
    """Test static processor instantiation"""
    print("\nTesting static processor...")
    
    try:
        from web_asset_to_oss import StaticProcessor
        
        processor = StaticProcessor()
        print("✓ StaticProcessor instantiated successfully")
        
        # Test method existence
        if hasattr(processor, 'process_url'):
            print("✓ process_url method available")
        else:
            print("✗ process_url method missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Static processor test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("=" * 60)
    print("REFACTORING FIXES VERIFICATION TEST")
    print("=" * 60)
    
    tests = [
        ("Legacy Imports", test_legacy_imports),
        ("Configuration", test_configuration), 
        ("Data Format", test_data_format),
        ("Queue Operations", test_queue_operations),
        ("Static Processor", test_static_processor)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
            
        results.append((test_name, result))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Refactoring fixes appear to be working correctly.")
        return True
    else:
        print(f"\n❌ {total - passed} tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
