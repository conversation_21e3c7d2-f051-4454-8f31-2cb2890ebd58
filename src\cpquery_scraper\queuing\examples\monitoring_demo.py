"""
任务队列监控日志演示

展示重构后的任务队列监控日志输出格式
"""

import asyncio
import logging
from queue_scheduling import (
    TaskProcessor, DataSourceConfig, QueueConfig,
    DataSourceType, QueueType
)

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

async def demo_redis_monitoring():
    """演示Redis数据源的监控日志"""
    print("=== Redis数据源监控日志演示 ===")
    
    # 创建Redis数据源配置
    data_source_config = DataSourceConfig(
        source_type=DataSourceType.REDIS,
        connection_params={
            'redis_params': {
                'host': 'localhost',
                'port': 6379,
                'db': 0
            }
        }
    )
    
    # 创建任务队列配置（使用较小的值便于演示）
    task_queue_config = QueueConfig(
        queue_type=QueueType.TASK_QUEUE,
        max_size=500,           # 较小的队列大小
        monitor_interval=10,    # 10秒监控间隔
        low_threshold=50,       # 低水位阈值50
        batch_size=100          # 批处理大小100
    )
    
    # 创建结果队列配置
    result_queue_config = QueueConfig(
        queue_type=QueueType.RESULT_QUEUE,
        max_size=1000,
        monitor_interval=30,
        low_threshold=0
    )
    
    # 创建任务处理器
    processor = TaskProcessor(
        data_source_config=data_source_config,
        task_queue_config=task_queue_config,
        result_queue_config=result_queue_config
    )
    
    try:
        # 启动处理器
        await processor.start()
        
        print("\n监控日志将显示以下信息：")
        print("1. 定期队列状态监控")
        print("2. 低水位触发的补充操作")
        print("3. 实际补充的任务数量")
        print("4. 队列大小变化")
        print("\n等待监控日志输出...")
        
        # 运行一段时间以观察监控日志
        await asyncio.sleep(60)  # 运行1分钟
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
    finally:
        await processor.stop()

def show_expected_log_format():
    """显示预期的日志输出格式"""
    print("\n=== 预期的日志输出格式 ===")
    
    print("\n1. 定期监控日志：")
    print("2025-07-11 09:30:00 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列监控：当前队列大小=45, 低水位阈值=50, 最大容量=500")
    
    print("\n2. 触发补充时的日志：")
    print("2025-07-11 09:30:00 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列：队列大小(45)低于阈值(50)，开始补充任务")
    print("2025-07-11 09:30:00 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列：开始补充任务，当前大小=45, 需要补充=455, 本次获取=100")
    print("2025-07-11 09:30:01 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列：从redis数据源加载了100个任务，当前队列大小=145")
    print("2025-07-11 09:30:01 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列：补充完成，补充前=45, 补充后=145, 实际添加=100个任务")
    
    print("\n3. 队列充足时的日志：")
    print("2025-07-11 09:30:10 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列监控：当前队列大小=145, 低水位阈值=50, 最大容量=500")
    print("2025-07-11 09:30:10 [queue_scheduling.queue_managers.task_queue_manager] INFO: --任务队列：队列大小(145)充足，无需补充")
    
    print("\n4. 兼容性层的日志（如果使用）：")
    print("--任务队列：已添加 100 个任务到队列 (批量大小: 50, 当前队列大小: 145)")
    print("--任务队列：120秒周期监控，当前队列大小: 145")

async def main():
    """主函数"""
    print("任务队列监控日志演示")
    print("=" * 50)
    
    # 显示预期的日志格式
    show_expected_log_format()
    
    print("\n" + "=" * 50)
    print("注意：实际运行需要Redis服务器连接")
    print("如果要运行实际演示，请确保Redis服务器可用")
    
    # 如果需要运行实际演示，取消下面的注释
    # await demo_redis_monitoring()

if __name__ == "__main__":
    asyncio.run(main())
