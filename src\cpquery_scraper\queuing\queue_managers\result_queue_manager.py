"""
结果队列管理器

负责管理结果队列，将处理结果保存到数据库或其他存储系统。

Author: wwind
Date: 2025.07.08
"""

import asyncio
import time
from typing import Optional, Dict, Any, List, Callable
import logging

from ..models import QueueConfig, QueueType, ProcessingStats
from ..exceptions import QueueOperationError, ConfigurationError

# 导入重构后的新模块
from src.cpquery_scraper.utils.formatters import trans_format
from src.cpquery_scraper.utils.db import MysqlConnection
from src.cpquery_scraper.config import config

# 在新架构中，这些工具始终可用
UTILITY_AVAILABLE = True


class ResultQueueManager:
    """
    结果队列管理器
    
    负责处理结果队列中的数据并保存到数据库
    """
    
    def __init__(self, 
                 queue_config: QueueConfig,
                 result_processor: Optional[Callable] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化结果队列管理器
        
        Args:
            queue_config: 队列配置
            result_processor: 结果处理器函数
            logger: 日志记录器
        """
        self.config = queue_config
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        
        # 验证配置
        if not queue_config.validate():
            raise ConfigurationError(
                "Invalid queue configuration",
                config_key="queue_config",
                error_code="INVALID_CONFIG"
            )
        
        # 创建结果队列
        self._result_queue = asyncio.Queue(maxsize=queue_config.max_size)
        self._is_running = False
        self._processor_task = None
        self._stats = ProcessingStats()
        
        # 结果处理器
        self._result_processor = result_processor or self._default_result_processor
        
        # MySQL连接（用于保存结果）
        self._mysql_client = None
        
        self.logger.info("ResultQueueManager initialized")
    
    @property
    def queue_size(self) -> int:
        """获取当前队列大小"""
        return self._result_queue.qsize()
    
    @property
    def is_running(self) -> bool:
        """检查管理器是否正在运行"""
        return self._is_running
    
    @property
    def stats(self) -> ProcessingStats:
        """获取处理统计信息"""
        return self._stats
    
    async def start(self) -> None:
        """启动结果队列管理器"""
        if self._is_running:
            self.logger.warning("ResultQueueManager is already running")
            return
        
        try:
            # 初始化MySQL连接
            if UTILITY_AVAILABLE and config:
                self._mysql_client = MysqlConnection()
            
            # 启动处理任务
            self._is_running = True
            self._processor_task = asyncio.create_task(self._process_results())
            
            self.logger.info("ResultQueueManager started successfully")
            
        except Exception as e:
            self._is_running = False
            raise QueueOperationError(
                f"Failed to start ResultQueueManager: {e}",
                queue_type="result_queue",
                operation="start"
            )
    
    async def stop(self) -> None:
        """停止结果队列管理器"""
        if not self._is_running:
            return
        
        try:
            self._is_running = False
            
            # 取消处理任务
            if self._processor_task and not self._processor_task.done():
                self._processor_task.cancel()
                try:
                    await self._processor_task
                except asyncio.CancelledError:
                    pass
            
            # 处理剩余的结果
            await self._process_remaining_results()
            
            # 关闭MySQL连接
            if self._mysql_client:
                self._mysql_client.close_mysql()
                self._mysql_client = None
            
            self.logger.info("ResultQueueManager stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping ResultQueueManager: {e}")
    
    async def put_result(self, result_data: Any) -> None:
        """
        添加结果到队列
        
        Args:
            result_data: 结果数据
        """
        try:
            await self._result_queue.put(result_data)
            self.logger.debug("Result added to queue")
            
        except Exception as e:
            raise QueueOperationError(
                f"Failed to put result to queue: {e}",
                queue_type="result_queue",
                operation="put"
            )
    
    def put_result_nowait(self, result_data: Any) -> None:
        """
        非阻塞方式添加结果到队列
        
        Args:
            result_data: 结果数据
        """
        try:
            self._result_queue.put_nowait(result_data)
            self.logger.debug("Result added to queue (nowait)")
            
        except asyncio.QueueFull:
            raise QueueOperationError(
                "Result queue is full",
                queue_type="result_queue",
                queue_size=self.queue_size,
                operation="put_nowait"
            )
        except Exception as e:
            raise QueueOperationError(
                f"Failed to put result to queue: {e}",
                queue_type="result_queue",
                operation="put_nowait"
            )
    
    async def get_queue_info(self) -> Dict[str, Any]:
        """
        获取队列信息
        
        Returns:
            队列信息字典
        """
        return {
            'queue_size': self.queue_size,
            'max_size': self.config.max_size,
            'is_running': self.is_running,
            'stats': self._stats.to_dict()
        }
    
    async def _process_results(self) -> None:
        """处理结果队列中的数据"""
        self.logger.info("Result processing started")
        
        while self._is_running:
            try:
                # 等待队列中有数据
                while self.queue_size == 0 and self._is_running:
                    await asyncio.sleep(self.config.monitor_interval)
                
                if not self._is_running:
                    break
                
                # 批量获取结果
                results = await self._get_batch_results()
                
                if results:
                    # 处理结果批次
                    await self._process_result_batch(results)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in result processing: {e}")
                # 继续处理，不要因为单次错误而停止
                await asyncio.sleep(1)
        
        self.logger.info("Result processing stopped")
    
    async def _get_batch_results(self) -> List[Any]:
        """批量获取结果"""
        results = []
        batch_size = min(self.config.batch_size, self.queue_size)
        
        for _ in range(batch_size):
            try:
                result = self._result_queue.get_nowait()
                results.append(result)
                self._result_queue.task_done()
            except asyncio.QueueEmpty:
                break
            except Exception as e:
                self.logger.error(f"Error getting result from queue: {e}")
                break
        
        return results
    
    async def _process_result_batch(self, results: List[Any]) -> None:
        """处理结果批次"""
        try:
            start_time = time.time()
            processed_items = []
            
            # 转换结果格式
            for result in results:
                try:
                    processed_item = await self._result_processor(result)
                    if processed_item is not None:
                        processed_items.append(processed_item)
                except Exception as e:
                    self.logger.error(f"Error processing result: {e}")
                    self._stats.update_stats(failed=1)
                    continue
            
            # 保存到数据库
            if processed_items:
                await self._save_results_to_database(processed_items)
            
            # 更新统计信息
            processing_time = time.time() - start_time
            self._stats.update_stats(completed=len(processed_items))
            
            self.logger.info(f"Processed {len(processed_items)} results in {processing_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"Error processing result batch: {e}")
            self._stats.update_stats(failed=len(results))
    
    async def _save_results_to_database(self, items: List[Dict[str, Any]]) -> None:
        """保存结果到数据库"""
        if not UTILITY_AVAILABLE or not config or not self._mysql_client:
            self.logger.warning("Database utilities not available, skipping save")
            return
        
        try:
            # 连接数据库
            if not hasattr(self._mysql_client, '_pool') or self._mysql_client._pool is None:
                self._mysql_client.connect_mysql(
                    config.MYSQL_PARAM, 
                    max_connections=config.MAX_TASK_NUMBER
                )
            
            # 准备批量插入数据
            if items:
                cols, values = zip(*items[0].items())
                values_list = [list(item.values()) for item in items]
                
                sql_insert = f"""
                    INSERT INTO {config.DB_RESULT_TABLE_NAME}
                    ({', '.join(f'`{col}`' for col in cols)}) 
                    VALUES ({', '.join(['%s' for _ in values])})
                """
                
                # 执行批量插入
                if not config.TEST_MODE:
                    affected_rows = self._mysql_client.insert_or_update_batch(sql_insert, values_list)
                    self.logger.info(f"Saved {affected_rows} results to database")
                else:
                    self.logger.info(f"Test mode: would save {len(items)} results to database")
                
                # 更新任务状态（如果需要）
                await self._update_task_status(items)
            
        except Exception as e:
            self.logger.error(f"Failed to save results to database: {e}")
            raise
    
    async def _update_task_status(self, items: List[Dict[str, Any]]) -> None:
        """更新任务状态"""
        if not UTILITY_AVAILABLE or not config or not self._mysql_client:
            return
        
        try:
            # 提取任务ID列表
            task_ids = [item.get('patent_id') for item in items if item.get('patent_id')]
            
            if task_ids and not config.TEST_MODE and config.TASK_SOURCE != 'excel':
                sql_update = "UPDATE `epatent_0` SET `state` = 1 WHERE `an` = %s"
                update_values = [(task_id,) for task_id in task_ids]
                
                affected_rows = self._mysql_client.insert_or_update_batch(sql_update, update_values)
                self.logger.info(f"Updated status for {affected_rows} tasks")
            
        except Exception as e:
            self.logger.error(f"Failed to update task status: {e}")
    
    async def _process_remaining_results(self) -> None:
        """处理剩余的结果"""
        if self.queue_size > 0:
            self.logger.info(f"Processing {self.queue_size} remaining results")
            results = await self._get_batch_results()
            if results:
                await self._process_result_batch(results)
    
    async def _default_result_processor(self, result_data: Any) -> Optional[Dict[str, Any]]:
        """
        默认结果处理器
        
        Args:
            result_data: 原始结果数据
            
        Returns:
            处理后的结果数据
        """
        try:
            if UTILITY_AVAILABLE and trans_format:
                return trans_format(result_data)
            else:
                # 如果工具类不可用，返回原始数据
                self.logger.warning("trans_format not available, returning raw data")
                return result_data
                
        except Exception as e:
            self.logger.error(f"Error in default result processor: {e}")
            return None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.stop()
    
    def __str__(self) -> str:
        """返回管理器的字符串表示"""
        return f"ResultQueueManager(size={self.queue_size})"
