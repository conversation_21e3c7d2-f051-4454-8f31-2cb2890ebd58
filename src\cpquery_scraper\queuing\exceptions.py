"""
任务调度模块异常类定义

定义了任务调度模块中使用的所有自定义异常类，提供统一的异常处理机制。

Author: wwind
Date: 2025.07.08
"""

from typing import Optional, Any


class TaskSchedulingError(Exception):
    """
    任务调度模块基础异常类
    
    所有任务调度相关的异常都应该继承自此类
    """
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[dict] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码（可选）
            details: 错误详细信息（可选）
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        """返回异常的字符串表示"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self) -> dict:
        """将异常信息转换为字典格式"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class DataSourceError(TaskSchedulingError):
    """
    数据源相关异常
    
    当数据源操作失败时抛出此异常，包括数据库连接失败、文件读取失败等
    """
    
    def __init__(self, message: str, source_type: Optional[str] = None, 
                 source_config: Optional[dict] = None, **kwargs):
        """
        初始化数据源异常
        
        Args:
            message: 错误消息
            source_type: 数据源类型（mysql, excel, redis等）
            source_config: 数据源配置信息
            **kwargs: 其他参数传递给父类
        """
        details = kwargs.get('details', {})
        if source_type:
            details['source_type'] = source_type
        if source_config:
            details['source_config'] = source_config
        kwargs['details'] = details
        super().__init__(message, **kwargs)
        self.source_type = source_type
        self.source_config = source_config


class DatabaseConnectionError(DataSourceError):
    """
    数据库连接异常
    
    当数据库连接失败或操作失败时抛出此异常
    """
    
    def __init__(self, message: str, database_type: str = "mysql", 
                 connection_params: Optional[dict] = None, **kwargs):
        """
        初始化数据库连接异常
        
        Args:
            message: 错误消息
            database_type: 数据库类型
            connection_params: 连接参数
            **kwargs: 其他参数
        """
        super().__init__(
            message, 
            source_type=database_type,
            source_config=connection_params,
            **kwargs
        )
        self.database_type = database_type
        self.connection_params = connection_params


class FileOperationError(DataSourceError):
    """
    文件操作异常
    
    当文件读取、写入或处理失败时抛出此异常
    """
    
    def __init__(self, message: str, file_path: Optional[str] = None, 
                 operation: Optional[str] = None, **kwargs):
        """
        初始化文件操作异常
        
        Args:
            message: 错误消息
            file_path: 文件路径
            operation: 操作类型（read, write, parse等）
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if file_path:
            details['file_path'] = file_path
        if operation:
            details['operation'] = operation
        kwargs['details'] = details
        super().__init__(message, source_type="file", **kwargs)
        self.file_path = file_path
        self.operation = operation


class QueueOperationError(TaskSchedulingError):
    """
    队列操作异常
    
    当队列操作失败时抛出此异常，包括队列满、队列空、队列初始化失败等
    """
    
    def __init__(self, message: str, queue_type: Optional[str] = None, 
                 queue_size: Optional[int] = None, operation: Optional[str] = None, **kwargs):
        """
        初始化队列操作异常
        
        Args:
            message: 错误消息
            queue_type: 队列类型（task_queue, result_queue等）
            queue_size: 队列大小
            operation: 操作类型（put, get, init等）
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if queue_type:
            details['queue_type'] = queue_type
        if queue_size is not None:
            details['queue_size'] = queue_size
        if operation:
            details['operation'] = operation
        kwargs['details'] = details
        super().__init__(message, **kwargs)
        self.queue_type = queue_type
        self.queue_size = queue_size
        self.operation = operation


class TaskValidationError(TaskSchedulingError):
    """
    任务验证异常
    
    当任务数据验证失败时抛出此异常
    """
    
    def __init__(self, message: str, task_data: Optional[Any] = None, 
                 validation_rule: Optional[str] = None, **kwargs):
        """
        初始化任务验证异常
        
        Args:
            message: 错误消息
            task_data: 任务数据
            validation_rule: 验证规则
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if task_data is not None:
            details['task_data'] = str(task_data)
        if validation_rule:
            details['validation_rule'] = validation_rule
        kwargs['details'] = details
        super().__init__(message, **kwargs)
        self.task_data = task_data
        self.validation_rule = validation_rule


class ConfigurationError(TaskSchedulingError):
    """
    配置错误异常
    
    当配置参数错误或缺失时抛出此异常
    """
    
    def __init__(self, message: str, config_key: Optional[str] = None, 
                 config_value: Optional[Any] = None, **kwargs):
        """
        初始化配置错误异常
        
        Args:
            message: 错误消息
            config_key: 配置键名
            config_value: 配置值
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = str(config_value)
        kwargs['details'] = details
        super().__init__(message, **kwargs)
        self.config_key = config_key
        self.config_value = config_value


class ResourceManagementError(TaskSchedulingError):
    """
    资源管理异常
    
    当资源管理失败时抛出此异常，包括连接池耗尽、资源释放失败等
    """
    
    def __init__(self, message: str, resource_type: Optional[str] = None, 
                 resource_id: Optional[str] = None, **kwargs):
        """
        初始化资源管理异常
        
        Args:
            message: 错误消息
            resource_type: 资源类型
            resource_id: 资源标识
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id:
            details['resource_id'] = resource_id
        kwargs['details'] = details
        super().__init__(message, **kwargs)
        self.resource_type = resource_type
        self.resource_id = resource_id
