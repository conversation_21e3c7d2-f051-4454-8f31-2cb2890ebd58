# CPQuery RPA Scraper

这是一个使用 `asyncio` 和 `Playwright` 实现的，用于获取cpquery专利数据的并行爬虫。

ahthor：wwind
date: 2023.04.05
refactor by: Jules
date: 2025.08.07

## 🚀 项目概述

本项目最初由一系列独立的`corutine_*.py`脚本构成。经过本次重构，项目已经升级为一个结构清晰、模块化的Python应用程序。新的架构将不同的功能（如浏览器管理、数据抓取、数据处理、队列管理）分离到独立的组件中，极大地提升了代码的可维护性、可测试性和可扩展性。

## 🏗️ 项目架构

重构后的所有源代码都位于 `src/cpquery_scraper` 目录下，形成了一个标准的Python包。

```
cpquery_scraper/
├── main.py                # 应用程序的启动入口
└── src/
    └── cpquery_scraper/
        ├── __init__.py
        ├── app.py                 # App类，负责协调和运行整个爬虫应用
        ├── config.py              # 全局配置文件
        ├── core/                  # 核心框架组件
        │   ├── asset_processor.py   # 静态资源（如PDF）处理器
        │   └── browser_manager.py # Playwright浏览器和上下文管理器
        ├── modules/               # 核心业务逻辑模块
        │   ├── auth/              # 用户认证模块
        │   └── clicker/           # 页面点击与交互模块
        ├── pipelines/             # 数据处理管道
        │   ├── base_pipeline.py
        │   ├── mysql_pipeline.py  # MySQL存储管道
        │   └── redis_pipeline.py  # Redis缓存管道
        ├── spiders/               # 爬虫逻辑
        │   └── patent_spider.py   # 专利信息爬虫
        ├── queuing/               # 任务队列管理
        │   └── ...
        └── utils/                 # 通用工具模块
            ├── db.py
            ├── formatters.py
            ├── helpers.py
            └── logger.py

tests/                         # 测试目录
├── test_app.py                # 应用集成测试
└── ...
```

## 🛠️ 安装与设置

1.  **克隆仓库**:
    ```bash
    git clone <repository_url>
    cd cpquery_scraper
    ```

2.  **创建虚拟环境** (推荐):
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
    ```

3.  **安装依赖**:
    项目的所有依赖都已整合到`requirements.txt`中。
    ```bash
    pip install -r requirements.txt
    ```

4.  **安装Playwright浏览器驱动**:
    这是Playwright运行所必需的。
    ```bash
    playwright install
    ```

## ධ 如何运行

### 运行爬虫

通过项目根目录下的 `main.py` 文件来启动整个应用程序：

```bash
python main.py
```

### 运行模式

你可以在 `src/cpquery_scraper/config.py` 文件中修改 `ROLE` 配置来切换运行模式：
*   **`Role.MAIN`**: 主节点模式。负责从MySQL生成任务、执行爬取并将结果最终存入MySQL。
*   **`Role.DISTRIBUTED`**: 分布式节点模式。只从Redis获取任务，并将结果写入Redis缓存。

### 运行测试

我们使用 `pytest` 进行测试。在项目根目录下运行：

```bash
pytest
```

---

## 📜 版本功能变更记录 (Changelog)

> 20230809：  v1.1
> 
>             new: 
> 
>             增加Redis作为任务来源，一旦获取的任务即从redis删去，为分布式运行做好准备；
> 
> 20230810：  v1.2
> 
>             bugfix:
> 
>             修复页面刷新失败后导致的页面重置无限循环，解决了日志因此容量暴增的问题；
> 
>             但怀疑导致该问题的根源（页面刷新不成功，可能需要页面重置）消除不了，需要后续继续测试；
> 
>             如果目前的措施会导致后续任务一直失败，则需要尝试进行完整的页面重置（spider函数中的data_page_init函数引入click过程）。
> 
> 20230814:   v1.3
> 
>             bugfix:
> 
>             修改用户使用到期结束逻辑，以克服Reddis客户端不及时释放导致的无法结束bug；
> 
>             change:
> 
>             1.修改后的用户到期结束逻辑为：当用户使用时限到期时，重置全局标志变量FLAG_SPIDER_RUNNING: bool = False，
> 
>             然后消耗完任务队列中的剩余任务，再等待结果队列中的数据写入数据库，再结束爬取协程。
> 
>             2.修改日志部分逻辑，取消mysql客户端和redis客户端类的单独日志名称，统一把日志名称归于所在模块的名称。
> 
> 20230816:   v1.4
> 
>             change:
> 
>             1.修正登录模块中check_login_page_is_logined逻辑（去掉迭代，增加None返回值）；
> 
>             2.修正get_page_logined函数逻辑，增加处理check_login_page_is_logined返回None的情形，
> 
>             3.增加重试次数过度自动延时功能，修改部分日志文字。
> 
> 20230908:   V1.41
> 
>             change:
> 
>             修正部分日志文字表述，更精简
> 
> 20230914:   V1.42
> 
>             bugfix:
> 
>             trans_format函数，当获取不到一个申请号所有的数据时，应返回None（之前不会返回None）；
> 
>             修正对应的代码为：if k=="patent_id" or k=="__time":
> 
> 20230915:   V1.43
> 
>             bugfix:
> 
>             修正trans_format函数逻辑，只有同时获取到著录项目数据（专利名称+申请日）和费用数据，才视为有效数据，写入数据库保存，
> 
>             否则抛弃掉
> 
> 20230925：  V1.44
> 
>             bugfix:
> 
>             增加浏览页页面崩溃或被关闭时，直接退出当前任务的逻辑，避免程序一直重试（页面刷新）
> 
> 20231007:   V1.45
> 
>             bugfix：
> 
>             当mysql任务表中没有数据时，程序会卡死在任务补充循环中（一直尝试从mysql服务器向redis补充任务），
> 
>             从而导致现存任务队列的任务执行和结果队列数据落盘。
> 
> 20231008:   V1.5
> 
>             NEW:
> 
>             登录cpquery系统的用户信息，增加从配置文件（user_config.json）获取的方式，由全局变量开关控制（USER_INFO_SOURCE_FROM_FILE）。
> 
>             change:
> 
>             优化部分日志文字，减少生产运行时的敏感信息暴露，但保留原有代码（注释掉未删除）用于debug。
> 
> 20231009：  V1.51
> 
>             bugfix:
> 
>             1.结果队列数据写入mysql时，因（appl_first）字段超长出错，会导致不断重试（重试前未关闭现有连接）而耗尽mysql服务器连接。
> 
>             修正bug，在重试前先关闭当前连接，重试时再开启新的连接。
> 
>             2.v1.5版本调试时改变了redis表名，修正回原来的redis表名。
> 
>             change:
> 
>             优化部分日志文字。
> 
> 20231028：  V1.52
> 
>             bugfix:
> 
>             1.用户密码错误时，程序会卡死在重新登录界面.
> 
>             change:
> 
>             2.优化部分user_login的日志文字。
> 
> 20231114：  V1.53
> 
>             bugfix:
> 
>             1.页面提示验证码错误时会被误识别为用户密码错；
> 
>             2.日志可能会暴露用户密码。
> 
>             change:
> 
>             1.发送验证码服务器的请求数据格式优化；
> 
>             2.登陆页面拖动滑块逻辑改为和实际一致。
> 
> 20231225:   V1.54
> 
>             change:
> 
>             1.浏览器context初始化时，增加一个参数：ignore_https_errors=True,忽略服务器证书错误
> 
> 20240722:   V1.55
> 
>             bugfix:
> 
>             1.修复了如果查询页有通知框，会导致程序卡死的bug（获取通知的内容时，不能准确定位locator）。
> 
> 20240822:   V1.56
> 
>             change:
> 
>             1.输出的数据增加了intclass、ipc_first字段。
> 
> 20241224：  V1.57
> 
>             bugfix:
> 
>             1.修复了当前用户密码错误时，页面输入框识别错误（识别不出法人登陆页面的输入框），导致程序卡死。
> 
>             change:
> 
>             1.登陆页面输入框识别优化，从单一识别逻辑，改为多个locators识别，以适应不同页面的输入框。
> 
>             增加的代码如下：
> 
>             '''
> 
>             phone_or_id_locator = page.locator("input[placeholder='手机号/证件号码']")
> 
>             credit_code_locator = page.locator("input[placeholder='统一社会信用代码']")
> 
>             agency_code_locator = page.locator("input[placeholder='代理机构代码']")
> 
>             combined_locator = phone_or_id_locator.or_(credit_code_locator).or_(agency_code_locator)
> 
>             '''
> 
> 20241225：  V1.571
> 
>             bugfix:
> 
>             1.修复了点击登录按钮失败时，导致程序卡死。
> 
> 20250114:   V1.58
> 
>             bugfix:
> 
>             1.修复了任务队列有时不能正常清空，导致爬取协程不能退出，从而程序僵死的bug。
> 
>             change:
> 
>             1.优化了系统刷失败重试的日志文本，更为清晰。
> 
> 20250115:   V1.581
> 
>             bugfix:
> 
>             1.修复了远程验证码服务器不可用时，不能返回默认偏差值的bug，增强了鲁棒性。
> 
> 20250307:   V1.60
> 
>             NEW:
> 
>             1.配置文件增加了程序运行角色（role）选项，用于区分不同程序运行的角色：
> 
>                 - main：部署在固定IP出口的节点上，能够：获取数据 + 写入缓存 + 写入Mysql数据库；
> 
>                 - distributed：部署在分布式节点上，能够：获取数据 + 写入缓存.
> 
>             2.增加了结果数据处理缓存逻辑：结果数据先定期（60秒）写入Redis缓存，然后再定期（30分钟）从Redis批量写入mysql数据库。
> 
> 20250308:   V1.61
> 
>             change:
> 
>             1.改变了任务调度程序：当ROLE为distributed时，不再承担从Mysql调取任务写入到Redis的职责(该职责由ROLE为main的节点承担)。
> 
>             bugfix:
> 
>             1.修复了trans_format函数中，国际分类号（intclass）的生成逻辑中，当主分类或副分类为None时，运算符“+”的报错；
> 
>                 改为：两者任一为None时，则作为空值（""）处理。
> 
> 20250419：  V1.62
> 
>             bugfix:
> 
>             1.修复了ROLE为MAIN时，把爬取结果数据从Redis批量写入mysql数据库时，写入成功的数据，没有在mysql的任务表中更新状态的bug。
> 
> 20250625:   V1.63
> 
>             change:
> 
>             1.重构了corutine_config.py,增加了配置类，同步修改全部模块的配置引用
> 
> 20250702：  V1.64
> 
>             bugfix:
> 
>             1.类型错误
> 
>             2.userlogin函数中,拖动验证码过程中，服务器返回不可识别的其他类型错误时，重试5次后退出登录循环。
> 
>             change:
> 
>             1.源网站应缴费信息、滞纳金信息格式有变化（没有数据时为Null）,修改trans_format函数，兼容该变化。
> 
> 
> 
> 20250703：  V1.65
> 
>             NEW:
> 
>             1.重构登录模块：将corutine_user_login.py重构为模块化的auth包
> 
>             2.按照Python最佳实践重新设计异常类体系
> 
>             3.提供完全向后兼容的接口，支持无缝切换
> 
>             4.增加详细的文档、测试和使用示例
> 
>             change:
> 
>             1.登录模块现在支持更好的错误处理和调试信息
> 
>             2.提供三种切换方案，推荐使用别名导入实现零代码修改
> 
>             3.所有测试、文档和示例都整理到auth目录下
> 
> 
> 
> 20250703：  V1.66
> 
>             NEW:
> 
>             1.重构点击模块：将corutine_click.py重构为模块化的click包
> 
>             2.按照Python最佳实践重新设计模块架构和异常处理体系
> 
>             3.提供完全向后兼容的接口，支持无缝替换现有模块
> 
>             4.修复类型注解错误，统一使用asyncio.Future替代concurrent.futures.Future
> 
>             5.增加详细的文档、测试和验证脚本
> 
>             change:
> 
>             1.点击模块现在采用职责分离的设计：查询管理器、数据提取器、文件处理器、同步点击管理器
> 
>             2.强化异常处理，异常类包含详细的业务上下文和错误信息
> 
>             3.保持100%业务逻辑一致性，所有核心功能、重试机制、错误处理完全保持原有行为
> 
>             4.支持三种导入方式，推荐使用别名导入实现零代码修改
> 
>             5.所有测试、文档和验证脚本都整理到click目录下
> 
> 20250715:  V1.67
> 
>             bugfix:
> 
>             1.修复了DISTRIBUTED角色在Redis无任务时，会阻塞协程60秒的bug。
> 
>             change:
> 
>             1.优化了任务调度程序：当ROLE为DISTRIBUTED时，遇到Redis无任务时，直接返回None，不再阻塞协程60秒。
> 
>             2.优化了日志输出，当Redis无任务时，不再输出重复的日志信息。
> 
> 20250716:   V1.671
> 
>             change:
> 
>             1.改变了页面重试的断言逻辑，先判断标志url（“/detail/index?zhuanlisqh”）是否在detail_page.url中，重试超次数后再进行断言。
> 
> 20250718：  V1.672
> 
>             change:
> 
>             1._login_worker_context函数，失败的话重新调用_login_worker_context函数，无限次重试。
> 
---

## 🗺️ RoadMap

todo : 日志文件保存逻辑优化，尽量每次运行生成一个文件。
todo : 多进程嵌套，多个playwright实例，每个实例多个页面
todo ：尝试在linux上直接部署和docker部署
tddo : 使用代理