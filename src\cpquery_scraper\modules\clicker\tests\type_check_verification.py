#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类型注解验证脚本
验证重构后的click模块类型注解正确性
"""
import os
import sys
import ast
import re
from typing import List, Dict, Set

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)


class TypeAnnotationChecker:
    """类型注解检查器"""
    
    def __init__(self):
        self.click_dir = os.path.dirname(os.path.dirname(__file__))
        self.issues = []
    
    def check_future_imports(self) -> bool:
        """检查Future类型的导入"""
        print("=== 检查Future类型导入 ===")
        
        python_files = []
        for root, dirs, files in os.walk(self.click_dir):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        has_issues = False
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, self.click_dir)

            # 跳过测试文件本身
            if 'type_check_verification.py' in rel_path:
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查是否有concurrent.futures.Future导入
                if 'concurrent.futures' in content:
                    print(f"  [FAIL] {rel_path} 仍然导入 concurrent.futures")
                    has_issues = True
                elif 'from concurrent' in content:
                    print(f"  [FAIL] {rel_path} 仍然从 concurrent 模块导入")
                    has_issues = True
                else:
                    print(f"  [OK] {rel_path} 无 concurrent.futures 导入")
                    
            except Exception as e:
                print(f"  [ERROR] {rel_path} 检查失败: {e}")
                has_issues = True
        
        return not has_issues
    
    def check_future_annotations(self) -> bool:
        """检查Future类型注解"""
        print("\n=== 检查Future类型注解 ===")
        
        python_files = []
        for root, dirs, files in os.walk(self.click_dir):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        has_issues = False
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, self.click_dir)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查Future类型注解
                future_patterns = [
                    r':\s*Optional\[Future\[',
                    r':\s*Future\[',
                    r'->\s*Optional\[Future\[',
                    r'->\s*Future\['
                ]
                
                found_issues = []
                for pattern in future_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        found_issues.extend(matches)
                
                if found_issues:
                    print(f"  [FAIL] {rel_path} 发现未修复的Future注解: {found_issues}")
                    has_issues = True
                else:
                    # 检查是否有正确的asyncio.Future注解
                    asyncio_future_patterns = [
                        r':\s*Optional\[asyncio\.Future\[',
                        r':\s*asyncio\.Future\[',
                        r'->\s*Optional\[asyncio\.Future\[',
                        r'->\s*asyncio\.Future\['
                    ]
                    
                    found_correct = []
                    for pattern in asyncio_future_patterns:
                        matches = re.findall(pattern, content)
                        if matches:
                            found_correct.extend(matches)
                    
                    if found_correct:
                        print(f"  [OK] {rel_path} 使用正确的asyncio.Future注解: {len(found_correct)} 处")
                    else:
                        print(f"  [INFO] {rel_path} 无Future类型注解")
                    
            except Exception as e:
                print(f"  [ERROR] {rel_path} 检查失败: {e}")
                has_issues = True
        
        return not has_issues
    
    def check_syntax_validity(self) -> bool:
        """检查语法有效性"""
        print("\n=== 检查语法有效性 ===")
        
        python_files = []
        for root, dirs, files in os.walk(self.click_dir):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        has_issues = False
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, self.click_dir)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 尝试解析AST
                ast.parse(content)
                print(f"  [OK] {rel_path} 语法正确")
                
            except SyntaxError as e:
                print(f"  [FAIL] {rel_path} 语法错误: {e}")
                has_issues = True
            except Exception as e:
                print(f"  [ERROR] {rel_path} 检查失败: {e}")
                has_issues = True
        
        return not has_issues
    
    def check_import_consistency(self) -> bool:
        """检查导入一致性"""
        print("\n=== 检查导入一致性 ===")
        
        # 检查__init__.py的导入
        init_file = os.path.join(self.click_dir, "__init__.py")
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查必要的导入
            required_imports = [
                'import asyncio',
                'from typing import',
                'from playwright.async_api import Page'
            ]
            
            missing_imports = []
            for imp in required_imports:
                if imp not in content:
                    missing_imports.append(imp)
            
            if missing_imports:
                print(f"  [FAIL] __init__.py 缺少必要导入: {missing_imports}")
                return False
            else:
                print(f"  [OK] __init__.py 导入完整")
                
            # 检查不应该存在的导入
            forbidden_imports = [
                'from concurrent.futures import',
                'import concurrent.futures'
            ]
            
            found_forbidden = []
            for imp in forbidden_imports:
                if imp in content:
                    found_forbidden.append(imp)
            
            if found_forbidden:
                print(f"  [FAIL] __init__.py 包含禁止的导入: {found_forbidden}")
                return False
            else:
                print(f"  [OK] __init__.py 无禁止导入")
                return True
                
        except Exception as e:
            print(f"  [ERROR] 检查__init__.py失败: {e}")
            return False
    
    def run_all_checks(self) -> bool:
        """运行所有检查"""
        print("开始类型注解验证...")
        print("=" * 60)
        
        checks = [
            ("Future导入检查", self.check_future_imports),
            ("Future注解检查", self.check_future_annotations),
            ("语法有效性检查", self.check_syntax_validity),
            ("导入一致性检查", self.check_import_consistency),
        ]
        
        results = []
        for name, check_func in checks:
            try:
                result = check_func()
                results.append((name, result))
            except Exception as e:
                print(f"检查 {name} 时发生错误: {e}")
                results.append((name, False))
        
        # 输出结果汇总
        print("\n" + "=" * 60)
        print("类型注解验证结果汇总:")
        passed = 0
        total = len(results)
        
        for name, result in results:
            status = "[PASS] 通过" if result else "[FAIL] 失败"
            print(f"{status} {name}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 检查通过")
        success_rate = (passed / total) * 100
        print(f"成功率: {success_rate:.1f}%")
        
        if passed == total:
            print("\n🎉 [SUCCESS] 所有类型注解检查通过！")
            print("✅ 类型提示错误已修复，可以正常使用")
            return True
        else:
            print(f"\n[WARNING] 有 {total-passed} 个检查失败")
            return False


def main():
    """主函数"""
    checker = TypeAnnotationChecker()
    success = checker.run_all_checks()
    return success


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查执行失败: {e}")
        sys.exit(1)
