# Click模块 - 页面点击和数据提取模块

## 📖 概述

Click模块是一个专门用于网页自动化点击操作和数据提取的Python模块。它提供了完整的页面交互功能，包括查询、点击、数据提取和文件处理等操作。

### 🎯 主要功能

- **主页查询管理** - 在cpquery主页执行查询操作
- **页面跳转控制** - 处理页面弹出和跳转逻辑
- **数据提取引擎** - 从页面提取各种类型的数据
- **文件处理系统** - 处理文件点击和附件获取
- **异常处理机制** - 完善的错误处理和重试逻辑

### ✨ 核心优势

- **100%向后兼容** - 与原corutine_click.py模块完全兼容
- **模块化设计** - 清晰的职责分离，易于维护和扩展
- **强化异常处理** - 详细的异常信息和错误上下文
- **智能重试机制** - 自动处理网络异常和页面问题
- **完善的日志记录** - 详细的操作日志和调试信息

## 🚀 快速开始

### 安装要求

```python
# 依赖项
- Python 3.7+
- playwright
- asyncio
- corutine_config (项目配置模块)
- corutine_utility (项目工具模块)
```

### 基本使用

#### 方式1：直接替换导入（推荐）

```python
# 原来的代码
from corutine_click import main_page_query_an, main_page_click_an, get_appl_data

# 只需修改为
from click import main_page_query_an, main_page_click_an, get_appl_data
# 其他代码完全不变！
```

#### 方式2：模块导入

```python
import click

# 使用模块前缀调用
result = await click.main_page_query_an(name, page, an)
detail_page = await click.main_page_click_an(name, page, an)
```

#### 方式3：别名导入（无缝替换）

```python
# 使用别名，原有代码完全不需要修改！
import click as corutine_click

# 原有代码保持不变
result = await corutine_click.main_page_query_an(name, page, an)
detail_page = await corutine_click.main_page_click_an(name, page, an)
```

## 📋 核心API

### 主页查询功能

#### `main_page_query_an(name, query_page, an, retry_times=0)`

在cpquery主页查询页面执行查询操作。

**参数：**
- `name` (str): 任务名称
- `query_page` (Page): 查询页面对象
- `an` (str): 申请号
- `retry_times` (int): 重试次数，默认0

**返回：**
- `dict`: 查询结果，包含code、data、msg字段

**示例：**
```python
result = await main_page_query_an("task1", query_page, "2023123456789")
if result["code"] == 200:
    print("查询成功:", result["data"])
```

#### `main_page_click_an(name, query_page, an, retry_times=0)`

点击查询结果，获取数据详情页面。

**参数：**
- `name` (str): 任务名称
- `query_page` (Page): 查询页面对象
- `an` (str): 申请号
- `retry_times` (int): 重试次数，默认0

**返回：**
- `Page`: 数据详情页面对象

**示例：**
```python
detail_page = await main_page_click_an("task1", query_page, "2023123456789")
print(f"详情页URL: {detail_page.url}")
```

### 数据提取功能

#### `get_appl_data(name, data_page, an, fut=None, retry_times=0)`

从数据页面提取申请信息。

**参数：**
- `name` (str): 任务名称
- `data_page` (Page): 数据页面对象
- `an` (str): 申请号
- `fut` (Future, optional): 异步Future对象
- `retry_times` (int): 重试次数，默认0

**返回：**
- `Future`: 包含申请信息的Future对象

**示例：**
```python
future = await get_appl_data("task1", data_page, "2023123456789")
appl_data = future.result()
```

#### `click_event_button_and_get_data_sync(name, data_page, an, url_init_page)`

同步模式：逐个点击事件按钮并获取数据。

**参数：**
- `name` (str): 任务名称
- `data_page` (Page): 数据页面对象
- `an` (str): 申请号
- `url_init_page` (str): 初始化页面URL

**返回：**
- `list`: 事件数据列表

**示例：**
```python
events_data = await click_event_button_and_get_data_sync(
    "task1", data_page, "2023123456789", init_url
)
for event_data in events_data:
    print(f"事件数据: {event_data}")
```

### 新增便利功能

#### `get_page_with_retry(name, query_page, an, max_retries=3)`

获取数据页面，带智能重试机制。

**参数：**
- `name` (str): 任务名称
- `query_page` (Page): 查询页面对象
- `an` (str): 申请号
- `max_retries` (int): 最大重试次数，默认3

**返回：**
- `Page`: 数据详情页面对象

**示例：**
```python
try:
    detail_page = await get_page_with_retry("task1", query_page, "2023123456789")
    print("页面获取成功")
except QueryError as e:
    print(f"查询失败: {e}")
```

#### `check_file_permission(file_info)`

检查文件访问权限。

**参数：**
- `file_info` (dict): 文件信息字典

**返回：**
- `bool`: 是否有权限访问文件

**示例：**
```python
if check_file_permission(file_info):
    print("有权限访问文件")
else:
    print("无权限访问文件")
```

## 🔧 配置选项

模块使用`corutine_config.config`进行配置，主要配置项包括：

```python
# 重试次数配置
MAX_DATA_PAGE_CLICK_RETRY_TIMES = 3
MAX_DATA_PAGE_INIT_RETRY_TIMES = 3
MAX_DATA_PAGE_REFRESH_RETRY_TIMES = 3

# 超时配置
WAIT_RESPONSE_TIME_MILLISEC_ASYNC = 30000
WAIT_RESPONSE_TIME_MILLISEC_SYNC = 30000
WAIT_RESPONSE_TIME_MILLISEC_REFRESH = 30000

# 数据范围配置
DATA_SCOPE_MASK = {
    "申请信息": True,
    "费用信息": True,
    "发文信息": True,
    "公告信息": True,
    "审查信息": True
}

# 其他配置
SLEEP_TIME_SEC_ASYNC = 2
TEST_MODE = False
```

## 🚨 异常处理

模块提供了完善的异常处理机制：

### 主要异常类

- **`ClickError`** - 基础异常类
- **`QueryError`** - 查询操作异常
- **`PageInitError`** - 页面初始化异常
- **`DataExtractionError`** - 数据提取异常
- **`FileAccessError`** - 文件访问异常
- **`ResponseTimeoutError`** - 响应超时异常
- **`PageCrashedError`** - 页面崩溃异常

### 异常处理示例

```python
from click import main_page_query_an, QueryError, PageCrashedError

try:
    result = await main_page_query_an("task1", page, "2023123456789")
except QueryError as e:
    print(f"查询失败: {e}")
    print(f"错误码: {e.error_code}")
    print(f"申请号: {e.an}")
except PageCrashedError as e:
    print(f"页面崩溃: {e}")
    print(f"崩溃原因: {e.crash_reason}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 📊 性能特性

- **智能重试** - 自动处理临时网络问题
- **并发支持** - 支持异步和同步两种模式
- **内存优化** - 及时清理临时数据和页面对象
- **错误恢复** - 自动从页面崩溃和网络异常中恢复

## 🧪 测试验证

运行测试以验证模块功能：

```bash
# 运行所有测试
python click/tests/run_all_tests.py

# 运行兼容性测试
python click/tests/test_click_compatibility.py

# 运行异常测试
python click/tests/test_exceptions.py
```

## 📚 更多文档

- [迁移指南](MIGRATION_GUIDE.md) - 详细的迁移步骤
- [异常处理指南](EXCEPTION_CLASSES_GUIDE.md) - 异常处理最佳实践
- [重构报告](CLICK_MODULE_REFACTOR_REPORT.md) - 技术细节和设计决策

## 🎉 总结

重构后的Click模块提供了：

- ✅ **完全兼容** - 无需修改现有代码
- ✅ **更好的错误处理** - 详细的异常信息
- ✅ **模块化设计** - 易于维护和扩展
- ✅ **完善的测试** - 确保质量和稳定性
- ✅ **详细的文档** - 完整的使用指南

立即开始使用重构后的Click模块，享受更好的开发体验！
