#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Click模块简单验证脚本
验证重构后的模块结构和业务逻辑一致性
"""
import os
import sys
import inspect
import re
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)


class ClickModuleVerifier:
    """Click模块验证器"""
    
    def __init__(self):
        self.click_dir = os.path.dirname(os.path.dirname(__file__))
        self.original_file = os.path.join(project_root, "corutine_click.py")
        self.results = []
    
    def verify_file_structure(self) -> bool:
        """验证文件结构"""
        print("=== 验证文件结构 ===")
        
        required_files = [
            "__init__.py",
            "exceptions.py", 
            "query_manager.py",
            "data_extractor.py",
            "file_handler.py",
            "sync_click_manager.py"
        ]
        
        all_present = True
        for file_name in required_files:
            file_path = os.path.join(self.click_dir, file_name)
            if os.path.exists(file_path):
                print(f"  [OK] {file_name} 存在")
            else:
                print(f"  [FAIL] {file_name} 不存在")
                all_present = False
        
        return all_present
    
    def verify_utf8_encoding(self) -> bool:
        """验证UTF-8编码"""
        print("\n=== 验证UTF-8编码 ===")
        
        python_files = []
        for root, dirs, files in os.walk(self.click_dir):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        all_utf8 = True
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 检查是否包含中文字符
                    if re.search(r'[\u4e00-\u9fff]', content):
                        print(f"  [OK] {os.path.relpath(file_path, self.click_dir)} 包含中文字符且UTF-8编码正常")
                    else:
                        print(f"  [INFO] {os.path.relpath(file_path, self.click_dir)} 无中文字符")
            except UnicodeDecodeError:
                print(f"  [FAIL] {os.path.relpath(file_path, self.click_dir)} UTF-8编码错误")
                all_utf8 = False
            except Exception as e:
                print(f"  [ERROR] {os.path.relpath(file_path, self.click_dir)} 读取失败: {e}")
                all_utf8 = False
        
        return all_utf8
    
    def verify_function_signatures(self) -> bool:
        """验证函数签名"""
        print("\n=== 验证函数签名 ===")
        
        # 检查__init__.py中的导出函数
        init_file = os.path.join(self.click_dir, "__init__.py")
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找核心函数定义
            core_functions = [
                'main_page_query_an',
                'main_page_click_an', 
                'get_appl_data',
                'click_event_button_and_get_data',
                'click_event_button_and_get_data_sync'
            ]
            
            all_found = True
            for func_name in core_functions:
                if f"async def {func_name}" in content or f"def {func_name}" in content:
                    print(f"  [OK] {func_name} 函数定义存在")
                else:
                    print(f"  [FAIL] {func_name} 函数定义不存在")
                    all_found = False
            
            return all_found
            
        except Exception as e:
            print(f"  [ERROR] 读取__init__.py失败: {e}")
            return False
    
    def verify_business_logic_consistency(self) -> bool:
        """验证业务逻辑一致性"""
        print("\n=== 验证业务逻辑一致性 ===")
        
        # 检查关键业务逻辑字符串
        key_logic_patterns = [
            r"查询页查询.*次后失败",
            r"数据页面初始化失败",
            r"当前用户没有权限查看该实体文件",
            r"浏览器意外关闭",
            r"页面已崩溃",
            r"重试第.*次"
        ]
        
        consistency_check = True
        
        # 检查各个模块文件
        module_files = [
            "query_manager.py",
            "data_extractor.py", 
            "file_handler.py",
            "sync_click_manager.py"
        ]
        
        for module_file in module_files:
            file_path = os.path.join(self.click_dir, module_file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                found_patterns = 0
                for pattern in key_logic_patterns:
                    if re.search(pattern, content):
                        found_patterns += 1
                
                if found_patterns > 0:
                    print(f"  [OK] {module_file} 包含 {found_patterns} 个关键业务逻辑模式")
                else:
                    print(f"  [INFO] {module_file} 未包含关键业务逻辑模式")
                    
            except Exception as e:
                print(f"  [ERROR] 检查 {module_file} 失败: {e}")
                consistency_check = False
        
        return consistency_check
    
    def verify_exception_handling(self) -> bool:
        """验证异常处理"""
        print("\n=== 验证异常处理 ===")
        
        exceptions_file = os.path.join(self.click_dir, "exceptions.py")
        
        try:
            with open(exceptions_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查异常类定义
            expected_exceptions = [
                'ClickError',
                'QueryError',
                'PageInitError', 
                'DataExtractionError',
                'FileAccessError',
                'RouteInterceptError',
                'ResponseTimeoutError',
                'PageCrashedError',
                'RetryExhaustedError'
            ]
            
            all_defined = True
            for exc_name in expected_exceptions:
                if f"class {exc_name}" in content:
                    print(f"  [OK] {exc_name} 异常类已定义")
                else:
                    print(f"  [FAIL] {exc_name} 异常类未定义")
                    all_defined = False
            
            # 检查异常类是否有实际实现
            if "def __init__" in content and "def __str__" in content:
                print("  [OK] 异常类有具体实现")
            else:
                print("  [FAIL] 异常类缺少具体实现")
                all_defined = False
            
            return all_defined
            
        except Exception as e:
            print(f"  [ERROR] 检查异常处理失败: {e}")
            return False
    
    def verify_import_compatibility(self) -> bool:
        """验证导入兼容性"""
        print("\n=== 验证导入兼容性 ===")
        
        init_file = os.path.join(self.click_dir, "__init__.py")
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查__all__导出列表
            if "__all__ = [" in content:
                print("  [OK] __all__ 导出列表存在")
                
                # 提取__all__列表内容
                all_match = re.search(r"__all__ = \[(.*?)\]", content, re.DOTALL)
                if all_match:
                    all_content = all_match.group(1)
                    exported_items = re.findall(r"'([^']+)'", all_content)
                    print(f"  [INFO] 导出 {len(exported_items)} 个项目")
                    
                    # 检查核心函数是否在导出列表中
                    core_functions = [
                        'main_page_query_an',
                        'main_page_click_an',
                        'get_appl_data',
                        'click_event_button_and_get_data',
                        'click_event_button_and_get_data_sync'
                    ]
                    
                    missing_exports = []
                    for func in core_functions:
                        if func not in exported_items:
                            missing_exports.append(func)
                    
                    if not missing_exports:
                        print("  [OK] 所有核心函数都在导出列表中")
                        return True
                    else:
                        print(f"  [FAIL] 缺少导出: {missing_exports}")
                        return False
                else:
                    print("  [FAIL] 无法解析__all__列表内容")
                    return False
            else:
                print("  [FAIL] __all__ 导出列表不存在")
                return False
                
        except Exception as e:
            print(f"  [ERROR] 检查导入兼容性失败: {e}")
            return False
    
    def run_all_verifications(self) -> bool:
        """运行所有验证"""
        print("开始Click模块验证...")
        print("=" * 60)
        
        verifications = [
            ("文件结构", self.verify_file_structure),
            ("UTF-8编码", self.verify_utf8_encoding),
            ("函数签名", self.verify_function_signatures),
            ("业务逻辑一致性", self.verify_business_logic_consistency),
            ("异常处理", self.verify_exception_handling),
            ("导入兼容性", self.verify_import_compatibility),
        ]
        
        results = []
        for name, verification_func in verifications:
            try:
                result = verification_func()
                results.append((name, result))
            except Exception as e:
                print(f"验证 {name} 时发生错误: {e}")
                results.append((name, False))
        
        # 输出结果汇总
        print("\n" + "=" * 60)
        print("验证结果汇总:")
        passed = 0
        total = len(results)
        
        for name, result in results:
            status = "[PASS] 通过" if result else "[FAIL] 失败"
            print(f"{status} {name}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 验证通过")
        success_rate = (passed / total) * 100
        print(f"成功率: {success_rate:.1f}%")
        
        if passed == total:
            print("\n[SUCCESS] 所有验证通过！Click模块重构符合要求！")
            return True
        else:
            print(f"\n[WARNING] 有 {total-passed} 个验证失败，请检查相关问题")
            return False


def main():
    """主函数"""
    verifier = ClickModuleVerifier()
    success = verifier.run_all_verifications()
    return success


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n验证被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n验证执行失败: {e}")
        sys.exit(1)
