import time
import asyncio
from datetime import datetime

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.db import RedisConnection, MysqlConnection
from src.cpquery_scraper.utils.logger import get_logger
from .base_pipeline import BasePipeline

class MySQLPipeline(BasePipeline):
    """
    一个定期将数据从 Redis 同步到 MySQL 的管道。
    此管道依赖于角色，并且仅在“主”节点上运行。
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.redis_conn = RedisConnection()
        self.mysql_conn = MysqlConnection()
        self.redis_key_prefix = "patent_id:"
        self.redis_index_key = "patent_index"
        self.is_main_role = (config.ROLE == config.Role.MAIN)

    async def open_spider(self, spider):
        if self.is_main_role:
            self.logger.info("MySQL 管道已打开（角色：MAIN）。")
        else:
            self.logger.info("MySQL 管道已打开（角色：DISTRIBUTED，同步已禁用）。")

    async def close_spider(self, spider):
        if self.is_main_role:
            self.logger.info("MySQL 管道已关闭。")

    async def process_item(self, item, spider):
        # 此管道不单独处理项目。
        # 它按计划工作，分批同步。
        return item

    async def run_sync_task(self):
        """
        将数据从 Redis 同步到 MySQL 的核心逻辑。
        此方法旨在由应用程序运行程序按计划调用。
        """
        if not self.is_main_role:
            return

        self.logger.info("开始执行 Redis → MySQL 批量同步任务。")

        try:
            self.redis_conn.connect_redis(db=2)

            patent_ids_bytes = await asyncio.to_thread(self.redis_conn.smembers, self.redis_index_key)
            patent_ids = list(patent_ids_bytes) if patent_ids_bytes else []

            if not patent_ids:
                self.logger.info("Redis 中暂无可同步的数据。")
                return

            start_time = time.time()
            batch_data = []
            synced_ids = []

            # 假设所有项目都具有相同的结构，从第一个项目中获取列
            sample_key = f"{self.redis_key_prefix}{patent_ids[0]}"
            all_fields = await asyncio.to_thread(self.redis_conn.hkeys, sample_key)
            cols = [field for field in all_fields if field not in ('synced', 'timestamp')]

            if not cols:
                self.logger.warning("无法从 Redis 项中确定列名。")
                return

            sql_insert_or_update_batch = f"""
            INSERT INTO {config.DB_RESULT_TABLE_NAME} ({', '.join(f'`{col}`' for col in cols)})
            VALUES ({', '.join(['%s'] * len(cols))})
            ON DUPLICATE KEY UPDATE
            {', '.join([f'`{col}`=VALUES(`{col}`)' for col in cols if col != 'patent_id'])}
            """

            for patent_id in patent_ids:
                redis_key = f"{self.redis_key_prefix}{patent_id}"
                sync_status = await asyncio.to_thread(self.redis_conn.hget, redis_key, "synced")
                if sync_status == "1":
                    continue

                all_data = await asyncio.to_thread(self.redis_conn.hgetall, redis_key)
                if not all_data.get('patent_id'):
                    self.logger.warning(f"Patent {patent_id} lacks necessary data.")
                    continue

                batch_data.append(tuple(all_data.get(col) for col in cols))
                synced_ids.append(patent_id)

            if not batch_data:
                self.logger.info("暂无新的数据需要同步到 MySQL。")
                return

            self.logger.info(f"准备向 MySQL 同步 {len(batch_data)} 条数据。")

            try:
                self.mysql_conn.connect_mysql(mysql_param=config.MYSQL_PARAM, max_connections=config.MAX_TASK_NUMBER)

                batch_size = 500
                total_affected_rows = 0
                for i in range(0, len(batch_data), batch_size):
                    chunk = batch_data[i:i+batch_size]
                    affected_rows = self.mysql_conn.insert_or_update_batch(sql_insert_or_update_batch, chunk)
                    total_affected_rows += affected_rows if affected_rows else 0

                self.logger.info(f"已成功向 MySQL 同步 {total_affected_rows} 行。")

                # Update Redis status and cleanup as per original logic
                pipe = self.redis_conn.pipeline()
                for patent_id in synced_ids:
                    redis_key = f"{self.redis_key_prefix}{patent_id}"
                    # Mark as synced
                    pipe.hset(redis_key, "synced", "1")
                    # Delete synced data and index entry as per original behavior
                    pipe.delete(redis_key)
                    pipe.srem(self.redis_index_key, patent_id)
                await asyncio.to_thread(pipe.execute)
                self.logger.info(f"已更新并清理 {len(synced_ids)} 条目的同步状态（Redis）。")

                # Update MySQL task table as per original logic (main node only)
                sql_persistent_state = f"UPDATE `epatent_0` SET `state` = 1 WHERE `an` = %s"
                self.mysql_conn.insert_or_update_batch(sql_persistent_state, [(pid,) for pid in synced_ids])
                self.logger.info(f"已更新 MySQL epatent_0 表中 {len(synced_ids)} 个任务的状态。")

            except Exception as e:
                self.logger.error(f"MySQL 批量写入过程中失败: {e}")
            finally:
                self.mysql_conn.close_mysql()

            elapsed_time = time.time() - start_time
            self.logger.info(f"Redis → MySQL 同步任务完成，用时 {elapsed_time:.2f} 秒。")

        except Exception as e:
            self.logger.error(f"Error during Redis to MySQL sync task: {e}")
        finally:
            self.redis_conn.close_redis()
