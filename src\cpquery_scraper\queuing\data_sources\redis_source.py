"""
Redis数据源实现
实现了从Redis队列读取任务的功能。
"""
import asyncio
from typing import List, Optional
import time

from ..models import TaskItem, DataSourceConfig, DataSourceType, TaskStatus
from ..exceptions import DataSourceError, DatabaseConnectionError
from .base import BaseDataSource, DataSourceFactory
from ..performance import record_task_fetch_time, record_batch_processing_time

from src.cpquery_scraper.config import config


def _validate_task_id_fast(task_id: str) -> bool:
    """快速任务ID验证"""
    return (isinstance(task_id, str) and
            len(task_id) > 0 and
            '.' not in task_id and
            ' ' not in task_id)


class RedisDataSource(BaseDataSource):
    """
    Redis数据源实现
    从Redis队列中读取任务列表。
    它还包含从MySQL补充任务到Redis的逻辑。
    """

    def __init__(self, config_obj: DataSourceConfig, logger=None):
        super().__init__(config_obj, logger)
        if config_obj.source_type != DataSourceType.REDIS:
            raise DataSourceError("Invalid source type for Redis data source", source_type=config_obj.source_type)

        # 参数校验
        redis_params = self.config.connection_params.get('redis_params')
        if not isinstance(redis_params, dict) or 'host' not in redis_params or 'port' not in redis_params:
            raise DataSourceError(
                "Missing required redis_params (host, port)",
                source_type="redis",
                error_code="MISSING_PARAMS"
            )

        # 延迟导入（便于测试 patch 与避免未安装redis库时报错）
        from src.cpquery_scraper.utils.db import RedisConnection, MysqlConnection
        self._redis_client = RedisConnection()
        self._mysql_client = MysqlConnection() # 用于补充

        self._data_key = self.config.connection_params.get('data_key', 'cpquery_cponline:an_wait_process')
        self._batch_size = self.config.connection_params.get('batch_size', 100)

    async def connect(self) -> None:
        """连接到Redis数据库"""
        try:
            params = self.config.connection_params.get('redis_params') or config.REDIS_PARAM
            self._redis_client.connect_redis(
                params,
                db=self.config.connection_params.get('db_index', 1)
            )
            self._is_connected = True
            self._log_operation("Connected to Redis data source")
        except Exception as e:
            self._is_connected = False
            raise self._handle_error(e, "connect to Redis data source")

    async def disconnect(self) -> None:
        """断开Redis数据库连接"""
        if self._redis_client and self._is_connected:
            self._redis_client.close_redis()
        self._is_connected = False
        self._log_operation("Disconnected from Redis data source")

    async def fetch_tasks(self, limit: Optional[int] = None) -> List[TaskItem]:
        """从Redis队列获取任务列表"""
        if not self._is_connected:
            raise DataSourceError("Not connected to Redis", "redis", "NOT_CONNECTED")

        limit = limit or self._batch_size
        tasks = []
        start_time = time.time()

        for _ in range(limit):
            task_id = self._redis_client.spop(self._data_key)
            if task_id is None:
                # 队列中没有更多任务，如果是主角色，则尝试补充
                if config.ROLE == config.Role.MAIN:
                    self.logger.info("Redis queue empty, attempting to replenish from MySQL.")
                    await self._replenish_from_mysql()
                    # 补充后再试一次
                    task_id = self._redis_client.spop(self._data_key)
                    if task_id is None:
                        self.logger.info("即使在补充后也没有可用的任务。")
                        break
                else:
                    self.logger.info("分布式工作程序的 Redis 队列为空。")
                    break

            if _validate_task_id_fast(task_id):
                tasks.append(TaskItem(task_id=task_id.strip(), status=TaskStatus.PENDING))
            else:
                self.logger.warning(f"Invalid task ID from Redis: {task_id}")

        processing_time = time.time() - start_time
        record_batch_processing_time(len(tasks), processing_time)
        self._log_operation(f"Fetched {len(tasks)} tasks from Redis", {'requested': limit, 'actual': len(tasks)})
        return tasks

    async def _replenish_from_mysql(self):
        """从MySQL获取任务并填充到Redis"""
        self.logger.info("Connecting to MySQL to replenish Redis task queue.")
        try:
            self._mysql_client.connect_mysql(config.MYSQL_PARAM, max_connections=1)
            sql = f"SELECT `an` FROM `epatent_0` WHERE `state` = 0 ORDER BY Level DESC LIMIT {config.TASK_QUEUE_MAX_SIZE}"
            results = self._mysql_client.query(sql)

            if not results:
                self.logger.warning("No tasks found in MySQL to replenish Redis.")
                return

            task_ids = [row["an"] for row in results]
            if task_ids:
                self._redis_client.sadd(self._data_key, *task_ids)
                self.logger.info(f"Successfully replenished Redis with {len(task_ids)} tasks from MySQL.")

        except Exception as e:
            self.logger.error(f"Failed to replenish tasks from MySQL: {e}", exc_info=True)
        finally:
            self._mysql_client.close_mysql()

    async def get_task_count(self, status: Optional[int] = None) -> int:
        """获取任务数量"""
        if not self._is_connected:
            return 0
        count = self._redis_client.scard(self._data_key)
        return int(count) if count is not None else 0

    async def health_check(self) -> bool:
        """健康检查"""
        return self._redis_client.ping() is True

    async def update_task_status(self, task_id: str, status: int) -> bool:
        """
        更新任务状态（Redis数据源不支持状态更新）

        Args:
            task_id: 任务ID
            status: 新状态

        Returns:
            总是返回True（Redis数据源不需要状态更新）
        """
        # Redis数据源是只读的，不支持状态更新
        # 这里返回True表示操作"成功"，但实际上没有执行任何操作
        # 忽略未使用的参数
        _ = status
        self.logger.debug(f"Redis data source does not support status update for task {task_id}")
        return True

# 注册Redis数据源到工厂
DataSourceFactory.register(DataSourceType.REDIS, RedisDataSource)
