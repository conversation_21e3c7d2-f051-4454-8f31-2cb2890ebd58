# Auth模块文档

## 文档索引

### 📚 主要文档

1. **[AUTH_README.md](AUTH_README.md)** - Auth模块总体介绍和快速开始指南
2. **[AUTH_MODULE_REFACTOR_REPORT.md](AUTH_MODULE_REFACTOR_REPORT.md)** - 详细的重构报告和技术细节
3. **[MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)** - 从原模块迁移到新模块的详细步骤
4. **[EXCEPTION_CLASSES_GUIDE.md](EXCEPTION_CLASSES_GUIDE.md)** - 异常类使用指南和最佳实践

### 🎯 快速导航

#### 新用户
- 从 [AUTH_README.md](AUTH_README.md) 开始
- 查看 [../examples/example_usage.py](../examples/example_usage.py) 了解使用方法

#### 迁移用户
- 阅读 [MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)
- 运行 [../tests/switch_verification.py](../tests/switch_verification.py) 验证兼容性

#### 开发者
- 查看 [AUTH_MODULE_REFACTOR_REPORT.md](AUTH_MODULE_REFACTOR_REPORT.md) 了解技术细节
- 参考 [EXCEPTION_CLASSES_GUIDE.md](EXCEPTION_CLASSES_GUIDE.md) 进行错误处理

### 🧪 测试和验证

所有测试文件位于 [../tests/](../tests/) 目录：

- `test_auth_module.py` - 基础兼容性测试
- `test_exceptions.py` - 异常类功能测试
- `detailed_comparison_test.py` - 详细对比测试
- `switch_verification.py` - 切换验证测试
- `run_all_tests.py` - 测试运行器

### 📖 使用示例

所有示例文件位于 [../examples/](../examples/) 目录：

- `example_usage.py` - 完整的使用示例
- `run_examples.py` - 示例运行器

### 🚀 快速开始

1. **查看总体介绍**：
   ```bash
   # 阅读主要文档
   cat auth/docs/AUTH_README.md
   ```

2. **运行测试验证**：
   ```bash
   # 运行所有测试
   python auth/tests/run_all_tests.py
   ```

3. **查看使用示例**：
   ```bash
   # 运行示例
   python auth/examples/example_usage.py
   ```

4. **进行迁移**：
   ```bash
   # 验证切换兼容性
   python auth/tests/switch_verification.py
   ```

### 📁 目录结构

```
auth/
├── __init__.py                 # 模块入口
├── exceptions.py               # 异常类定义
├── user_provider.py            # 用户信息管理
├── login_checker.py            # 登录状态检查
├── captcha_solver.py           # 验证码处理
├── login_manager.py            # 登录流程管理
├── utils.py                    # 工具函数
├── docs/                       # 文档目录
│   ├── README.md              # 本文件
│   ├── AUTH_README.md         # 主要文档
│   ├── AUTH_MODULE_REFACTOR_REPORT.md
│   ├── MIGRATION_GUIDE.md
│   └── EXCEPTION_CLASSES_GUIDE.md
├── tests/                      # 测试目录
│   ├── __init__.py
│   ├── run_all_tests.py       # 测试运行器
│   ├── test_auth_module.py    # 兼容性测试
│   ├── test_exceptions.py     # 异常类测试
│   ├── detailed_comparison_test.py
│   └── switch_verification.py
└── examples/                   # 示例目录
    ├── __init__.py
    ├── run_examples.py        # 示例运行器
    └── example_usage.py       # 使用示例
```

### 💡 提示

- 所有脚本都已配置为可以从项目根目录正确运行
- 测试脚本会自动处理路径问题
- 如果遇到导入问题，请确保在项目根目录执行命令

### 🤝 贡献

如果发现文档有误或需要改进，请：
1. 检查相关测试是否通过
2. 更新对应的文档
3. 运行测试验证更改
