
python -m nuitka --standalone corutine_main.py  --include-data-dir=./stealth=stealth --include-data-dir=./browser=browser  --include-data-dir=D:\users\qwers\miniconda3\envs\nuitka_build_for_cpquery_rpa\Lib\site-packages\playwright=playwright

# 可选
--include-data-dir=D:\users\qwers\miniconda3\envs\nuitka_build_for_cpquery_rpa\Lib\site-packages\playwright
# 必选
--include-package-data=playwright
--mingw64
# 可选
--windows-disable-console

python -m nuitka --onefile corutine_main.py  --mingw64   --include-data-dir=./browser=browser  --include-package-data=playwright

python -m nuitka --show-memory --show-progress  --onefile corutine_main.py  --include-data-dir=./browser=browser  --include-package-data=playwright



python -m nuitka --onefile corutine_main.py  --mingw64   --include-package-data=playwright

nuitka --onefile --mingw64 --include-package-data=playwright --include-package-data=aliyunsdkcore --include-data-files=D:\users\qwers\miniconda3\envs\nuitka_build_for_cpquery_rpa\Lib\site-packages\aliyunsdkcore\data\*.json=.\static_assets\aliyunsdkcore\data\ corutine_main.py


需要包含文件：retry_config.json的源路径和目标路径
D:\users\qwers\miniconda3\envs\nuitka_build_for_cpquery_rpa\Lib\site-packages\aliyunsdkcore\data\retry_config.json
oss2\resources\retry_config.json
 

