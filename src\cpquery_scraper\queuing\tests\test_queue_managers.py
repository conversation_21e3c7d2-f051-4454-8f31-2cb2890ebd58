"""
队列管理器测试用例

测试任务队列管理器和结果队列管理器的功能。

Author: wwind
Date: 2025.07.08
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from ..models import (
    QueueConfig, QueueType, DataSourceConfig, DataSourceType, 
    TaskItem, TaskStatus
)
from ..queue_managers import TaskQueueManager, ResultQueueManager
from ..data_sources import DataSourceFactory
from ..exceptions import QueueOperationError, ConfigurationError


class TestTaskQueueManager(unittest.IsolatedAsyncioTestCase):
    """测试任务队列管理器"""
    
    def setUp(self):
        """设置测试环境"""
        # 创建模拟数据源
        self.mock_data_source = Mock()
        self.mock_data_source.source_type = DataSourceType.MYSQL
        self.mock_data_source.is_connected = False
        self.mock_data_source.connect = AsyncMock()
        self.mock_data_source.disconnect = AsyncMock()
        self.mock_data_source.fetch_tasks = AsyncMock()
        self.mock_data_source.health_check = AsyncMock(return_value=True)
        
        # 创建队列配置
        self.queue_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=100,
            monitor_interval=1,  # 短间隔用于测试
            low_threshold=10,
            batch_size=20
        )
        self.manager = TaskQueueManager(self.mock_data_source, self.queue_config)

    async def tearDown(self):
        await self.manager.stop()
    
    def test_init_with_invalid_config(self):
        """测试使用无效配置初始化"""
        invalid_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=0,  # 无效的最大大小
            monitor_interval=1,
            low_threshold=10
        )
        
        with self.assertRaises(ConfigurationError):
            TaskQueueManager(self.mock_data_source, invalid_config)
    
    async def test_start_and_stop(self):
        """测试启动和停止管理器"""
        manager = TaskQueueManager(self.mock_data_source, self.queue_config)
        
        # 模拟数据源返回一些任务
        test_tasks = [
            TaskItem(task_id="task1", status=TaskStatus.PENDING),
            TaskItem(task_id="task2", status=TaskStatus.PENDING)
        ]
        self.mock_data_source.fetch_tasks.return_value = test_tasks
        
        # 启动管理器
        await manager.start()
        self.assertTrue(manager.is_running)
        self.mock_data_source.connect.assert_called_once()
        
        # 等待一小段时间让初始化完成
        await asyncio.sleep(0.1)
        
        # 检查队列中是否有任务
        self.assertGreater(manager.queue_size, 0)
        
        # 停止管理器
        await manager.stop()
        self.assertFalse(manager.is_running)
        self.mock_data_source.disconnect.assert_called_once()
    
    @unittest.mock.patch('asyncio.sleep', return_value=None)
    async def test_monitoring(self, mock_sleep):
        """测试监控任务"""
        manager = TaskQueueManager(self.mock_data_source, self.queue_config)

        # 模拟数据源返回任务
        self.mock_data_source.fetch_tasks.return_value = []

        await manager.start()

        # 让监控运行一段时间
        await asyncio.sleep(2)

        # 验证健康检查是否被调用
        self.mock_data_source.health_check.assert_called()

        await manager.stop()

    async def test_get_task(self):
        """测试获取任务"""
        manager = TaskQueueManager(self.mock_data_source, self.queue_config)
        
        # 模拟数据源返回任务
        test_tasks = [TaskItem(task_id="task1", status=TaskStatus.PENDING)]
        self.mock_data_source.fetch_tasks.return_value = test_tasks
        
        await manager.start()
        
        # 获取任务
        task = await manager.get_task(timeout=1.0)
        self.assertIsNotNone(task)
        self.assertEqual(task.task_id, "task1")
        
        await manager.stop()
    
    async def test_get_task_timeout(self):
        """测试获取任务超时"""
        manager = TaskQueueManager(self.mock_data_source, self.queue_config)
        
        # 模拟数据源返回空任务列表
        self.mock_data_source.fetch_tasks.return_value = []
        
        await manager.start()
        
        # 尝试获取任务，应该超时
        task = await manager.get_task(timeout=0.1)
        self.assertIsNone(task)
        
        await manager.stop()
    
    async def test_task_done(self):
        """测试标记任务完成"""
        manager = TaskQueueManager(self.mock_data_source, self.queue_config)
        
        test_tasks = [TaskItem(task_id="task1", status=TaskStatus.PENDING)]
        self.mock_data_source.fetch_tasks.return_value = test_tasks
        
        await manager.start()
        
        # 获取任务
        task = await manager.get_task(timeout=1.0)
        initial_processing = manager.stats.processing_tasks
        
        # 标记任务完成
        await manager.task_done(task, success=True)
        
        # 检查统计信息
        self.assertEqual(manager.stats.completed_tasks, 1)
        self.assertEqual(manager.stats.failed_tasks, 0)
        
        await manager.stop()
    
    async def test_get_queue_info(self):
        """测试获取队列信息"""
        manager = TaskQueueManager(self.mock_data_source, self.queue_config)
        
        info = await manager.get_queue_info()
        
        self.assertIn('queue_size', info)
        self.assertIn('max_size', info)
        self.assertIn('is_running', info)
        self.assertIn('data_source_type', info)
        self.assertIn('stats', info)
        
        self.assertEqual(info['max_size'], 100)
        self.assertEqual(info['data_source_type'], 'mysql')


class TestResultQueueManager(unittest.IsolatedAsyncioTestCase):
    """测试结果队列管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.queue_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=100,
            monitor_interval=1,  # 短间隔用于测试
            low_threshold=0,
            batch_size=10
        )
    
    def test_init_with_invalid_config(self):
        """测试使用无效配置初始化"""
        invalid_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=-1,  # 无效的最大大小
            monitor_interval=1,
            low_threshold=0
        )
        
        with self.assertRaises(ConfigurationError):
            ResultQueueManager(invalid_config)
    
    async def test_start_and_stop(self):
        """测试启动和停止管理器"""
        manager = ResultQueueManager(self.queue_config)
        
        # 启动管理器
        await manager.start()
        self.assertTrue(manager.is_running)
        
        # 停止管理器
        await manager.stop()
        self.assertFalse(manager.is_running)
    
    async def test_put_result(self):
        """测试添加结果"""
        manager = ResultQueueManager(self.queue_config)
        
        await manager.start()
        
        # 添加结果
        test_result = {"task_id": "task1", "result": "success"}
        await manager.put_result(test_result)
        
        self.assertEqual(manager.queue_size, 1)
        
        await manager.stop()
    
    def test_put_result_nowait(self):
        """测试非阻塞添加结果"""
        manager = ResultQueueManager(self.queue_config)
        
        # 添加结果
        test_result = {"task_id": "task1", "result": "success"}
        manager.put_result_nowait(test_result)
        
        self.assertEqual(manager.queue_size, 1)
    
    def test_put_result_nowait_queue_full(self):
        """测试队列满时非阻塞添加结果"""
        # 创建一个很小的队列
        small_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=1,
            monitor_interval=1,
            low_threshold=0
        )
        
        manager = ResultQueueManager(small_config)
        
        # 填满队列
        manager.put_result_nowait({"task_id": "task1"})
        
        # 尝试再添加一个，应该抛出异常
        with self.assertRaises(QueueOperationError):
            manager.put_result_nowait({"task_id": "task2"})
    
    async def test_get_queue_info(self):
        """测试获取队列信息"""
        manager = ResultQueueManager(self.queue_config)
        
        info = await manager.get_queue_info()
        
        self.assertIn('queue_size', info)
        self.assertIn('max_size', info)
        self.assertIn('is_running', info)
        self.assertIn('stats', info)
        
        self.assertEqual(info['max_size'], 100)
    
    async def test_context_manager(self):
        """测试上下文管理器"""
        async with ResultQueueManager(self.queue_config) as manager:
            self.assertTrue(manager.is_running)
            
            # 添加一些结果
            manager.put_result_nowait({"task_id": "task1"})
            self.assertEqual(manager.queue_size, 1)
        
        # 退出上下文后应该停止
        self.assertFalse(manager.is_running)


class TestQueueManagerIntegration(unittest.IsolatedAsyncioTestCase):
    """测试队列管理器集成"""
    
    async def test_task_and_result_queue_integration(self):
        """测试任务队列和结果队列的集成"""
        # 创建模拟数据源
        mock_data_source = Mock()
        mock_data_source.source_type = DataSourceType.MYSQL
        mock_data_source.is_connected = False
        mock_data_source.connect = AsyncMock()
        mock_data_source.disconnect = AsyncMock()
        mock_data_source.health_check = AsyncMock(return_value=True)
        
        # 模拟返回一些任务
        test_tasks = [
            TaskItem(task_id="task1", status=TaskStatus.PENDING),
            TaskItem(task_id="task2", status=TaskStatus.PENDING)
        ]
        mock_data_source.fetch_tasks = AsyncMock(return_value=test_tasks)
        
        # 创建配置
        task_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=100,
            monitor_interval=1,
            low_threshold=10
        )
        
        result_config = QueueConfig(
            queue_type=QueueType.RESULT_QUEUE,
            max_size=100,
            monitor_interval=1,
            low_threshold=0
        )
        
        # 创建管理器
        task_manager = TaskQueueManager(mock_data_source, task_config)
        result_manager = ResultQueueManager(result_config)
        
        try:
            # 启动管理器
            await task_manager.start()
            await result_manager.start()
            
            # 等待任务加载
            await asyncio.sleep(0.1)
            
            # 模拟处理流程
            task = await task_manager.get_task(timeout=1.0)
            self.assertIsNotNone(task)
            
            # 处理任务并生成结果
            result = {"task_id": task.task_id, "status": "completed"}
            await result_manager.put_result(result)
            
            # 标记任务完成
            await task_manager.task_done(task, success=True)
            
            # 检查统计信息
            self.assertEqual(task_manager.stats.completed_tasks, 1)
            self.assertEqual(result_manager.queue_size, 1)
            
        finally:
            # 清理
            await task_manager.stop()
            await result_manager.stop()


if __name__ == '__main__':
    unittest.main()
