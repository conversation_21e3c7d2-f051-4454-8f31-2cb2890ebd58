import json
import time

# Corrected import paths
from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.utils.notice_mapping import NOTICE_MAPPING

# 此文件广泛使用日志记录器，因此创建别名会很有帮助
print_info = get_logger(__name__).info

def timestamp_to_date(timestamp, format_string="%Y-%m-%d %H:%M:%S"):
    """
    将时间戳转换为指定格式的日期字符串
    """
    return time.strftime(format_string, time.localtime(timestamp))

def trans_format(item_origi: dict) -> dict | None:
    '''
    把从cpquery获取的原始数据，转换为业务要求的数据格式
    '''
    if config.TEST_MODE:
        print_info("debug: 进入trans_format函数")
        print_info("debug: 查看原始结果记录的格式")
        print_info(f"debug:item_origi=\n{list(item_origi.keys())}")

    assert len(item_origi) == 1

    # 申请号处理
    patent_id = str(list(item_origi.keys())[0])

    data = list(item_origi.values())[0]

    # 检查data中的非None值的数量，而不是总长度
    non_none_fields = sum(1 for v in data.values() if v is not None)
    if non_none_fields < 4:
        print_info(f"--trans_format函数: {patent_id} 数据不完整，有效字段数量少于4: {data.keys()}")
        return None

    # 申请信息处理
    assert "sqxx" in data
    sqxx_info = data["sqxx"]
    if sqxx_info['code'] == 200:
        sqxx_data = sqxx_info['data']
        zhuluxmxx = sqxx_data['zhuluxmxx']['zhuluxmxx']

        assert zhuluxmxx['zhuanlisqh'] == patent_id

        if zhuluxmxx['zhuanlimc'] != "--":
            invention_name = zhuluxmxx['zhuanlimc']
        else:
            invention_name = None

        if zhuluxmxx["shenqingr"] != "--":
            apply_date = zhuluxmxx["shenqingr"]
            assert len(apply_date.split("-")) == 3
            ady = apply_date.split("-")[0]
            adm = apply_date.split("-")[1]
        else:
            apply_date, ady, adm = None,None,None

        if zhuluxmxx['anjianywzt'] != "--":
            case_state = zhuluxmxx['anjianywzt']
        else:
            case_state = None

        if zhuluxmxx['zhufenlh'] != "--":
            ipc_main = f"{zhuluxmxx['zhufenlh']}({zhuluxmxx['zhufenlhbbh']})"
            ipc_sub = f"{zhuluxmxx['fufenlh']}({zhuluxmxx['fufenlhbbh']})"
            date_ipc_get = zhuluxmxx['fenleirq']
        else:
            ipc_main, ipc_sub, date_ipc_get = None, None, None

        if sqxx_data['shenqingren']["isShow"]:
            shenqingren_list = sqxx_data['shenqingren']['shenqingrenList']
            applicant = ";".join([x["shenqingrxm"] for x in shenqingren_list])
            appl_first = [x["shenqingrxm"] for x in shenqingren_list][0]
        else:
            applicant, appl_first = None,None

        if sqxx_data["famingren"]["isShow"]:
            famingren_list = sqxx_data["famingren"]["famingrenList"]
            inventor = "、".join([x["famingrxm"] for x in famingren_list])
        else:
            inventor = None

        if sqxx_data["dailijg"]["isShow"]:
            dailijg_list = sqxx_data["dailijg"]["dailijgList"]
            attorney = dailijg_list[0]["diyidlrxm"]
            agency = dailijg_list[0]["dailijgdm"]
        else:
            agency,attorney = None,None

        if sqxx_data["youxianq"]["isShow"]:
            priority_list = sqxx_data["youxianq"]["youxianqList"]
            priority = []
            for x in priority_list:
                p = {"Prior_code" : x["zaixiansqh"],
                        "Prior_date" : x["zaixiansqr"],
                        "Prior_institution": x["zaixiansqgb"] }
                priority.append(p)
        else:
            priority = None

        if sqxx_data["guojisqjd"]["isShow"]:
            stage_list = sqxx_data["guojisqjd"]["guojisqjdList"]
            stage = []
            for x in stage_list:
                inter = {"Inter_an" : x["guojisqh"],
                            "inter_apd" : x["guojisqr"],
                            "inter_pn": x["guojigbh"],
                            "inter_pd" : x["guojigbr"] }
                stage.append(inter)
        else:
            stage = None

        if sqxx_data["zhuluxmbg"]["isShow"]:
            change_list = sqxx_data["zhuluxmbg"]["zhuluxmbgList"]
            change = []
            for x in change_list:
                cha = {"date" : x["zhulubgsxr"],
                        "type" : x["zhulubgsx"],
                        "before": x["zhulubgq"],
                        "after" : x["zhulubgh"] }
                change.append(cha)
        else:
            change = None
    elif sqxx_info['code'] == 201:
        sqxx_data = sqxx_info['data']
        zhuluxmxx = sqxx_data['records'][0]

        assert zhuluxmxx['zhuanlisqh'] == patent_id

        if zhuluxmxx['zhuanlimc'] != "--":
            invention_name = zhuluxmxx['zhuanlimc']
        else:
            invention_name = None

        if zhuluxmxx["shenqingr"] != "--":
            apply_date = zhuluxmxx["shenqingr"]
            assert len(apply_date.split("-")) == 3
            ady = apply_date.split("-")[0]
            adm = apply_date.split("-")[1]
        else:
            apply_date, ady, adm = None,None,None

        if zhuluxmxx['anjianywzt'] != "--":
            case_state = zhuluxmxx['anjianywzt']
        else:
            case_state = None

        if zhuluxmxx['zhufenlh'] != "--":
            ipc_main = str(zhuluxmxx['zhufenlh'])
            ipc_sub = None
            date_ipc_get = None
        else:
            ipc_main, ipc_sub, date_ipc_get = None, None, None

        if zhuluxmxx['shenqingrxm'] != "--":
            shenqingren_list = zhuluxmxx['shenqingrxm'].split(",")
            applicant = ";".join([x for x in shenqingren_list])
            appl_first = [x for x in shenqingren_list][0]
        else:
            applicant, appl_first = None,None

        inventor = None
        agency,attorney = None,None
        priority = None
        stage = None
        change = None
    else:
        print_info(f"--trans_format函数：{patent_id} 申请信息异常（未获取到或不存在）：{data['sqxx']}")
        invention_name = None
        apply_date, ady, adm = None,None,None
        case_state = None
        ipc_main, ipc_sub, date_ipc_get = None, None, None
        applicant, appl_first = None,None
        inventor = None
        agency, attorney = None, None
        priority = None
        change = None
        stage = None

    # 费用信息处理
    assert "fyxx" in data
    fyxx_info = data["fyxx"]
    if fyxx_info["code"] == 200:
        fyxx_data = fyxx_info['data']

        # 已缴费
        if "yijiaofei" in fyxx_data and fyxx_data["yijiaofei"] is not None and fyxx_data["yijiaofei"]["isShow"]:
            fee_paid_list = fyxx_data["yijiaofei"]["svYijfList"]
            fee_paid = []
            for x in fee_paid_list:
                fee = {"expense_category" : x["yijiaofjfzlmc"],
                        "expense_number" : x["yijiaofjfje"],
                        "expense_expired": x["yijiaofjfrq"],
                        "payer_name" : x["yijiaofjfrxm"] ,
                        "bill_num" : x["yijiaofpjdm"]}
                fee_paid.append(fee)
        else:
            fee_paid = None

        # 应缴费
        if "yingjiaofei" in fyxx_data and fyxx_data["yingjiaofei"] is not None and fyxx_data["yingjiaofei"]["isShow"]:
            fee_payable_list = fyxx_data["yingjiaofei"]["svYingjfList"]
            fee_payable = []
            for x in fee_payable_list:
                fee = {"fee_category" : x["yingjiaoffyzlmc"],
                        "fee_number" : x["yingjiaoje"],
                        "fee_expired": x["jiaofeijzr"],
                        "fee_status" : x["yingjiaoffyzt"]}
                fee_payable.append(fee)
        else:
            fee_payable = None

        # 退费
        if "tuifei" in fyxx_data and fyxx_data["tuifei"] is not None and fyxx_data["tuifei"]["isShow"]:
            refund_list = fyxx_data["tuifei"]["svtfList"]
            refund = []
            for x in refund_list:
                fee = {"refund_type" : x["tuifeitfzlmc"],
                        "refund_amount" : x["tuifeitfje"],
                        "refund_date": x["tuifeitfrq"],
                        "beneficiary" : x["tuifeiskrxm"],
                        "receipt" : x["tuifeipjhm"],
                        "receipt_code" : x["tuifeipjdm"]}
                refund.append(fee)
        else:
            refund = None

        # 滞纳金
        if "zhinajin" in fyxx_data and fyxx_data["zhinajin"] is not None and fyxx_data["zhinajin"]["isShow"]:
            demurrage_list = fyxx_data["zhinajin"]["svZnjList"]
            demurrage = []
            for x in demurrage_list:
                fee = {"time" : x["zhinajjfsj"],
                        "annual" : x["zhinajdqnfje"],
                        "should_paid": x["zhinajyjznje"],
                        "total" : x["zhinajzj"]}
                demurrage.append(fee)
        else:
            demurrage = None

        # 收据发文
        if "shoujufawen" in fyxx_data and fyxx_data["shoujufawen"] is not None and fyxx_data["shoujufawen"]["isShow"]:
            receipt_list = fyxx_data["shoujufawen"]["svSjfwList"]
            receipt = []
            for x in receipt_list:
                fee = {"fee_type" : x["shoujufwfyzlmc"],
                        "fee_amount" : x["shoujufwjfje"],
                        "payer_name": x["shoujufwjfrxm"],
                        "payment_date" : x["shoujufwjfsj"],
                        "receipt_code" : x["shoujufwsjh"],
                        "receipt_header" : x["shoujufwsjtt"],
                        "receipt_mailing_address": x["shoujufwyjdz"],
                        "remittance_date" : x["shoujufwtkhcrq"],
                        "Shipped" : x["shoujufwsfjc"],
                        "mailing_date" : x["shoujufwfwrq"],
                        "tracking_num": x["shoujufwghhm"],
                        "refund_remittance_date" : x["shoujufwtkrq"],
                        }
                receipt.append(fee)
        else:
            receipt = None

        # 冲红
        if "chonghong" in fyxx_data and fyxx_data["chonghong"] is not None and fyxx_data["chonghong"]["isShow"]:
            chonghong_list = fyxx_data["chonghong"]["svchList"]
            chonghong = []
            for x in chonghong_list:
                fee = x
                chonghong.append(fee)
        else:
            chonghong = None
    else:
        print_info(f"--trans_format函数：{patent_id} 费用信息异常（未获取到或不存在）：{data['fyxx']}")
        fee_paid = None
        fee_payable = None
        refund = None
        demurrage = None
        receipt = None
        chonghong = None

    # 发文信息处理
    assert "fwxx" in data
    fwxx_info = data["fwxx"]
    if fwxx_info["code"] == 200:
        fwxx_data = fwxx_info['data']

        # 发文信息：通知书发文
        if fwxx_data["tongzhishufw"]["isShow"]:
            dispatch_list = fwxx_data["tongzhishufw"]["tongzhishufwList"]
            dispatch = []
            for x in dispatch_list:
                fw = {"notification_title" : x["tongzhismc"],
                        "dispatch_date" : x["fawenr"],
                        "recipient's_name" : x["shoujianrxm"],
                        "recipient's_post_code" : x["shoujianryb"],
                        "download_time" : x["xiazaisj"],
                        "download_ip" : x["xiazaiip"],
                        "method_of_delivery" : x["fawenfs"],}
                dispatch.append(fw)
        else:
            dispatch = None

        # 发文信息：证书发文
        if fwxx_data["zhuanlizsfw"]["isShow"]:
            letter_list = fwxx_data["zhuanlizsfw"]["zhuanlizsfwList"]
            letter = []
            for x in letter_list:
                t = {
                        "dispatch_date" : x["fawenr"],
                        "recipient's_name" : x["shoujianrxm"],
                        "recipient's_post_code" : x["shoujianryb"],
                    }
                letter.append(t)
        else:
            letter = None

        # 发文信息：退信发文
        if fwxx_data["tuixinfw"]["isShow"]:
            e_return_list = fwxx_data["tuixinfw"]["tuixinfwList"]
            e_return = []
            for x in e_return_list:
                t = {
                        "returned_letter_type" : x["tuixinzlmc"],
                        "original_recipient " : x["yuansjrxm"],
                        "original_dispatch_date" : x["yuantzsfwr"],
                        "returned_letter_reason": x["tuixinyy"],
                        "resend_recipient" : x["chongfasjrxm"],
                        "resend_dispatch_date" : x["chongfatzsfwr"],
                        "equation_delivery_code" : x["gongshisdjqh"],
                        "equation_delivery_date" : x["gongshisdrq"]
                    }
                e_return.append(t)
        else:
            e_return = None
    else:
        print_info(f"--trans_format函数：{patent_id} 发文信息异常（未获取到或不存在）：{data['fwxx']}")
        dispatch = None
        letter = None
        e_return = None

    # 公告信息处理
    assert "ggxx" in data
    ggxx_info = data["ggxx"]
    if ggxx_info["code"] == 200:
        ggxx_data = ggxx_info['data']

        # 发明公告
        if ggxx_data["faminggbsqgg"]["isShow"]:
            publish_list = ggxx_data["faminggbsqgg"]["faminggbsqggList"]
            publish = []
            pn, pd, gn, gd = '','','',''
            for x in publish_list:
                t = {
                        "code" : x["gonggaogbh"],
                        "type" : x["gonggaolx"],
                        "num" : x["quanqih"],
                        "date" : x["gonggaogbr"],
                    }
                publish.append(t)

                if "公布" in t['type']:
                    pn = t['code']
                    pd = t['date']
                elif "授权" in t["type"]:
                    gn = t['code']
                    gd = t['date']
        else:
            publish = None
            pn, pd, gn, gd = None,None,None,None

        # 事务公告
        if ggxx_data["shiwugg"]["isShow"]:
            business_list = ggxx_data["shiwugg"]["shiwuggList"]
            business = []
            for x in business_list:
                t = {
                        "type" : x["shiwugglx"],
                        "num" : x["gonggaojqh"],
                        "date" : x["shiwuggr"],
                    }
                business.append(t)
        else:
            business = None
    else:
        print_info(f"--trans_format函数：{patent_id} 公告信息异常（未获取到或不存在）：{data['ggxx']}")
        publish = None
        business = None
        pn, pd, gn, gd = None,None,None,None

    # 更新日期
    up_time = timestamp_to_date(time.time())

    item = {
        "patent_id": patent_id,
        "invention_name": invention_name,
        "apply_date": apply_date,
        "ady": ady,
        "adm": adm,
        "case_state": case_state,
        "intclass" : f"{ipc_main or ''},{ipc_sub or ''}",
        "ipc_first" : ipc_main,
        "applicant": applicant,
        "appl_first": appl_first,
        "pn": pn,
        "pd": pd,
        "gn": gn,
        "gd": gd,
        "inventor": inventor,
        "attorney": attorney,
        "agency": agency,
        "priority": priority,
        "stage": stage,
        "change": change,
        "fee_payable": fee_payable,
        "demurrage": demurrage,
        "fee_paid": fee_paid,
        "refund": refund,
        "receipt": receipt,
        "dispatch": dispatch,
        "letter": letter,
        "e_return": e_return,
        "publish": publish,
        "business": business,
        "__time" : up_time,
    }

    # items数值dict格式转换为json格式，便于写入数据库
    for k, v in item.items():
        if k=="patent_id" or k=="__time":
            continue
        else:
            if v is None:
                continue
            else:
                if not isinstance(v, str):
                    item[k] = json.dumps(v, ensure_ascii=False)

    # 有效数据判断，只有著录项目数据和费用数据都有，才视为有效数据，否则返回None
    if invention_name is None and apply_date is None and fyxx_info["code"] != 200:
        print_info(f"--trans_format函数:无效数据，舍弃，返回None, 原因：invention_name={invention_name}, apply_date={apply_date}, fyxx_code={fyxx_info['code']}, 原始数据：{item}")
        return None
    else:
        return item
