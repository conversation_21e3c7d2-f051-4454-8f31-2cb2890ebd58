"""
数据源模块

提供统一的数据源接口和实现，支持MySQL、Excel、Redis等多种数据源。

Author: wwind
Date: 2025.07.08
"""

from .base import BaseDataSource, DataSourceFactory

# 导入具体的数据源实现以触发注册
from .mysql_source import MySQLDataSource
from .excel_source import ExcelDataSource
from .redis_source import RedisDataSource

__all__ = [
    'BaseDataSource',
    'DataSourceFactory',
    'MySQLDataSource',
    'ExcelDataSource',
    'RedisDataSource'
]
