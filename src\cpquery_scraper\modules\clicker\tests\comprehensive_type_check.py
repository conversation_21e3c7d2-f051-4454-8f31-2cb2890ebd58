#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的类型注解验证脚本
验证重构后的click模块类型注解与原模块完全一致
"""
import os
import ast
import re
from typing import Dict, List, Tuple

def check_type_consistency():
    """检查类型注解一致性"""
    click_dir = os.path.dirname(os.path.dirname(__file__))
    original_file = os.path.join(os.path.dirname(click_dir), "corutine_click.py")
    
    print("全面类型注解一致性检查:")
    print("=" * 60)
    
    # 检查原模块的类型注解模式
    original_patterns = {}
    try:
        with open(original_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 提取原模块的关键类型注解
        original_patterns = {
            'main_page_query_an_return': re.search(r'def main_page_query_an.*?->\s*(\w+)', original_content),
            'main_page_click_an_return': re.search(r'def main_page_click_an.*?->\s*(\w+)', original_content),
            'get_appl_data_return': re.search(r'def get_appl_data.*?->\s*(.*?):', original_content, re.DOTALL),
            'click_event_return': re.search(r'def click_event_button_and_get_data[^_].*?->\s*(.*?):', original_content, re.DOTALL),
            'click_event_sync_return': re.search(r'def click_event_button_and_get_data_sync.*?->\s*(.*?):', original_content, re.DOTALL),
            'click_file_name_return': re.search(r'def click_file_name.*?->\s*(\w+)', original_content),
        }
        
        print("原模块类型注解模式:")
        for key, match in original_patterns.items():
            if match:
                return_type = match.group(1).strip()
                print(f"  {key}: {return_type}")
            else:
                print(f"  {key}: 未找到")
                
    except Exception as e:
        print(f"读取原模块失败: {e}")
        return False
    
    # 检查重构模块的类型注解
    print("\n重构模块类型注解检查:")
    
    # 检查__init__.py中的函数签名
    init_file = os.path.join(click_dir, "__init__.py")
    try:
        with open(init_file, 'r', encoding='utf-8') as f:
            init_content = f.read()
        
        # 检查关键函数的返回类型
        checks = [
            ('main_page_query_an', r'def main_page_query_an.*?->\s*(\w+)', 'dict'),
            ('main_page_click_an', r'def main_page_click_an.*?->\s*(\w+)', 'Page'),
            ('get_appl_data', r'def get_appl_data.*?->\s*(.*?):', 'Optional[asyncio.Future[dict[str, Union[int, str, None]]]]'),
            ('click_event_button_and_get_data', r'def click_event_button_and_get_data[^_].*?->\s*(.*?):', 'Optional[asyncio.Future[dict[str, Union[int, str, None]]]]'),
            ('click_event_button_and_get_data_sync', r'def click_event_button_and_get_data_sync.*?->\s*(.*?):', 'List[dict[str, dict]]'),
            ('click_file_name', r'def click_file_name.*?->\s*(\w+)', 'dict'),
        ]
        
        all_correct = True
        for func_name, pattern, expected_type in checks:
            match = re.search(pattern, init_content, re.DOTALL)
            if match:
                actual_type = match.group(1).strip()
                if expected_type in actual_type or actual_type in expected_type:
                    print(f"  ✅ {func_name}: {actual_type}")
                else:
                    print(f"  ❌ {func_name}: 期望 {expected_type}, 实际 {actual_type}")
                    all_correct = False
            else:
                print(f"  ❌ {func_name}: 未找到函数定义")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"检查__init__.py失败: {e}")
        return False

def check_import_consistency():
    """检查导入一致性"""
    print("\n导入一致性检查:")
    
    click_dir = os.path.dirname(os.path.dirname(__file__))
    
    files_to_check = [
        "__init__.py",
        "data_extractor.py", 
        "file_handler.py",
        "query_manager.py",
        "sync_click_manager.py",
        "exceptions.py"
    ]
    
    all_correct = True
    
    for filename in files_to_check:
        file_path = os.path.join(click_dir, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查禁止的导入
            forbidden_imports = [
                'from concurrent.futures import',
                'import concurrent.futures',
                'concurrent.futures.Future'
            ]
            
            found_forbidden = []
            for forbidden in forbidden_imports:
                if forbidden in content:
                    found_forbidden.append(forbidden)
            
            if found_forbidden:
                print(f"  ❌ {filename}: 包含禁止导入 {found_forbidden}")
                all_correct = False
            else:
                print(f"  ✅ {filename}: 导入正确")
                
        except Exception as e:
            print(f"  ❌ {filename}: 检查失败 - {e}")
            all_correct = False
    
    return all_correct

def check_type_annotation_patterns():
    """检查类型注解模式"""
    print("\n类型注解模式检查:")
    
    click_dir = os.path.dirname(os.path.dirname(__file__))
    
    files_to_check = [
        "__init__.py",
        "data_extractor.py", 
        "file_handler.py",
        "query_manager.py",
        "sync_click_manager.py"
    ]
    
    all_correct = True
    
    for filename in files_to_check:
        file_path = os.path.join(click_dir, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            issues = []
            
            # 检查是否混用Dict和dict
            dict_upper_count = len(re.findall(r'Dict\[', content))
            dict_lower_count = len(re.findall(r'dict\[', content))
            
            if dict_upper_count > 0 and dict_lower_count > 0:
                issues.append(f"混用Dict和dict (Dict: {dict_upper_count}, dict: {dict_lower_count})")
            
            # 检查Future类型使用
            future_wrong = re.findall(r':\s*Future\[|:\s*Optional\[Future\[|->\s*Future\[|->\s*Optional\[Future\[', content)
            if future_wrong:
                issues.append(f"使用了错误的Future类型: {len(future_wrong)} 处")
            
            # 检查asyncio.Future使用
            future_correct = re.findall(r'asyncio\.Future\[', content)
            
            if issues:
                print(f"  ❌ {filename}: {', '.join(issues)}")
                all_correct = False
            else:
                if future_correct:
                    print(f"  ✅ {filename}: 类型注解正确 (包含 {len(future_correct)} 个asyncio.Future)")
                else:
                    print(f"  ✅ {filename}: 类型注解正确")
                
        except Exception as e:
            print(f"  ❌ {filename}: 检查失败 - {e}")
            all_correct = False
    
    return all_correct

def main():
    """主函数"""
    print("开始全面类型注解验证...")
    
    checks = [
        ("类型一致性", check_type_consistency),
        ("导入一致性", check_import_consistency),
        ("类型注解模式", check_type_annotation_patterns),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"检查 {name} 时发生错误: {e}")
            results.append((name, False))
    
    # 输出结果汇总
    print("\n" + "=" * 60)
    print("全面验证结果汇总:")
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "[PASS] 通过" if result else "[FAIL] 失败"
        print(f"{status} {name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 检查通过")
    success_rate = (passed / total) * 100
    print(f"成功率: {success_rate:.1f}%")
    
    if passed == total:
        print("\n🎉 [SUCCESS] 所有类型注解验证通过！")
        print("✅ 重构后的click模块类型注解与原模块完全一致")
        print("✅ 所有类型提示错误已修复")
        print("✅ 可以正常使用，不会有IDE类型提示错误")
        return True
    else:
        print(f"\n[WARNING] 有 {total-passed} 个验证失败")
        return False

if __name__ == "__main__":
    try:
        result = main()
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n验证被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n验证执行失败: {e}")
        exit(1)
