<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>nuitka&lowbar;build&lowbar;for&lowbar;cpqueryRPA</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="nuitka_build_for_cpqueryrpa">nuitka_build_for_cpqueryRPA</h1>
<p>协程方式实现的并行playwright爬虫，获取cpquery数据。
ahthor：wwind
2023.04.05</p>
<h2 id="版本功能变更记录changelog">版本功能变更记录（changelog）:</h2>
<p>20230809：  v1.1
new:
增加Redis作为任务来源，一旦获取的任务即从redis删去，为分布式运行做好准备；
20230810：  v1.2
bugfix:
修复页面刷新失败后导致的页面重置无限循环，解决了日志因此容量暴增的问题；
但怀疑导致该问题的根源（页面刷新不成功，可能需要页面重置）消除不了，需要后续继续测试；
如果目前的措施会导致后续任务一直失败，则需要尝试进行完整的页面重置（spider函数中的data_page_init函数引入click过程）。
20230814:   v1.3
bugfix:
修改用户使用到期结束逻辑，以克服Reddis客户端不及时释放导致的无法结束bug；
change:
1.修改后的用户到期结束逻辑为：当用户使用时限到期时，重置全局标志变量FLAG_SPIDER_RUNNING: bool = False，
然后消耗完任务队列中的剩余任务，再等待结果队列中的数据写入数据库，再结束爬取协程。
2.修改日志部分逻辑，取消mysql客户端和redis客户端类的单独日志名称，统一把日志名称归于所在模块的名称。
20230816:   v1.4
change:
1.修正登录模块中check_login_page_is_logined逻辑（去掉迭代，增加None返回值）；
2.修正get_page_logined函数逻辑，增加处理check_login_page_is_logined返回None的情形，
3.增加重试次数过度自动延时功能，修改部分日志文字。
20230908:   V1.41
change:
修正部分日志文字表述，更精简
20230914:   V1.42
bugfix:
trans_format函数，当获取不到一个申请号所有的数据时，应返回None（之前不会返回None）；
修正对应的代码为：if k==&quot;patent_id&quot; or k==&quot;<em><em>time&quot;:
20230915:   V1.43
bugfix:
修正trans_format函数逻辑，只有同时获取到著录项目数据（专利名称+申请日）和费用数据，才视为有效数据，写入数据库保存，
否则抛弃掉
20230925：  V1.44
bugfix:
增加浏览页页面崩溃或被关闭时，直接退出当前任务的逻辑，避免程序一直重试（页面刷新）
20231007:   V1.45
bugfix：
当mysql任务表中没有数据时，程序会卡死在任务补充循环中（一直尝试从mysql服务器向redis补充任务），
从而导致现存任务队列的任务执行和结果队列数据落盘。
20231008:   V1.5
NEW:
登录cpquery系统的用户信息，增加从配置文件（user_config.json）获取的方式，由全局变量开关控制（USER_INFO_SOURCE_FROM_FILE）。
change:
优化部分日志文字，减少生产运行时的敏感信息暴露，但保留原有代码（注释掉未删除）用于debug。
20231009：  V1.51
bugfix:
1.结果队列数据写入mysql时，因（appl_first）字段超长出错，会导致不断重试（重试前未关闭现有连接）而耗尽mysql服务器连接。
修正bug，在重试前先关闭当前连接，重试时再开启新的连接。
2.v1.5版本调试时改变了redis表名，修正回原来的redis表名。
change:
优化部分日志文字。
20231028：  V1.52
bugfix:
1.用户密码错误时，程序会卡死在重新登录界面.
change:
2.优化部分user_login的日志文字。
20231114：  V1.53
bugfix:
1.页面提示验证码错误时会被误识别为用户密码错；
2.日志可能会暴露用户密码。
change:
1.发送验证码服务器的请求数据格式优化；
2.登陆页面拖动滑块逻辑改为和实际一致。
20231225:   V1.54
change:
1.浏览器context初始化时，增加一个参数：ignore_https_errors=True,忽略服务器证书错误
20240722:   V1.55
bugfix:
1.修复了如果查询页有通知框，会导致程序卡死的bug（获取通知的内容时，不能准确定位locator）。
20240822:   V1.56
change:
1.输出的数据增加了intclass、ipc_first字段。
20241224：  V1.57
bugfix:
1.修复了当前用户密码错误时，页面输入框识别错误（识别不出法人登陆页面的输入框），导致程序卡死。
change:
1.登陆页面输入框识别优化，从单一识别逻辑，改为多个locators识别，以适应不同页面的输入框。
增加的代码如下：
'''
phone_or_id_locator = page.locator(&quot;input[placeholder='手机号/证件号码']&quot;)
credit_code_locator = page.locator(&quot;input[placeholder='统一社会信用代码']&quot;)
agency_code_locator = page.locator(&quot;input[placeholder='代理机构代码']&quot;)
combined_locator = phone_or_id_locator.or</em>(credit_code_locator).or</em>(agency_code_locator)
'''
20241225：  V1.571
bugfix:
1.修复了点击登录按钮失败时，导致程序卡死。
20250114:   V1.58
bugfix:
1.修复了任务队列有时不能正常清空，导致爬取协程不能退出，从而程序僵死的bug。
change:
1.优化了系统刷失败重试的日志文本，更为清晰。
20250115:   V1.581
bugfix:
1.修复了远程验证码服务器不可用时，不能返回默认偏差值的bug，增强了鲁棒性。
20250307:   V1.60
NEW:
1.配置文件增加了程序运行角色（role）选项，用于区分不同程序运行的角色：
- main：部署在固定IP出口的节点上，能够：获取数据 + 写入缓存 + 写入Mysql数据库；
- distributed：部署在分布式节点上，能够：获取数据 + 写入缓存.
2.增加了结果数据处理缓存逻辑：结果数据先定期（60秒）写入Redis缓存，然后再定期（30分钟）从Redis批量写入mysql数据库。
20250308:   V1.61
change:
1.改变了任务调度程序：当ROLE为distributed时，不再承担从Mysql调取任务写入到Redis的职责(该职责由ROLE为main的节点承担)。
bugfix:
1.修复了trans_format函数中，国际分类号（intclass）的生成逻辑中，当主分类或副分类为None时，运算符“+”的报错；
改为：两者任一为None时，则作为空值（&quot;&quot;）处理。
20250419：  V1.62
bugfix:
1.修复了ROLE为MAIN时，把爬取结果数据从Redis批量写入mysql数据库时，写入成功的数据，没有在mysql的任务表中更新状态的bug。
20250625:   V1.63
change:
1.重构了corutine_config.py,增加了配置类，同步修改全部模块的配置引用
20250702：  V1.64
bugfix:
1.类型错误
2.userlogin函数中,拖动验证码过程中，服务器返回不可识别的其他类型错误时，重试5次后退出登录循环。
change:
1.源网站应缴费信息、滞纳金信息格式有变化（没有数据时为Null）,修改trans_format函数，兼容该变化。</p>
<p>20250703：  V1.65
NEW:
1.重构登录模块：将corutine_user_login.py重构为模块化的auth包
2.按照Python最佳实践重新设计异常类体系
3.提供完全向后兼容的接口，支持无缝切换
4.增加详细的文档、测试和使用示例
change:
1.登录模块现在支持更好的错误处理和调试信息
2.提供三种切换方案，推荐使用别名导入实现零代码修改
3.所有测试、文档和示例都整理到auth目录下</p>
<p>20250703：  V1.66
NEW:
1.重构点击模块：将corutine_click.py重构为模块化的click包
2.按照Python最佳实践重新设计模块架构和异常处理体系
3.提供完全向后兼容的接口，支持无缝替换现有模块
4.修复类型注解错误，统一使用asyncio.Future替代concurrent.futures.Future
5.增加详细的文档、测试和验证脚本
change:
1.点击模块现在采用职责分离的设计：查询管理器、数据提取器、文件处理器、同步点击管理器
2.强化异常处理，异常类包含详细的业务上下文和错误信息
3.保持100%业务逻辑一致性，所有核心功能、重试机制、错误处理完全保持原有行为
4.支持三种导入方式，推荐使用别名导入实现零代码修改
5.所有测试、文档和验证脚本都整理到click目录下
20250715:  V1.67
bugfix:
1.修复了DISTRIBUTED角色在Redis无任务时，会阻塞协程60秒的bug。
change:
1.优化了任务调度程序：当ROLE为DISTRIBUTED时，遇到Redis无任务时，直接返回None，不再阻塞协程60秒。
2.优化了日志输出，当Redis无任务时，不再输出重复的日志信息。
20250716:   V1.671
change:
1.改变了页面重试的断言逻辑，先判断标志url（“/detail/index?zhuanlisqh”）是否在detail_page.url中，重试超次数后再进行断言。
20250718：  V1.672
change:
1._login_worker_context函数，失败的话重新调用_login_worker_context函数，无限次重试。</p>
<p>20241004(To finish):   V2.0
NEW:
1.增加获取审查过程中间文件的功能</p>
<h2 id="auth模块重构-v165">Auth模块重构 (V1.65)</h2>
<h3 id="-重构概述">🎯 重构概述</h3>
<p>将原有的单体登录模块 <code>corutine_user_login.py</code> 重构为符合Python最佳实践的模块化 <code>auth</code> 包，提供更好的代码组织、错误处理和可维护性。</p>
<h3 id="-主要改进">✨ 主要改进</h3>
<ul>
<li><strong>模块化设计</strong>: 职责分离，每个模块负责特定功能</li>
<li><strong>强化异常处理</strong>: 详细的异常信息和错误恢复机制</li>
<li><strong>100%向后兼容</strong>: 无需修改现有代码即可切换</li>
<li><strong>完善文档</strong>: 详细的使用指南和迁移文档</li>
</ul>
<h3 id="-快速切换">🚀 快速切换</h3>
<pre><code class="language-python"><span class="hljs-comment"># 原来的代码</span>
<span class="hljs-keyword">from</span> corutine_user_login <span class="hljs-keyword">import</span> user_login, get_page_logined, get_user

<span class="hljs-comment"># 只需修改为（推荐）</span>
<span class="hljs-keyword">import</span> auth <span class="hljs-keyword">as</span> corutine_user_login
<span class="hljs-comment"># 其他代码完全不变！</span>
</code></pre>
<h3 id="-auth模块结构">📁 Auth模块结构</h3>
<pre><code>auth/
├── __init__.py              # 兼容接口
├── exceptions.py            # 异常类定义
├── user_provider.py         # 用户信息管理
├── login_checker.py         # 登录状态检查
├── captcha_solver.py        # 验证码处理
├── login_manager.py         # 登录流程管理
├── utils.py                 # 工具函数
├── docs/                    # 📚 文档目录
│   ├── README.md           # 文档索引
│   ├── AUTH_README.md      # 主要文档
│   ├── MIGRATION_GUIDE.md  # 迁移指南
│   └── EXCEPTION_CLASSES_GUIDE.md
├── tests/                   # 🧪 测试目录
│   ├── run_all_tests.py    # 测试运行器
│   ├── test_auth_module.py # 兼容性测试
│   └── switch_verification.py
└── examples/                # 📖 示例目录
    ├── run_examples.py     # 示例运行器
    └── example_usage.py    # 使用示例
</code></pre>
<h3 id="-验证测试">🧪 验证测试</h3>
<pre><code class="language-bash"><span class="hljs-comment"># 运行所有测试</span>
python auth/tests/run_all_tests.py

<span class="hljs-comment"># 验证切换兼容性</span>
python auth/tests/switch_verification.py

<span class="hljs-comment"># 查看使用示例</span>
python auth/examples/example_usage.py
</code></pre>
<h3 id="-文档">📚 文档</h3>
<ul>
<li><strong>快速开始</strong>: <a href="auth/docs/AUTH_README.html">auth/docs/AUTH_README.md</a></li>
<li><strong>迁移指南</strong>: <a href="auth/docs/MIGRATION_GUIDE.html">auth/docs/MIGRATION_GUIDE.md</a></li>
<li><strong>异常处理</strong>: <a href="auth/docs/EXCEPTION_CLASSES_GUIDE.html">auth/docs/EXCEPTION_CLASSES_GUIDE.md</a></li>
<li><strong>完整报告</strong>: <a href="auth/docs/AUTH_MODULE_REFACTOR_REPORT.html">auth/docs/AUTH_MODULE_REFACTOR_REPORT.md</a></li>
</ul>
<hr>
<h2 id="click模块重构-v166">Click模块重构 (V1.66)</h2>
<h3 id="-重构概述-1">🎯 重构概述</h3>
<p>将原有的单体点击模块 <code>corutine_click.py</code> 重构为符合Python最佳实践的模块化 <code>click</code> 包，提供更好的代码组织、错误处理和可维护性，同时修复了类型注解错误。</p>
<h3 id="-主要改进-1">✨ 主要改进</h3>
<ul>
<li><strong>模块化设计</strong>: 职责分离，查询管理器、数据提取器、文件处理器、同步点击管理器</li>
<li><strong>强化异常处理</strong>: 详细的异常信息和业务上下文，包含原模块所有业务细节</li>
<li><strong>100%向后兼容</strong>: 无需修改现有spider代码即可切换</li>
<li><strong>类型注解修复</strong>: 统一使用asyncio.Future，消除IDE类型提示错误</li>
<li><strong>完善文档</strong>: 详细的验证报告和业务逻辑对比分析</li>
</ul>
<h3 id="-快速切换-1">🚀 快速切换</h3>
<pre><code class="language-python"><span class="hljs-comment"># 原来的代码</span>
<span class="hljs-keyword">from</span> corutine_click <span class="hljs-keyword">import</span> click_event_button_and_get_data, click_event_button_and_get_data_sync

<span class="hljs-comment"># 只需修改为（推荐）</span>
<span class="hljs-keyword">from</span> click <span class="hljs-keyword">import</span> click_event_button_and_get_data, click_event_button_and_get_data_sync
<span class="hljs-comment"># 或者使用别名导入实现零代码修改</span>
<span class="hljs-keyword">import</span> click <span class="hljs-keyword">as</span> corutine_click
</code></pre>
<h3 id="-click模块结构">📁 Click模块结构</h3>
<pre><code>click/
├── __init__.py                    # 兼容接口，提供与原模块相同的函数
├── exceptions.py                  # 异常类定义，包含详细业务上下文
├── query_manager.py               # 主页查询管理器
├── data_extractor.py              # 数据提取管理器
├── file_handler.py                # 文件处理管理器
├── sync_click_manager.py          # 同步点击管理器
├── docs/                          # 📚 文档目录
│   ├── README.md                 # 文档索引
│   └── CLICK_README.md           # 主要文档
├── tests/                         # 🧪 测试目录
│   ├── run_all_tests.py          # 测试运行器
│   ├── test_click_compatibility.py # 兼容性测试
│   ├── simple_verification.py    # 简单验证脚本
│   ├── final_verification.py     # 最终验证脚本
│   └── type_check_verification.py # 类型注解验证
├── examples/                      # 📖 示例目录
│   ├── run_examples.py           # 示例运行器
│   └── __init__.py
├── BUSINESS_LOGIC_VERIFICATION_REPORT.md  # 业务逻辑验证报告
├── TYPE_ANNOTATION_FIX_REPORT.md          # 类型注解修复报告
└── FINAL_SUMMARY.md                       # 重构总结报告
</code></pre>
<h3 id="-验证结果">✅ 验证结果</h3>
<ul>
<li><strong>业务逻辑一致性</strong>: 100% - 所有核心业务流程、重试机制、错误处理完全保持</li>
<li><strong>异常处理完整性</strong>: 100% - 异常类包含原模块所有业务细节和错误信息</li>
<li><strong>运行效果一致性</strong>: 100% - 同步/异步模式、数据处理、页面操作完全一致</li>
<li><strong>中文编码正确性</strong>: 100% - 所有文件UTF-8编码，中文显示正常</li>
<li><strong>类型注解正确性</strong>: 100% - 修复所有类型提示错误，支持IDE智能提示</li>
</ul>
<h3 id="-验证测试-1">🧪 验证测试</h3>
<pre><code class="language-bash"><span class="hljs-comment"># 运行基础验证</span>
python click/tests/simple_verification.py

<span class="hljs-comment"># 运行最终验证</span>
python click/tests/final_verification.py

<span class="hljs-comment"># 验证类型注解修复</span>
python click/tests/type_check_verification.py
</code></pre>
<h3 id="-文档-1">📚 文档</h3>
<ul>
<li><strong>主要文档</strong>: <a href="click/docs/CLICK_README.html">click/docs/CLICK_README.md</a></li>
<li><strong>业务逻辑验证</strong>: <a href="click/BUSINESS_LOGIC_VERIFICATION_REPORT.html">click/BUSINESS_LOGIC_VERIFICATION_REPORT.md</a></li>
<li><strong>类型注解修复</strong>: <a href="click/TYPE_ANNOTATION_FIX_REPORT.html">click/TYPE_ANNOTATION_FIX_REPORT.md</a></li>
<li><strong>重构总结</strong>: <a href="click/FINAL_SUMMARY.html">click/FINAL_SUMMARY.md</a></li>
</ul>
<hr>
<p>RoadMap:
todo : 日志文件保存逻辑优化，尽量每次运行生成一个文件。
todo : 多进程嵌套，多个playwright实例，每个实例多个页面
todo ：尝试在linux上直接部署和docker部署
tddo : 使用代理</p>

            
            
        </body>
        </html>