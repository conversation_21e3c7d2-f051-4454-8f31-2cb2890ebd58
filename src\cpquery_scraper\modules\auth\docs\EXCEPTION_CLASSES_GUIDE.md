# 异常类使用指南

## 概述

重构后的登录模块提供了完善的异常类体系，每个异常类都包含详细的错误信息和上下文数据，便于调试和错误处理。

## 异常类层次结构

```
LoginError (基础异常类)
├── CaptchaError (验证码异常)
├── CredentialsError (用户凭据异常)
├── NetworkError (网络异常)
├── PageCrashedError (页面崩溃异常)
├── TimeoutError (超时异常)
├── ConfigurationError (配置异常)
├── LoginTimeoutError (登录超时异常，继承自TimeoutError)
└── CaptchaTimeoutError (验证码超时异常，继承自CaptchaError和TimeoutError)
```

## 基础异常类 - LoginError

所有登录相关异常的基类，提供统一的错误处理接口。

### 属性
- `message`: 错误消息
- `error_code`: 错误代码（可选）
- `details`: 详细错误信息字典（可选）

### 使用示例
```python
from auth.exceptions import LoginError

try:
    # 登录操作
    pass
except LoginError as e:
    print(f"登录错误: {e}")
    print(f"错误码: {e.error_code}")
    print(f"详细信息: {e.details}")
```

## 具体异常类

### 1. CredentialsError - 用户凭据异常

当用户名、密码或用户类型错误时抛出。

#### 特有属性
- `user_id`: 用户ID（自动掩码保护隐私）
- `user_type`: 用户类型

#### 使用示例
```python
from auth.exceptions import CredentialsError

# 抛出异常
raise CredentialsError(
    "用户名或密码错误",
    error_code="INVALID_CREDENTIALS",
    user_id="1234567890123456",
    user_type="自然人"
)

# 捕获异常
try:
    # 登录操作
    pass
except CredentialsError as e:
    print(f"凭据错误: {e}")
    print(f"用户类型: {e.user_type}")
    print(f"用户ID: {e.user_id}")  # 输出: 1234************
```

### 2. CaptchaError - 验证码异常

当验证码识别、处理或验证失败时抛出。

#### 特有属性
- `captcha_type`: 验证码类型（如 'blockPuzzle'）
- `retry_count`: 已重试次数

#### 使用示例
```python
from auth.exceptions import CaptchaError

# 抛出异常
raise CaptchaError(
    "验证码识别失败",
    error_code="CAPTCHA_FAILED",
    captcha_type="blockPuzzle",
    retry_count=3
)

# 捕获异常
try:
    # 验证码处理
    pass
except CaptchaError as e:
    print(f"验证码错误: {e}")
    print(f"验证码类型: {e.captcha_type}")
    print(f"重试次数: {e.retry_count}")
```

### 3. NetworkError - 网络异常

当网络请求失败、超时或连接问题时抛出。

#### 特有属性
- `url`: 请求的URL
- `status_code`: HTTP状态码
- `timeout`: 超时时间

#### 使用示例
```python
from auth.exceptions import NetworkError

# 抛出异常
raise NetworkError(
    "网络连接超时",
    error_code="NETWORK_TIMEOUT",
    url="https://example.com/api",
    status_code=408,
    timeout=30.0
)

# 捕获异常
try:
    # 网络请求
    pass
except NetworkError as e:
    print(f"网络错误: {e}")
    print(f"URL: {e.url}")
    print(f"状态码: {e.status_code}")
```

### 4. PageCrashedError - 页面崩溃异常

当浏览器页面崩溃或上下文关闭时抛出。

#### 特有属性
- `page_url`: 崩溃页面的URL
- `crash_reason`: 崩溃原因

#### 使用示例
```python
from auth.exceptions import PageCrashedError

# 抛出异常
raise PageCrashedError(
    "浏览器页面已崩溃",
    error_code="PAGE_CRASHED",
    page_url="https://example.com/login",
    crash_reason="Target closed"
)
```

### 5. TimeoutError - 超时异常

当登录操作超过预期时间时抛出。

#### 特有属性
- `operation`: 超时的操作名称
- `timeout_seconds`: 超时时间（秒）

#### 使用示例
```python
from auth.exceptions import TimeoutError

# 抛出异常
raise TimeoutError(
    "登录操作超时",
    error_code="OPERATION_TIMEOUT",
    operation="user_login",
    timeout_seconds=60.0
)
```

### 6. ConfigurationError - 配置异常

当配置项缺失或无效时抛出。

#### 特有属性
- `config_key`: 配置项键名
- `config_value`: 配置项值

#### 使用示例
```python
from auth.exceptions import ConfigurationError

# 抛出异常
raise ConfigurationError(
    "缺少必需的配置项",
    error_code="MISSING_CONFIG",
    config_key="AUTH_MAINPAGE_URL",
    config_value=None
)
```

## 最佳实践

### 1. 异常处理顺序

按照从具体到一般的顺序捕获异常：

```python
from auth.exceptions import (
    CredentialsError, CaptchaError, NetworkError, 
    PageCrashedError, TimeoutError, LoginError
)

try:
    # 登录操作
    result = await auth.user_login(name, page, user_info, url)
except CredentialsError as e:
    # 处理用户凭据错误
    print(f"用户凭据错误: {e}")
    # 可能需要更换用户或提示用户检查凭据
except CaptchaError as e:
    # 处理验证码错误
    print(f"验证码错误: {e}")
    # 可能需要重试验证码
except NetworkError as e:
    # 处理网络错误
    print(f"网络错误: {e}")
    # 可能需要检查网络连接或重试
except PageCrashedError as e:
    # 处理页面崩溃
    print(f"页面崩溃: {e}")
    # 需要重新创建页面
except TimeoutError as e:
    # 处理超时错误
    print(f"操作超时: {e}")
    # 可能需要增加超时时间或重试
except LoginError as e:
    # 处理其他登录错误
    print(f"登录错误: {e}")
except Exception as e:
    # 处理未知错误
    print(f"未知错误: {e}")
```

### 2. 错误日志记录

利用异常的详细信息进行日志记录：

```python
import logging

def log_login_error(exc):
    """记录登录错误的详细信息"""
    logger = logging.getLogger(__name__)
    
    # 基本错误信息
    logger.error(f"{exc.__class__.__name__}: {exc}")
    
    # 错误码
    if hasattr(exc, 'error_code') and exc.error_code:
        logger.error(f"错误码: {exc.error_code}")
    
    # 详细信息
    if hasattr(exc, 'details') and exc.details:
        for key, value in exc.details.items():
            logger.error(f"{key}: {value}")
    
    # 特定异常的额外信息
    if isinstance(exc, CredentialsError):
        logger.error(f"用户类型: {exc.user_type}")
    elif isinstance(exc, NetworkError):
        logger.error(f"URL: {exc.url}, 状态码: {exc.status_code}")
    elif isinstance(exc, CaptchaError):
        logger.error(f"验证码类型: {exc.captcha_type}, 重试次数: {exc.retry_count}")
```

### 3. 错误恢复策略

根据不同的异常类型采用不同的恢复策略：

```python
async def robust_login(name, page, max_retries=3):
    """带有错误恢复的登录函数"""
    for attempt in range(max_retries):
        try:
            user_info = auth.get_user()
            result = await auth.user_login(name, page, user_info, config.CPQUERY_URL)
            return result
            
        except CredentialsError as e:
            # 用户凭据错误，尝试其他用户
            print(f"用户凭据错误，尝试其他用户: {e}")
            continue
            
        except CaptchaError as e:
            # 验证码错误，短暂等待后重试
            print(f"验证码错误，等待后重试: {e}")
            await asyncio.sleep(2)
            continue
            
        except NetworkError as e:
            # 网络错误，等待较长时间后重试
            print(f"网络错误，等待后重试: {e}")
            await asyncio.sleep(10)
            continue
            
        except PageCrashedError as e:
            # 页面崩溃，需要重新创建页面
            print(f"页面崩溃，重新创建页面: {e}")
            # 这里需要重新创建页面的逻辑
            raise  # 暂时重新抛出，需要上层处理
            
        except TimeoutError as e:
            # 超时错误，可能需要增加超时时间
            print(f"操作超时: {e}")
            continue
    
    raise LoginError(f"登录失败，已重试{max_retries}次")
```

## 向后兼容性

新的异常类完全向后兼容，原有的异常处理代码无需修改：

```python
# 原有代码仍然有效
try:
    result = await auth.user_login(name, page, user_info, url)
except Exception as e:
    print(f"登录失败: {e}")

# 新代码可以获得更详细的信息
try:
    result = await auth.user_login(name, page, user_info, url)
except LoginError as e:
    print(f"登录失败: {e}")
    print(f"错误码: {e.error_code}")
    print(f"详细信息: {e.details}")
```

## 总结

改进后的异常类提供了：

1. **详细的错误信息**: 每个异常都包含相关的上下文信息
2. **结构化的错误数据**: 便于程序化处理和日志记录
3. **隐私保护**: 自动掩码敏感信息如用户ID
4. **向后兼容**: 不影响现有代码的正常运行
5. **易于调试**: 提供足够的信息帮助定位问题

这些改进使得错误处理更加专业和实用，符合Python最佳实践。
