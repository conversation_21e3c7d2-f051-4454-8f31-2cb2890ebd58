"""
Legacy compatibility shim for result_processor.py

This module provides backward compatibility for result processing functionality.
It contains the ResultQueueProcessor class with start_scheduled_tasks method
that handles the result queue → Redis → MySQL pipeline.

The original result_processor.py provided:
1. ResultQueueProcessor class with start_scheduled_tasks method
2. Periodic result queue processing to Redis
3. Main node Redis → MySQL synchronization
4. Proper Redis key naming and synced status management
"""

import asyncio
import time
from typing import Optional

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.pipelines.redis_pipeline import RedisPipeline
from src.cpquery_scraper.pipelines.mysql_pipeline import MySQLPipeline

logger = get_logger(__name__)

class ResultQueueProcessor:
    """
    Result queue processor that handles the pipeline from result queue to Redis to MySQL
    
    This class replicates the original ResultQueueProcessor behavior:
    - Periodic processing of result queue to Redis
    - Main node synchronization from Redis to MySQL
    - Proper key naming and status management
    - Batch processing with configurable intervals
    """
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.redis_pipeline = RedisPipeline()
        self.mysql_pipeline = MySQLPipeline()
        self.is_running = False
        
    async def start_scheduled_tasks(self, result_queue: asyncio.Queue):
        """
        Start scheduled result processing tasks
        
        This method replicates the original start_scheduled_tasks behavior:
        - Periodically reads all messages from result_queue
        - Calls trans_format to convert to business table structure  
        - Writes to Redis (db=2) with "patent_id:{an}" keys
        - Main node periodically syncs Redis data to MySQL in batches of 500
        - Updates synced=1 and deletes synced data from Redis
        - Updates task table epatent_0.state=1 after successful sync
        
        Args:
            result_queue: asyncio.Queue containing result data to process
        """
        self.logger.info("Starting scheduled result processing tasks")
        self.is_running = True
        
        # Initialize pipelines
        await self.redis_pipeline.open_spider(spider=None)
        await self.mysql_pipeline.open_spider(spider=None)
        
        try:
            # Start background tasks
            tasks = []
            
            # Task 1: Result queue → Redis processing
            tasks.append(asyncio.create_task(
                self._process_results_to_redis(result_queue)
            ))
            
            # Task 2: Redis → MySQL sync (main node only)
            if self.mysql_pipeline.is_main_role:
                tasks.append(asyncio.create_task(
                    self._sync_redis_to_mysql()
                ))
            
            # Wait for all tasks
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            self.logger.error(f"Error in start_scheduled_tasks: {e}")
        finally:
            self.is_running = False
            await self.redis_pipeline.close_spider(spider=None)
            await self.mysql_pipeline.close_spider(spider=None)
            self.logger.info("Scheduled result processing tasks stopped")
    
    async def _process_results_to_redis(self, result_queue: asyncio.Queue):
        """
        Process results from queue to Redis with configured interval
        
        Args:
            result_queue: Queue containing result data
        """
        self.logger.info("Starting result queue → Redis processing")
        
        while self.is_running and config.FLAG_SPIDER_RUNNING:
            try:
                # Collect batch over interval period
                batch = []
                end_time = time.time() + config.RESULT_TO_REDIS_INTERVAL
                
                while time.time() < end_time and self.is_running and config.FLAG_SPIDER_RUNNING:
                    try:
                        item = await asyncio.wait_for(result_queue.get(), timeout=1.0)
                        batch.append(item)
                        result_queue.task_done()
                    except asyncio.TimeoutError:
                        continue
                
                # Process batch to Redis
                if batch:
                    self.logger.info(f"Processing {len(batch)} results to Redis")
                    success_count = await self.redis_pipeline.process_items_batch(batch)
                    self.logger.info(f"Successfully wrote {success_count} items to Redis")
                else:
                    self.logger.debug("No results to process in this interval")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in result queue → Redis processing: {e}")
                await asyncio.sleep(5)
        
        self.logger.info("Result queue → Redis processing stopped")
    
    async def _sync_redis_to_mysql(self):
        """
        Sync data from Redis to MySQL with configured interval (main node only)
        """
        self.logger.info("Starting Redis → MySQL sync (main node)")
        
        while self.is_running and config.FLAG_SPIDER_RUNNING:
            try:
                # Wait for sync interval
                await asyncio.sleep(config.RESULT_TO_MYSQL_INTERVAL)
                
                if not self.is_running or not config.FLAG_SPIDER_RUNNING:
                    break
                
                # Execute sync task
                await self.mysql_pipeline.run_sync_task()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in Redis → MySQL sync: {e}")
                await asyncio.sleep(60)  # Wait longer on error
        
        self.logger.info("Redis → MySQL sync stopped")
    
    async def stop(self):
        """Stop the result processor"""
        self.logger.info("Stopping result processor")
        self.is_running = False

# Legacy compatibility - allow direct instantiation
def create_result_processor() -> ResultQueueProcessor:
    """Create a new result processor instance"""
    return ResultQueueProcessor()

# Legacy compatibility exports
__all__ = [
    'ResultQueueProcessor',
    'create_result_processor'
]
