"""
测试改进后的异常类
验证异常类的功能和信息完整性
"""
import traceback
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from auth.exceptions import (
    LoginError, CaptchaError, CredentialsError, NetworkError,
    PageCrashedError, TimeoutError, ConfigurationError,
    LoginTimeoutError, CaptchaTimeoutError
)


def test_base_login_error():
    """测试基础登录异常"""
    print("=== 测试基础登录异常 ===")
    
    # 基本异常
    try:
        raise LoginError("基本登录错误")
    except LoginError as e:
        print(f"基本异常: {e}")
        print(f"异常表示: {repr(e)}")
    
    # 带错误码的异常
    try:
        raise LoginError("带错误码的登录错误", error_code="LOGIN_001")
    except LoginError as e:
        print(f"带错误码: {e}")
        print(f"错误码: {e.error_code}")
    
    # 带详细信息的异常
    try:
        raise LoginError(
            "详细登录错误", 
            error_code="LOGIN_002",
            details={"step": "authentication", "attempt": 3}
        )
    except LoginError as e:
        print(f"详细异常: {e}")
        print(f"详细信息: {e.details}")


def test_credentials_error():
    """测试用户凭据异常"""
    print("\n=== 测试用户凭据异常 ===")
    
    try:
        raise CredentialsError(
            "用户名或密码错误",
            error_code="INVALID_CREDENTIALS",
            user_id="1234567890123456",  # 长用户ID，会被掩码
            user_type="自然人"
        )
    except CredentialsError as e:
        print(f"凭据异常: {e}")
        print(f"用户ID: {e.user_id}")
        print(f"用户类型: {e.user_type}")
        print(f"详细信息: {e.details}")


def test_captcha_error():
    """测试验证码异常"""
    print("\n=== 测试验证码异常 ===")
    
    try:
        raise CaptchaError(
            "验证码识别失败",
            error_code="CAPTCHA_FAILED",
            captcha_type="blockPuzzle",
            retry_count=3
        )
    except CaptchaError as e:
        print(f"验证码异常: {e}")
        print(f"验证码类型: {e.captcha_type}")
        print(f"重试次数: {e.retry_count}")
        print(f"详细信息: {e.details}")


def test_network_error():
    """测试网络异常"""
    print("\n=== 测试网络异常 ===")
    
    try:
        raise NetworkError(
            "网络连接超时",
            error_code="NETWORK_TIMEOUT",
            url="https://example.com/api",
            status_code=408,
            timeout=30.0
        )
    except NetworkError as e:
        print(f"网络异常: {e}")
        print(f"URL: {e.url}")
        print(f"状态码: {e.status_code}")
        print(f"超时时间: {e.timeout}")
        print(f"详细信息: {e.details}")


def test_page_crashed_error():
    """测试页面崩溃异常"""
    print("\n=== 测试页面崩溃异常 ===")
    
    try:
        raise PageCrashedError(
            "浏览器页面已崩溃",
            error_code="PAGE_CRASHED",
            page_url="https://example.com/login",
            crash_reason="Target closed"
        )
    except PageCrashedError as e:
        print(f"页面崩溃异常: {e}")
        print(f"页面URL: {e.page_url}")
        print(f"崩溃原因: {e.crash_reason}")
        print(f"详细信息: {e.details}")


def test_timeout_error():
    """测试超时异常"""
    print("\n=== 测试超时异常 ===")
    
    try:
        raise TimeoutError(
            "登录操作超时",
            error_code="OPERATION_TIMEOUT",
            operation="user_login",
            timeout_seconds=60.0
        )
    except TimeoutError as e:
        print(f"超时异常: {e}")
        print(f"操作: {e.operation}")
        print(f"超时时间: {e.timeout_seconds}秒")
        print(f"详细信息: {e.details}")


def test_configuration_error():
    """测试配置异常"""
    print("\n=== 测试配置异常 ===")
    
    try:
        raise ConfigurationError(
            "缺少必需的配置项",
            error_code="MISSING_CONFIG",
            config_key="AUTH_MAINPAGE_URL",
            config_value=None
        )
    except ConfigurationError as e:
        print(f"配置异常: {e}")
        print(f"配置键: {e.config_key}")
        print(f"配置值: {e.config_value}")
        print(f"详细信息: {e.details}")


def test_inheritance():
    """测试异常继承关系"""
    print("\n=== 测试异常继承关系 ===")
    
    # 测试所有异常都继承自LoginError
    exceptions_to_test = [
        CaptchaError("test"),
        CredentialsError("test"),
        NetworkError("test"),
        PageCrashedError("test"),
        TimeoutError("test"),
        ConfigurationError("test"),
        LoginTimeoutError("test"),
        CaptchaTimeoutError("test")
    ]
    
    for exc in exceptions_to_test:
        is_login_error = isinstance(exc, LoginError)
        print(f"{exc.__class__.__name__} 是 LoginError 的子类: {is_login_error}")
    
    # 测试多重继承
    captcha_timeout = CaptchaTimeoutError("test")
    print(f"CaptchaTimeoutError 是 CaptchaError 的子类: {isinstance(captcha_timeout, CaptchaError)}")
    print(f"CaptchaTimeoutError 是 TimeoutError 的子类: {isinstance(captcha_timeout, TimeoutError)}")


def test_exception_handling():
    """测试异常处理场景"""
    print("\n=== 测试异常处理场景 ===")
    
    def simulate_login_process():
        """模拟登录过程中的各种异常"""
        import random
        
        error_types = [
            lambda: CredentialsError("密码错误", error_code="100006", user_id="test123"),
            lambda: CaptchaError("验证码错误", error_code="100013", retry_count=2),
            lambda: NetworkError("网络超时", url="https://example.com", timeout=30),
            lambda: PageCrashedError("页面崩溃", crash_reason="Target closed"),
            lambda: TimeoutError("操作超时", operation="login", timeout_seconds=60)
        ]
        
        # 随机抛出一个异常
        raise random.choice(error_types)()
    
    # 测试统一异常处理
    try:
        simulate_login_process()
    except CredentialsError as e:
        print(f"处理凭据错误: {e} (错误码: {e.error_code})")
    except CaptchaError as e:
        print(f"处理验证码错误: {e} (重试次数: {e.retry_count})")
    except NetworkError as e:
        print(f"处理网络错误: {e} (URL: {e.url})")
    except PageCrashedError as e:
        print(f"处理页面崩溃: {e} (原因: {e.crash_reason})")
    except TimeoutError as e:
        print(f"处理超时错误: {e} (操作: {e.operation})")
    except LoginError as e:
        print(f"处理通用登录错误: {e}")
    except Exception as e:
        print(f"处理未知错误: {e}")


def test_error_logging():
    """测试错误日志记录"""
    print("\n=== 测试错误日志记录 ===")
    
    def log_exception(exc):
        """模拟日志记录函数"""
        print(f"[ERROR] {exc.__class__.__name__}: {exc}")
        if hasattr(exc, 'error_code') and exc.error_code:
            print(f"[ERROR] 错误码: {exc.error_code}")
        if hasattr(exc, 'details') and exc.details:
            print(f"[ERROR] 详细信息: {exc.details}")
    
    # 测试各种异常的日志记录
    exceptions = [
        CredentialsError("用户名错误", error_code="INVALID_USER", user_id="test123"),
        NetworkError("连接失败", url="https://example.com", status_code=500),
        CaptchaError("验证码超时", captcha_type="slider", retry_count=5)
    ]
    
    for exc in exceptions:
        log_exception(exc)
        print()


def main():
    """运行所有测试"""
    print("开始测试改进后的异常类...")
    
    test_base_login_error()
    test_credentials_error()
    test_captcha_error()
    test_network_error()
    test_page_crashed_error()
    test_timeout_error()
    test_configuration_error()
    test_inheritance()
    test_exception_handling()
    test_error_logging()
    
    print("\n所有异常类测试完成！")


if __name__ == "__main__":
    main()
