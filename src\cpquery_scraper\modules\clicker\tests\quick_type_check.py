#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速类型注解检查脚本
"""
import os
import ast
import re

def check_type_annotations():
    """检查类型注解"""
    click_dir = os.path.dirname(os.path.dirname(__file__))
    
    print("快速类型注解检查:")
    print("=" * 50)
    
    # 检查的文件
    files_to_check = [
        "__init__.py",
        "data_extractor.py", 
        "file_handler.py",
        "query_manager.py",
        "sync_click_manager.py",
        "exceptions.py"
    ]
    
    issues = []
    
    for filename in files_to_check:
        file_path = os.path.join(click_dir, filename)
        
        if not os.path.exists(file_path):
            issues.append(f"{filename}: 文件不存在")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 语法检查
            try:
                ast.parse(content)
                print(f"✅ {filename}: 语法正确")
            except SyntaxError as e:
                issues.append(f"{filename}: 语法错误 - {e}")
                print(f"❌ {filename}: 语法错误 - {e}")
                continue
            
            # 检查类型注解问题
            file_issues = []
            
            # 1. 检查是否还有concurrent.futures导入
            if 'concurrent.futures' in content:
                file_issues.append("仍有concurrent.futures导入")
            
            # 2. 检查Future类型注解
            if re.search(r':\s*Future\[', content) or re.search(r'->\s*Future\[', content):
                file_issues.append("使用了Future而不是asyncio.Future")
            
            # 3. 检查Dict vs dict一致性
            dict_upper = len(re.findall(r'Dict\[', content))
            dict_lower = len(re.findall(r'dict\[', content))
            
            if dict_upper > 0 and dict_lower > 0:
                file_issues.append(f"混用Dict和dict类型 (Dict: {dict_upper}, dict: {dict_lower})")
            
            if file_issues:
                issues.extend([f"{filename}: {issue}" for issue in file_issues])
                print(f"⚠️  {filename}: {len(file_issues)} 个问题")
                for issue in file_issues:
                    print(f"   - {issue}")
            else:
                print(f"✅ {filename}: 类型注解正确")
                
        except Exception as e:
            issues.append(f"{filename}: 检查失败 - {e}")
            print(f"❌ {filename}: 检查失败 - {e}")
    
    print("\n" + "=" * 50)
    if issues:
        print(f"发现 {len(issues)} 个问题:")
        for issue in issues:
            print(f"❌ {issue}")
        return False
    else:
        print("🎉 所有类型注解检查通过！")
        return True

if __name__ == "__main__":
    success = check_type_annotations()
    exit(0 if success else 1)
