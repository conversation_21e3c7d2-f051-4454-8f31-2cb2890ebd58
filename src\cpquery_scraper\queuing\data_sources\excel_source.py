"""
Excel数据源实现

实现了从Excel文件读取任务的功能。

Author: wwind
Date: 2025.07.08
"""

import os
import glob
import shutil
from typing import List, Optional

from ..models import TaskItem, DataSourceConfig, DataSourceType, TaskStatus
from ..exceptions import FileOperationError, DataSourceError
from .base import BaseDataSource, DataSourceFactory

# 尝试导入openpyxl
try:
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    load_workbook = None


class ExcelDataSource(BaseDataSource):
    """
    Excel数据源实现
    
    从Excel文件中读取任务列表
    """
    
    def __init__(self, config: DataSourceConfig, logger=None):
        """
        初始化Excel数据源
        
        Args:
            config: 数据源配置
            logger: 日志记录器
        """
        if config.source_type != DataSourceType.EXCEL:
            raise DataSourceError(
                f"Invalid source type for Excel data source: {config.source_type.value}",
                source_type=config.source_type.value,
                error_code="INVALID_CONFIG"
            )
        
        super().__init__(config, logger)
        
        # 获取Excel文件路径
        self._excel_dir = config.connection_params.get('excel_dir', './task_excel/')
        self._processed_dir = config.connection_params.get('processed_dir', './task_excel/已处理/')
        self._file_pattern = config.connection_params.get('file_pattern', '*.xlsx')
        
        # 支持的列名
        self._column_names = config.connection_params.get(
            'column_names', 
            ["专利申请号", "申请号", "专利号/专利申请号", "专利号"]
        )
        
        # 验证openpyxl是否可用
        if not OPENPYXL_AVAILABLE:
            raise FileOperationError(
                "openpyxl library is not available. Please install it to use Excel data source.",
                operation="import",
                error_code="DEPENDENCY_MISSING"
            )
        
        # 验证Excel目录
        if not os.path.exists(self._excel_dir):
            raise FileOperationError(
                f"Excel directory does not exist: {self._excel_dir}",
                file_path=self._excel_dir,
                operation="access",
                error_code="DIRECTORY_NOT_FOUND"
            )
    
    async def connect(self) -> None:
        """连接到Excel数据源（检查目录和文件）"""
        try:
            # 检查Excel目录是否存在
            if not os.path.exists(self._excel_dir):
                raise FileOperationError(
                    f"Excel directory not found: {self._excel_dir}",
                    file_path=self._excel_dir,
                    operation="access"
                )
            
            # 创建已处理目录（如果不存在）
            os.makedirs(self._processed_dir, exist_ok=True)
            
            self._is_connected = True
            self._log_operation("Connected to Excel data source", 
                              {'excel_dir': self._excel_dir})
            
        except Exception as e:
            self._is_connected = False
            raise self._handle_error(e, "connect to Excel data source")
    
    async def disconnect(self) -> None:
        """断开Excel数据源连接"""
        self._is_connected = False
        self._log_operation("Disconnected from Excel data source")
    
    async def fetch_tasks(self, limit: Optional[int] = None) -> List[TaskItem]:
        """
        从Excel文件获取任务列表

        Args:
            limit: 限制返回的任务数量（对Excel数据源无效）

        Returns:
            任务项列表
        """
        # limit参数对Excel数据源无效，忽略
        _ = limit
        if not self._is_connected:
            raise FileOperationError(
                "Not connected to Excel data source",
                operation="fetch_tasks",
                error_code="NOT_CONNECTED"
            )
        
        try:
            # 获取Excel文件列表
            file_pattern = os.path.join(self._excel_dir, self._file_pattern)
            excel_files = glob.glob(file_pattern)
            
            if not excel_files:
                self.logger.info(f"No Excel files found in {self._excel_dir}")
                return []
            
            all_tasks = []
            
            # 处理每个Excel文件
            for file_path in excel_files:
                try:
                    tasks = await self._process_excel_file(file_path)
                    all_tasks.extend(tasks)
                    
                    # 移动已处理的文件
                    await self._move_processed_file(file_path)
                    
                except Exception as e:
                    self.logger.error(f"Failed to process Excel file {file_path}: {e}")
                    continue
            
            self._log_operation("Fetched tasks from Excel files",
                              {'file_count': len(excel_files), 'task_count': len(all_tasks)})
            return all_tasks
            
        except Exception as e:
            raise self._handle_error(e, "fetch tasks from Excel files")
    
    async def _process_excel_file(self, file_path: str) -> List[TaskItem]:
        """
        处理单个Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            任务项列表
        """
        tasks = []
        
        try:
            # 检查openpyxl是否可用
            if not OPENPYXL_AVAILABLE or load_workbook is None:
                raise DataSourceError(
                    "openpyxl library not available",
                    source_type="excel",
                    error_code="DEPENDENCY_MISSING"
                )

            # 加载工作簿
            workbook = load_workbook(filename=file_path, read_only=True)
            
            # 处理每个工作表
            for worksheet in workbook:
                sheet_tasks = await self._process_worksheet(worksheet, file_path)
                tasks.extend(sheet_tasks)
            
            workbook.close()
            
            self.logger.info(f"Processed Excel file {file_path}: {len(tasks)} tasks")
            return tasks
            
        except Exception as e:
            raise FileOperationError(
                f"Failed to process Excel file: {e}",
                file_path=file_path,
                operation="read"
            )
    
    async def _process_worksheet(self, worksheet, file_path: str) -> List[TaskItem]:
        """
        处理单个工作表
        
        Args:
            worksheet: 工作表对象
            file_path: 文件路径（用于日志）
            
        Returns:
            任务项列表
        """
        tasks = []
        
        try:
            # 获取列名
            if worksheet.max_row < 1:
                return tasks
            
            # 读取第一行作为列名
            columns = [cell.value for cell in worksheet[1]]
            
            # 找到申请号列
            an_column_index = None
            for i, column_name in enumerate(columns):
                if column_name in self._column_names:
                    an_column_index = i
                    break
            
            if an_column_index is None:
                self.logger.warning(f"No valid application number column found in {file_path}")
                return tasks
            
            # 读取数据行
            for row in worksheet.iter_rows(min_row=2, values_only=True):
                if len(row) <= an_column_index:
                    continue
                
                an_value = row[an_column_index]
                if an_value is None:
                    continue
                
                # 清理申请号
                task_id = str(an_value).replace(".", "").strip("ZL").strip()
                
                # 创建任务项
                task_item = TaskItem(
                    task_id=task_id,
                    status=TaskStatus.PENDING
                )
                
                # 验证任务项
                if task_item.is_valid():
                    tasks.append(task_item)
                else:
                    self.logger.warning(f"Invalid task ID in Excel: {task_id}")
            
            return tasks
            
        except Exception as e:
            raise FileOperationError(
                f"Failed to process worksheet: {e}",
                file_path=file_path,
                operation="parse"
            )
    
    async def _move_processed_file(self, file_path: str) -> None:
        """
        移动已处理的文件到已处理目录
        
        Args:
            file_path: 文件路径
        """
        try:
            # 确保已处理目录存在
            os.makedirs(self._processed_dir, exist_ok=True)
            
            # 移动文件
            file_name = os.path.basename(file_path)
            destination = os.path.join(self._processed_dir, file_name)
            shutil.move(file_path, destination)
            
            self.logger.info(f"Moved processed file to {destination}")
            
        except Exception as e:
            self.logger.error(f"Failed to move processed file {file_path}: {e}")
            # 不抛出异常，因为文件移动失败不应该影响任务处理
    
    async def update_task_status(self, task_id: str, status: int) -> bool:
        """
        更新任务状态（Excel数据源不支持状态更新）
        
        Args:
            task_id: 任务ID
            status: 新状态
            
        Returns:
            总是返回True（Excel数据源不需要状态更新）
        """
        # Excel数据源是只读的，不支持状态更新
        # 这里返回True表示操作"成功"，但实际上没有执行任何操作
        # 忽略未使用的参数
        _ = status
        self.logger.debug(f"Excel data source does not support status update for task {task_id}")
        return True
    
    async def get_task_count(self, status: Optional[int] = None) -> int:
        """
        获取任务数量
        
        Args:
            status: 任务状态过滤条件（对Excel数据源无效）
            
        Returns:
            任务数量
        """
        try:
            # status参数对Excel数据源无效，忽略
            _ = status
            # 对于Excel数据源，需要读取所有文件来计算任务数量
            tasks = await self.fetch_tasks()
            return len(tasks)
        except Exception as e:
            raise self._handle_error(e, "get task count from Excel files")
    
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            数据源是否健康
        """
        try:
            # 检查Excel目录是否存在且可访问
            return os.path.exists(self._excel_dir) and os.access(self._excel_dir, os.R_OK)
        except Exception as e:
            self.logger.warning(f"Excel health check failed: {e}")
            return False
    
    def __str__(self) -> str:
        """返回数据源的字符串表示"""
        return f"ExcelDataSource({self._excel_dir})"


# 注册Excel数据源到工厂
DataSourceFactory.register(DataSourceType.EXCEL, ExcelDataSource)
