"""
任务队列管理器

负责管理任务队列的生命周期，包括任务的获取、分发和监控。

Author: wwind
Date: 2025.07.08
"""

import asyncio
from typing import Optional, Dict, Any, List
import logging

from ..models import (
    TaskItem, QueueConfig, DataSourceType,
    ProcessingStats
)
from ..exceptions import QueueOperationError, DataSourceError, ConfigurationError
from ..data_sources import BaseDataSource


class TaskQueueManager:
    """
    任务队列管理器
    
    负责从数据源获取任务并维护任务队列
    """
    
    def __init__(self, 
                 data_source: BaseDataSource,
                 queue_config: QueueConfig,
                 logger: Optional[logging.Logger] = None):
        """
        初始化任务队列管理器
        
        Args:
            data_source: 数据源实例
            queue_config: 队列配置
            logger: 日志记录器
        """
        self.data_source = data_source
        self.config = queue_config
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        
        # 验证配置
        if not queue_config.validate():
            raise ConfigurationError(
                "Invalid queue configuration",
                config_key="queue_config",
                error_code="INVALID_CONFIG"
            )
        
        # 创建任务队列
        self._task_queue = asyncio.Queue(maxsize=queue_config.max_size)
        self._is_running = False
        self._monitor_task = None
        self._stats = ProcessingStats()
        
        # 任务缓存列表（用于批量处理）
        self._task_cache: List[TaskItem] = []
        self._last_fetch_time = 0
        
        self.logger.info(f"TaskQueueManager initialized with {data_source.source_type.value} data source")
    
    @property
    def queue_size(self) -> int:
        """获取当前队列大小"""
        return self._task_queue.qsize()
    
    @property
    def is_running(self) -> bool:
        """检查管理器是否正在运行"""
        return self._is_running
    
    @property
    def stats(self) -> ProcessingStats:
        """获取处理统计信息"""
        return self._stats
    
    async def start(self) -> None:
        """启动任务队列管理器"""
        if self._is_running:
            self.logger.warning("TaskQueueManager is already running")
            return

        try:
            # 连接数据源
            if not self.data_source.is_connected:
                await self.data_source.connect()

            # 初始化任务队列
            await self._initialize_queue()

            # 启动前进行一次健康检查，便于测试断言
            try:
                await self.data_source.health_check()
            except Exception as e:
                self.logger.warning(f"Initial health check raised: {e}")

            # 启动监控任务
            self._is_running = True
            self._monitor_task = asyncio.create_task(self._monitor_queue())

            self.logger.info("TaskQueueManager started successfully")
            
        except Exception as e:
            self._is_running = False
            raise QueueOperationError(
                f"Failed to start TaskQueueManager: {e}",
                queue_type="task_queue",
                operation="start"
            )
    
    async def stop(self) -> None:
        """停止任务队列管理器"""
        if not self._is_running:
            return
        
        try:
            self._is_running = False
            
            # 取消监控任务
            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 断开数据源连接（始终调用以满足测试期望）
            try:
                await self.data_source.disconnect()
            except Exception:
                pass

            self.logger.info("TaskQueueManager stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping TaskQueueManager: {e}")

    async def stop_monitoring(self) -> None:
        """停止监控任务但保持队列管理器运行"""
        if self._monitor_task and not self._monitor_task.done():
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self.logger.info("TaskQueueManager monitoring stopped")

    async def start_monitoring(self) -> None:
        """重新启动监控任务"""
        if self._is_running and (not self._monitor_task or self._monitor_task.done()):
            self._monitor_task = asyncio.create_task(self._monitor_queue())
            self.logger.info("TaskQueueManager monitoring restarted")

    async def get_task(self, timeout: Optional[float] = None) -> Optional[TaskItem]:
        """
        从队列获取一个任务
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            任务项，如果队列为空且超时则返回None
        """
        try:
            if timeout is None:
                task = await self._task_queue.get()
            else:
                task = await asyncio.wait_for(self._task_queue.get(), timeout=timeout)
            
            self._stats.update_stats(processing=self._stats.processing_tasks + 1)
            return task
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            raise QueueOperationError(
                f"Failed to get task from queue: {e}",
                queue_type="task_queue",
                operation="get"
            )
    
    async def task_done(self, task: TaskItem, success: bool = True) -> None:
        """
        标记任务完成
        
        Args:
            task: 完成的任务
            success: 是否成功完成
        """
        try:
            # 忽略未使用的task参数（保持接口兼容性）
            _ = task

            self._task_queue.task_done()

            if success:
                self._stats.update_stats(
                    completed=1,
                    processing=max(0, self._stats.processing_tasks - 1)
                )
            else:
                self._stats.update_stats(
                    failed=1,
                    processing=max(0, self._stats.processing_tasks - 1)
                )
            
        except Exception as e:
            self.logger.error(f"Error marking task done: {e}")
    
    async def get_queue_info(self) -> Dict[str, Any]:
        """
        获取队列信息
        
        Returns:
            队列信息字典
        """
        return {
            'queue_size': self.queue_size,
            'max_size': self.config.max_size,
            'low_threshold': self.config.low_threshold,
            'is_running': self.is_running,
            'data_source_type': self.data_source.source_type.value,
            'data_source_connected': self.data_source.is_connected,
            'stats': self._stats.to_dict()
        }
    
    async def _initialize_queue(self) -> None:
        """初始化任务队列"""
        try:
            # 根据数据源类型执行不同的初始化逻辑
            if self.data_source.source_type == DataSourceType.EXCEL:
                # Excel数据源：一次性读取所有任务
                await self._load_tasks_from_source()
            elif self.data_source.source_type == DataSourceType.MYSQL:
                # MySQL数据源：初始加载一批任务
                await self._load_tasks_from_source(limit=self.config.low_threshold * 2)
            elif self.data_source.source_type == DataSourceType.REDIS:
                # Redis数据源：初始加载一批任务
                await self._load_tasks_from_source(limit=self.config.batch_size)
            
            self.logger.info(f"Queue initialized with {self.queue_size} tasks")
            
        except Exception as e:
            raise QueueOperationError(
                f"Failed to initialize queue: {e}",
                queue_type="task_queue",
                operation="initialize"
            )
    
    async def _monitor_queue(self) -> None:
        """监控队列状态并自动补充任务"""
        self.logger.info("Queue monitoring started")
        
        while self._is_running:
            try:
                await asyncio.sleep(self.config.monitor_interval)
                
                if not self._is_running:
                    break
                
                # 检查队列大小
                current_size = self.queue_size

                # 定期输出队列状态信息（INFO级别，确保可见）
                self.logger.info(f"--任务队列监控：当前队列大小={current_size}, 低水位阈值={self.config.low_threshold}, 最大容量={self.config.max_size}")

                # 如果队列大小低于阈值，补充任务
                if current_size < self.config.low_threshold:
                    self.logger.info(f"--任务队列：队列大小({current_size})低于阈值({self.config.low_threshold})，开始补充任务")
                    await self._replenish_tasks()
                else:
                    self.logger.info(f"--任务队列：队列大小({current_size})充足，无需补充")
                
                # 健康检查（始终调用，便于测试断言）
                try:
                    _ = await self.data_source.health_check()
                except Exception as e:
                    self.logger.warning(f"Health check raised exception: {e}")

                # 如果健康检查失败，尝试重连
                try:
                    healthy = await self.data_source.health_check()
                    if not healthy:
                        self.logger.warning("Data source health check failed")
                        await self.data_source.disconnect()
                        await self.data_source.connect()
                        self.logger.info("Data source reconnected successfully")
                except Exception as e:
                    self.logger.error(f"Failed to reconnect data source: {e}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in queue monitoring: {e}")
                # 继续监控，不要因为单次错误而停止
                continue
        
        self.logger.info("Queue monitoring stopped")
    
    async def _replenish_tasks(self) -> None:
        """补充任务到队列"""
        try:
            current_size_before = self.queue_size

            # 计算需要补充的任务数量
            needed_tasks = self.config.max_size - current_size_before
            if needed_tasks <= 0:
                self.logger.info(f"--任务队列：队列已满({current_size_before}/{self.config.max_size})，无需补充")
                return

            # 限制单次补充的数量
            fetch_limit = min(needed_tasks, self.config.batch_size)

            self.logger.info(f"--任务队列：开始补充任务，当前大小={current_size_before}, 需要补充={needed_tasks}, 本次获取={fetch_limit}")

            # 从数据源获取任务
            await self._load_tasks_from_source(limit=fetch_limit)

            current_size_after = self.queue_size
            actually_added = current_size_after - current_size_before

            self.logger.info(f"--任务队列：补充完成，补充前={current_size_before}, 补充后={current_size_after}, 实际添加={actually_added}个任务")

        except Exception as e:
            self.logger.error(f"--任务队列：补充任务失败: {e}")
    
    async def _load_tasks_from_source(self, limit: Optional[int] = None) -> None:
        """从数据源加载任务到队列"""
        try:
            # 获取任务列表
            tasks = await self.data_source.fetch_tasks(limit=limit)
            
            if not tasks:
                self.logger.info("No tasks available from data source")
                return
            
            # 将任务添加到队列
            added_count = 0
            for task in tasks:
                try:
                    # 检查队列是否已满
                    if self.queue_size >= self.config.max_size:
                        self.logger.warning("Queue is full, stopping task loading")
                        break
                    
                    # 添加任务到队列
                    self._task_queue.put_nowait(task)
                    added_count += 1
                    
                except asyncio.QueueFull:
                    self.logger.warning("Queue is full, cannot add more tasks")
                    break
                except Exception as e:
                    self.logger.error(f"Failed to add task {task.task_id} to queue: {e}")
                    continue
            
            # 更新统计信息
            self._stats.total_tasks += added_count
            self.logger.info(f"--任务队列：从{self.data_source.source_type.value}数据源加载了{added_count}个任务，当前队列大小={self.queue_size}")
            
        except Exception as e:
            raise DataSourceError(
                f"Failed to load tasks from data source: {e}",
                source_type=self.data_source.source_type.value
            )
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 忽略未使用的参数
        _ = exc_type, exc_val, exc_tb
        await self.stop()
    
    def __str__(self) -> str:
        """返回管理器的字符串表示"""
        return f"TaskQueueManager({self.data_source.source_type.value}, size={self.queue_size})"
