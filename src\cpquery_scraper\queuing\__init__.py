"""
任务调度模块

重构后的任务调度模块，提供统一的任务队列管理和数据源接口。

主要功能：
1. 从多种数据源（MySQL、Excel、Redis）读取任务
2. 管理任务队列和结果队列
3. 提供统一的异常处理机制
4. 支持任务状态跟踪和统计

Author: wwind
Date: 2025.07.08
Version: 2.0.0
"""

from .exceptions import (
    TaskSchedulingError,
    DataSourceError,
    DatabaseConnectionError,
    FileOperationError,
    QueueOperationError,
    TaskValidationError,
    ConfigurationError,
    ResourceManagementError
)

from .models import (
    DataSourceType,
    QueueType,
    TaskStatus,
    OperationType,
    TaskItem,
    DataSourceConfig,
    QueueConfig,
    ProcessingStats,
    Constants
)

from .data_sources import (
    BaseDataSource,
    DataSourceFactory,
    MySQLDataSource,
    ExcelDataSource,
    RedisDataSource
)

from .queue_managers import (
    TaskQueueManager,
    ResultQueueManager
)

from .processors import (
    TaskProcessor,
    TaskProcessorFactory
)

from .performance import (
    PerformanceMetrics,
    PerformanceMonitor,
    get_performance_monitor,
    record_task_fetch_time,
    record_queue_operation_time,
    record_batch_processing_time
)

from .config_factory import (
    OptimizedConfigFactory,
    PerformanceProfiler
)

# 版本信息
__version__ = "2.0.0"
__author__ = "wwind"
__date__ = "2025.07.08"

# 导出的公共接口
__all__ = [
    # 异常类
    'TaskSchedulingError',
    'DataSourceError',
    'DatabaseConnectionError',
    'FileOperationError',
    'QueueOperationError',
    'TaskValidationError',
    'ConfigurationError',
    'ResourceManagementError',

    # 数据模型和枚举
    'DataSourceType',
    'QueueType',
    'TaskStatus',
    'OperationType',
    'TaskItem',
    'DataSourceConfig',
    'QueueConfig',
    'ProcessingStats',
    'Constants',

    # 数据源
    'BaseDataSource',
    'DataSourceFactory',
    'MySQLDataSource',
    'ExcelDataSource',
    'RedisDataSource',

    # 队列管理器
    'TaskQueueManager',
    'ResultQueueManager',

    # 处理器
    'TaskProcessor',
    'TaskProcessorFactory',

    # 性能监控
    'PerformanceMetrics',
    'PerformanceMonitor',
    'get_performance_monitor',
    'record_task_fetch_time',
    'record_queue_operation_time',
    'record_batch_processing_time',

    # 配置工厂
    'OptimizedConfigFactory',
    'PerformanceProfiler',

    # 版本信息
    '__version__',
    '__author__',
    '__date__'
]

# Legacy compat shims for some tests that patch old paths
# They import corutine_queue_scheduling.*, provide minimal aliases
try:
    import types as _types
    corutine_queue_scheduling = _types.ModuleType('corutine_queue_scheduling')
    # Expose MysqlConnection under the old path for patching
    from src.cpquery_scraper.utils.db import MysqlConnection as _MysqlConn
    ds_mod = _types.ModuleType('corutine_queue_scheduling.data_sources')
    mysql_src_mod = _types.ModuleType('corutine_queue_scheduling.data_sources.mysql_source')
    setattr(mysql_src_mod, 'MysqlConnection', _MysqlConn)
    ds_mod.mysql_source = mysql_src_mod
    corutine_queue_scheduling.data_sources = ds_mod
    import sys as _sys
    _sys.modules['corutine_queue_scheduling'] = corutine_queue_scheduling
    _sys.modules['corutine_queue_scheduling.data_sources'] = ds_mod
    _sys.modules['corutine_queue_scheduling.data_sources.mysql_source'] = mysql_src_mod
except Exception:
    pass

