"""
Top-level compatibility package `click` for legacy spider code.
Re-exports the refactored clicker module API.
Note: This is a project-internal alias and unrelated to the PyPI `click` CLI lib.
"""
try:
    from src.cpquery_scraper.modules.clicker import *  # type: ignore  # noqa: F401,F403
except Exception:  # Fallback to file loading if needed
    import os, importlib.util  # noqa: E401
    here = os.path.dirname(__file__)
    # Look for the module under ../src/cpquery_scraper/modules/clicker/__init__.py
    base = os.path.abspath(os.path.join(here, '..', 'src', 'cpquery_scraper', 'modules', 'clicker', '__init__.py'))
    if not os.path.exists(base):
        # Also try sibling path src/cpquery_scraper/modules/clicker when running from repo root
        base = os.path.abspath(os.path.join(here, 'src', 'cpquery_scraper', 'modules', 'clicker', '__init__.py'))
    spec = importlib.util.spec_from_file_location("_refactored_clicker", base)
    if spec and spec.loader:
        _m = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(_m)  # type: ignore
        for _n in dir(_m):
            if not _n.startswith('_'):
                globals()[_n] = getattr(_m, _n)
    else:
        raise ImportError("Unable to locate refactored clicker module for top-level 'click' alias")

