"""
登录流程管理模块
"""
import asyncio
import json
import time
from typing import Dict, Any, Optional, Tu<PERSON>
from playwright.async_api import Page, expect

from .exceptions import (
    # LoginError, 
    CredentialsError,
    # NetworkError, PageCrashedError,
    # TimeoutError, CaptchaError
)
from .user_provider import UserProvider
from .login_checker import Login<PERSON>hecker
from .captcha_solver import CaptchaSolver
from .utils import extract_username_from_welcome, check_page_exception_msg_is_crashed

class LoginManager:
    """登录流程管理类"""
    
    def __init__(self, config: Any, logger: Any):
        """
        初始化登录管理器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.user_provider = UserProvider(config, logger)
        self.login_checker = LoginChecker(config, logger)
        self.captcha_solver = CaptchaSolver(config, logger)
    
    async def get_page_logined(self, name: str, page: Page, retry_times: int = 0) -> Optional[Page]:
        """
        对传入的页面进行登录状态校验，如果未登录，则用一个新用户进行登录
        
        Args:
            name: 协程名称，用于日志
            page: Playwright页面对象
            retry_times: 重试次数
            
        Returns:
            登录成功后的页面对象，失败时返回None
        """
        if retry_times > 10:
            await asyncio.sleep(60)
        elif retry_times > 20:
            await asyncio.sleep(600)

        try:
            await page.goto(self.config.AUTH_MAINPAGE_URL, wait_until="networkidle")
        except Exception as e:
            if check_page_exception_msg_is_crashed(name, repr(e), 'get_page_logined'):
                self.logger.info(f"--{name}:页面已崩溃，退出")
                return None
            else:
                self.logger.info(f"--{name}:第{retry_times}次，导航到{self.config.AUTH_MAINPAGE_URL}错误（10秒后重试）：{e}")
                await asyncio.sleep(10)
                retry_times += 1
                return await self.get_page_logined(name, page, retry_times)
        else:
            # 判断当前是否已登录状态，如果已登录则点击退出
            login_status = await self.login_checker.check_login_status(name, page)
            if login_status is True:
                self.logger.info(f"--{name}:页面当前为已登录状态，点击功能菜单，退出登录")
                await asyncio.sleep(3)
                await page.get_by_text("您好，").click()
                await page.get_by_text("退出", exact=True).click()
                self.logger.info(f"--{name}:已退出登录")
                await asyncio.sleep(3)  # 不可缺少
                retry_times += 1
                return await self.get_page_logined(name, page, retry_times)
            elif login_status is False:
                # 新用户登录
                self.logger.info(f"--{name}:获取新用户")
                user_info = self.user_provider.get_user()  
                self.logger.info(f"--{name}:获取到的新用户为：{user_info['id']}")
                result = await self.login(name, page, user_info, self.config.CPQUERY_URL)
                if result is None:
                    self.logger.info(f"--{name}:用户登录失败，未能获取登录信息")
                    return None
                page, user_name_logined = result
                self.logger.info(f"--{name}:页面重新登陆成功，当前登录用户：{user_name_logined}")
                return page
            else: 
                self.logger.info(f"--{name}:无法判断登录状态，10秒后重试")
                await asyncio.sleep(10)
                retry_times += 1
                return await self.get_page_logined(name, page, retry_times)
    
    async def login(self, name: str, page: Page, user_info: Dict[str, str], 
                   before_redirect_url: str) -> Optional[Tuple[Page, str]]:
        """
        执行用户登录流程
        
        Args:
            name: 协程名称，用于日志
            page: Playwright页面对象
            user_info: 用户信息字典
            before_redirect_url: 登录成功后重定向的URL
            
        Returns:
            成功时返回(页面对象, 用户名)元组，失败时返回None
        """
        time_start = time.time()

        # 导航到统一身份认证系统首页
        times_retry_goto_auth = 0
        while True:
            if page.url != self.config.AUTH_MAINPAGE_URL or times_retry_goto_auth > 0:
                if times_retry_goto_auth == 0:
                    self.logger.info(f"--{name}:导航到认证系统登录页")
                else:
                    self.logger.info(f"--{name}:导航到认证系统登录页，重试第{times_retry_goto_auth}次")

                # 当当前页面不是空白页时，判断当前页面的登录状态， 如果已登录，则登出当前用户
                if page.url != "about:blank" and await self.login_checker.check_login_status(name, page):
                    self.logger.info(f"--{name}:页面当前为已登录状态，点击功能菜单，退出登录")
                    await asyncio.sleep(3)
                    await page.get_by_text("您好，").click()
                    await page.get_by_text("退出", exact=True).click()
                    self.logger.info(f"--{name}:已退出登录")
                    await asyncio.sleep(3)  # 不可缺少
                
                try:
                    await page.goto(
                        self.config.AUTH_MAINPAGE_URL, timeout=30000
                    )
                    await expect(page.get_by_text('自然人登录')).to_be_visible()
                except Exception as e: 
                    self.logger.error(f"debug:导航到认证系统登录页发生错误：{e}")
                    times_retry_goto_auth += 1

                    if times_retry_goto_auth < 10:
                        interval_retry_goto_auth = 10
                    elif times_retry_goto_auth < 30:
                        interval_retry_goto_auth = 60
                    else:
                        interval_retry_goto_auth = 1000

                    self.logger.warning(
                        f"--{name}:导航到认证系统登录页失败（url = {page.url}），等待{interval_retry_goto_auth}秒重试！"
                    )
                    await asyncio.sleep(interval_retry_goto_auth)
                    continue
                else:
                    if (
                        page.url == self.config.AUTH_MAINPAGE_URL
                        and await page.title() == self.config.AUTH_MAINPAGE_TITLE
                    ):
                        break
                    else:
                        self.logger.warning(f"--{name}:进入认证系统登录页未成功，重试")
                        continue
            else:
                self.logger.info(f"--{name}:已在认证系统登录页")
                break

        assert await page.title() == self.config.AUTH_MAINPAGE_TITLE, f"--{name}:页面标题错误，应为{self.config.AUTH_MAINPAGE_TITLE}，实际为{await page.title()}"

        # 判断登录状态
        if await self.login_checker.check_login_status(name, page):
            self.logger.info(f"--{name}:页面当前为已登录状态，点击功能菜单，退出登录")
            await asyncio.sleep(3)
            await page.get_by_text("您好，").click()
            await page.get_by_text("退出", exact=True).click()
            self.logger.info(f"--{name}: 已退出登录")
            await asyncio.sleep(3)  # 不可缺少

        # 获取用户信息
        user_id = user_info["id"]
        user_pass = user_info["pass"]
        user_type = user_info["type"]

        # 执行登录操作，根据 placeholder 自动选择输入框。
        phone_or_id_locator = page.locator("input[placeholder='手机号/证件号码']")
        credit_code_locator = page.locator("input[placeholder='统一社会信用代码']")
        agency_code_locator = page.locator("input[placeholder='代理机构代码']")
        password_locator = page.locator("input[placeholder='请输入密码']")
        combined_id_locator = phone_or_id_locator.or_(credit_code_locator).or_(agency_code_locator)

        #  确保登录表单可见
        try:
            await expect(combined_id_locator).to_be_visible(timeout=3000)
            await expect(password_locator).to_be_visible(timeout=3000)
        except Exception as e:
            self.logger.error(f"--{name}:登录页面表单获取错误：{e}")
            return await self.login(name, page, user_info, before_redirect_url)
        else:
            self.logger.info(f"--{name}:要登入的ID：{user_id}  用户类型为：{user_type} ")   
            # 填写用户名
            if user_type == "自然人":
                await page.get_by_role("radio", name="自然人登录").click()
                await phone_or_id_locator.fill(user_id)
            elif user_type == "法人":
                await page.get_by_role("radio", name="法人登录").click()
                await credit_code_locator.fill(user_id)
            elif user_type == "代理机构":
                await page.get_by_role("radio", name="代理机构登录").click()
                await agency_code_locator.fill(user_id)
            else:
                raise CredentialsError(
                    f"错误的用户类型：{user_type}",
                    error_code="INVALID_USER_TYPE",
                    user_type=user_type,
                    details={"valid_types": ["自然人", "法人", "代理机构"]}
                )

            # 填写密码
            await password_locator.fill(user_pass)

        # 点击登录按钮登录，获取响应中的验证码图片信息
        try:
            async with page.expect_response("**/am/captcha/get") as captcha_get:
                await page.get_by_role("button", name="登录").click()
            captcha_get = await captcha_get.value
            captcha_get_result = await captcha_get.json()
            captcha_token = captcha_get_result["repData"]["token"]
        except Exception as e:
            self.logger.error(f"--{name}:获取验证码返回信息失败：{e}")
            return await self.login(name, page, user_info, before_redirect_url)
        else:
            # 调用远程接口计算验证码偏移值
            repData = captcha_get_result["repData"]
            offset_position = self.captcha_solver.calculate_offset(repData)

        # 设置响应处理器，监测页面响应，处理用户名、密码错的情形
        user_or_passwd_is_wrong = None

        async def handle_response(response):
            nonlocal user_or_passwd_is_wrong
            if "/am/login" in response.url:
                try:
                    resp = await response.json()
                    if resp['code']==100006 and resp['message']=='密码错误':
                        self.logger.error(f"--密码错误：{resp}")
                        self.logger.error(f"--密码错误的用户信息：{user_info}")
                        user_or_passwd_is_wrong = True
                        return
                    elif resp['code']==100013 and resp['message']=='验证码错误':
                        self.logger.warning(f"--验证码错误：{resp}")
                        user_or_passwd_is_wrong = False
                        return
                    elif resp["code"]==200 or resp['code']=='200':
                        self.logger.info("--登录成功")
                        user_or_passwd_is_wrong = False
                        return
                    elif "异常" in resp["message"]:
                        self.logger.error(f"--异常：{resp}")
                        user_or_passwd_is_wrong = None
                        return
                    elif resp['code']==100003:
                        self.logger.error(f"--{resp['message']}：请检查用户类型是否正确，或者此时登录页面的登录类型是否选择正确")
                        self.logger.error(f"--用户名或密码错误的用户信息：{user_info}")
                        user_or_passwd_is_wrong = True
                        return
                    elif resp['code']==100005:
                        self.logger.warning(f"--{resp['message']}:请检查此时输入栏是否空白")
                        user_or_passwd_is_wrong = None
                        return
                    else:
                        self.logger.error(f"--未定义的接口响应：{resp}")
                        raise ValueError(f"--{name}:未定义的接口响应：{resp}")
                except Exception as e:
                    self.logger.info(f"--{name}:未返回错误，推测用户登录已成功:{e}")
                    user_or_passwd_is_wrong = False
                    return
            else:
                return

        page.on("response", handle_response)

        # 循环操作滑动验证码，直至成功
        i = 0
        while True:
            i += 1
            # 定位滑动条上的移动块
            locator_slider = page.locator(".verify-move-block")
            target = page.locator(".verify-bar-area")
            # 构造发送给验证码服务器的数据
            post_data = {
                "captchaType": "blockPuzzle",
                "pointJson": json.dumps({"x":offset_position,"y":5}).replace(' ', ''),
                "token": captcha_token,
            }

            # 尝试拖动验证码图片中滑动条上的移动块
            try:
                await page.route(
                    "**/am/captcha/check",
                    lambda route: route.continue_(post_data=post_data),
                )  # 不管拖动是否正确，直接替换发送给验证链接的数值

                async with page.expect_response(
                    "**/am/captcha/get", timeout=3000
                ) as captcha_get:
                    async with page.expect_response(
                        "**/am/captcha/check", timeout=3000
                    ) as captcha_check:
                        await locator_slider.drag_to(
                            target,
                            source_position={"x": 0, "y": 5},
                            target_position={"x": offset_position, "y": 5}
                        )
                    captcha_check = await captcha_check.value
                    captcha_check = await captcha_check.json()

                    # 根据验证响应数据，判断是否验证成功，成功则退出验证码循环
                    if captcha_check["repCode"] == "6110":
                        self.logger.info(f"--{name}:验证码{captcha_check['repMsg']}，页面等待0.5秒")
                        await page.wait_for_timeout(500)
                    elif captcha_check["repCode"] == "6111":
                        self.logger.info(f"--{name}:验证码：{captcha_check['repMsg']}")
                        await page.wait_for_timeout(500)
                    elif captcha_check["repCode"] == "0000":
                        await page.wait_for_timeout(500)
                        break
                    elif captcha_check["repCode"] == "0011":
                        self.logger.info(f"--{name}:{captcha_check['repMsg']}，页面等待0.5秒")
                        await page.wait_for_timeout(500)
                    else:
                        self.logger.error(f"！！{name}:未定义的接口响应={captcha_check}！！")

                # 再次获取并计算验证码图片偏移值
                captcha_get = await captcha_get.value
                captcha_get = await captcha_get.json()
                captcha_token = captcha_get["repData"]["token"]

                # 如果偏移计算函数返回固定值，则意味着短时间不可用，不再请求
                if i > 2 and offset_position == self.config.OFFSET_DEFAULT:
                    offset_position = self.config.OFFSET_DEFAULT
                else:
                    offset_position = self.captcha_solver.calculate_offset(captcha_get["repData"])
            except Exception as err:
                if "/am/captcha/get" in repr(err):  # 获取验证码图片超时，重试
                    self.logger.info(f"--{name}:获取验证码图片超时:{err}")  # 验证成功也会超时
                    break  # 成功了要退出循环
                elif "/am/captcha/check" in repr(err):
                    self.logger.info(f"--{name}:验证超时")
                    await page.wait_for_timeout(500)
                    continue  # 验证超时，继续循环
                elif "repCode" in repr(err):
                    self.logger.info(f"--{name}:服务端异常,请核实请求中的数据格式")
                    await page.wait_for_timeout(500)
                    continue
                elif check_page_exception_msg_is_crashed(name, repr(err), 'user_login'):
                    self.logger.info(f"--{name}:当前页面已崩溃，退出")
                    return None
                elif "Timeout" in repr(err):
                    self.logger.info(f"--{name}:验证超时，重试第{i}次")
                    if i > 20:
                        self.logger.error(f"--{name}:验证超时，重试第{i}次，已超过20次，退出")
                        return None
                    else:
                        await asyncio.sleep(5)
                        continue
                else:
                    self.logger.error(f"--{name}:其它未定义错误登录信息：{err}")
                    if i > 20:
                        self.logger.error(f"--{name}:其它未定义错误登录信息：{err}，已超过20次，退出")
                        return None
                    else:
                        await asyncio.sleep(5)
                        continue

        # 去除不再需要的的页面回调
        page.remove_listener("response", handle_response)
        await page.unroute("**/am/captcha/check")

        # 登录后处理：返回登录前网页，获得当前登录的用户名
        await asyncio.sleep(3)
        if not user_or_passwd_is_wrong:
            i = 0
            while True:
                self.logger.info(f"--{name}:登录后处理，导航到目标系统（或指定url）")

                try:
                    await page.goto(before_redirect_url, wait_until="networkidle", timeout=45000)
                    welcome_tip = await page.get_by_text("欢迎您，").inner_text()
                    user_name_logined = extract_username_from_welcome(welcome_tip)
                except Exception as e:
                    self.logger.error(f"--{name}:第{i}次登录后导航错误：{e}")
                    if "tysf.cponline.cnipa.gov.cn" in page.url:
                        self.logger.warning("-！当前页面用户登录已失效，重新登录")
                        result_page = await self.get_page_logined(name, page)
                        if result_page is None:
                            self.logger.error(f"--{name}:重新获取登录页面失败，退出登录流程")
                            return None
                        page = result_page
                        i = 0
                        continue
                    elif check_page_exception_msg_is_crashed(name, repr(e), 'user_login'):
                        self.logger.error("-！当前页面已崩溃，退出")
                        return None
                    else:
                        i += 1
                        await asyncio.sleep(5)
                        continue
                else:
                    self.logger.info(f"--{name}: 登陆成功，当前登录的用户名为: {user_name_logined}")
                    break
        else:
            self.logger.warning(f"--{name}:用户名或密码错误，再次登录")
            return await self.login(name, page, self.user_provider.get_user(), before_redirect_url)

        time_end = time.time()
        self.logger.info(f"--{name}: 本次登录耗时：{time_end-time_start}秒")
        return page, user_name_logined
