import time
import pathlib
from logging import <PERSON>att<PERSON>, <PERSON><PERSON><PERSON><PERSON>, StreamH<PERSON>ler, FileHandler, INFO

# Corrected import path for the config
from src.cpquery_scraper.config import config

__instance = {}

def get_logger(name):
    """
    单角色模式
    """
    if name in __instance:
        return __instance[name]
    formatter_option = '%(asctime)s [%(name)s] %(levelname)s: %(message)s'
    logger_formatter = Formatter(formatter_option)

    if config.LOG_OPTION == "file":
        # 创建文件handler并设置日志级别 文件日志
        time_str = time.strftime("%Y-%m-%d_%H-%M", time.localtime(time.time()))

        # Use the BASE_DIR from config for robust path handling
        log_directory = config.BASE_DIR / "log"
        if not log_directory.exists():
            log_directory.mkdir()

        if config.TEST_MODE:
            log_file_name = log_directory / f"{time_str}_{name}.log"
        else:
            log_file_name = log_directory / f"{time_str}.log"

        handler = FileHandler(log_file_name, mode='a', encoding='utf-8')
    elif config.LOG_OPTION == "console":
        # 流日志，可交由supervisor处理
        handler = StreamHandler()
    else:
        raise ValueError(f"Unknown LOG_OPTION: {config.LOG_OPTION}")

    handler.setLevel(INFO)
    handler.setFormatter(logger_formatter)
    _logger = getLogger(name)
    _logger.addHandler(handler)
    _logger.setLevel(INFO)
    __instance[name] = _logger
    return _logger
