"""
Legacy compatibility shim for corutine_utility.py

This module provides backward compatibility for utility functions.
It re-exports the key utilities that were in the original corutine_utility.py:
- get_logger
- trans_format (core format conversion)
- RedisConnection (with pipeline/batch operations)
- MysqlConnection (with batch upsert capabilities)

The original corutine_utility.py provided:
1. Logging utilities with print replacement
2. Core data format transformation (trans_format)
3. Redis connection management with pipeline support
4. MySQL connection management with batch upsert
5. Database connection pooling and error handling
"""

import json
import time
from typing import Optional, Dict, Any, List

# Re-export core utilities from refactored modules
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.utils.formatters import trans_format
from src.cpquery_scraper.utils.db import RedisConnection, MysqlConnection
from src.cpquery_scraper.config import config

# Create module logger
logger = get_logger(__name__)

# Legacy print replacement for compatibility
def print(*args, **kwargs):
    """Legacy print replacement that uses logger"""
    if args:
        message = ' '.join(str(arg) for arg in args)
        logger.info(message)

# Enhanced RedisConnection with legacy compatibility methods
class LegacyRedisConnection(RedisConnection):
    """
    Enhanced Redis connection with legacy compatibility methods
    
    This class extends the refactored RedisConnection to provide
    backward compatibility with the original interface.
    """
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(self.__class__.__name__)
    
    def connect_redis_with_retry(self, db: int = 0, max_retries: int = 3) -> bool:
        """
        Connect to Redis with retry logic
        
        Args:
            db: Redis database number
            max_retries: Maximum retry attempts
            
        Returns:
            bool: True if connected successfully
        """
        for attempt in range(max_retries):
            try:
                self.connect_redis(db=db)
                self.logger.info(f"Redis connection established (db={db})")
                return True
            except Exception as e:
                self.logger.warning(f"Redis connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    self.logger.error(f"Failed to connect to Redis after {max_retries} attempts")
                    return False
        return False
    
    def batch_hset(self, key_value_pairs: List[tuple]) -> int:
        """
        Batch hash set operations using pipeline
        
        Args:
            key_value_pairs: List of (redis_key, field, value) tuples
            
        Returns:
            int: Number of successful operations
        """
        if not key_value_pairs:
            return 0
        
        try:
            pipe = self.pipeline()
            if pipe is None:
                raise ConnectionError("Failed to get Redis pipeline")
            
            for redis_key, field, value in key_value_pairs:
                pipe.hset(redis_key, field, str(value))
            
            results = pipe.execute()
            return len([r for r in results if r])
        except Exception as e:
            self.logger.error(f"Batch hset operation failed: {e}")
            return 0

# Enhanced MysqlConnection with legacy compatibility methods  
class LegacyMysqlConnection(MysqlConnection):
    """
    Enhanced MySQL connection with legacy compatibility methods
    
    This class extends the refactored MysqlConnection to provide
    backward compatibility with the original interface.
    """
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(self.__class__.__name__)
    
    def connect_mysql_with_retry(self, mysql_param: Dict[str, Any], 
                               max_connections: int = 10, max_retries: int = 3) -> bool:
        """
        Connect to MySQL with retry logic
        
        Args:
            mysql_param: MySQL connection parameters
            max_connections: Maximum connection pool size
            max_retries: Maximum retry attempts
            
        Returns:
            bool: True if connected successfully
        """
        for attempt in range(max_retries):
            try:
                self.connect_mysql(mysql_param=mysql_param, max_connections=max_connections)
                self.logger.info("MySQL connection established")
                return True
            except Exception as e:
                self.logger.warning(f"MySQL connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    self.logger.error(f"Failed to connect to MySQL after {max_retries} attempts")
                    return False
        return False
    
    def batch_upsert_with_rollback(self, sql: str, data_batch: List[tuple], 
                                 batch_size: int = 500) -> int:
        """
        Batch upsert with rollback on failure
        
        Args:
            sql: SQL statement for upsert
            data_batch: List of data tuples
            batch_size: Size of each batch (default 500 as per documentation)
            
        Returns:
            int: Total number of affected rows
        """
        if not data_batch:
            return 0
        
        total_affected = 0
        
        try:
            # Process in batches of specified size
            for i in range(0, len(data_batch), batch_size):
                chunk = data_batch[i:i + batch_size]
                
                try:
                    affected_rows = self.insert_or_update_batch(sql, chunk)
                    total_affected += affected_rows if affected_rows else 0
                    self.logger.debug(f"Batch {i//batch_size + 1}: {affected_rows} rows affected")
                except Exception as e:
                    self.logger.error(f"Batch upsert failed for chunk {i//batch_size + 1}: {e}")
                    # Continue with next batch rather than failing completely
                    continue
            
            self.logger.info(f"Batch upsert completed: {total_affected} total rows affected")
            return total_affected
            
        except Exception as e:
            self.logger.error(f"Fatal error in batch_upsert_with_rollback: {e}")
            return 0

# Legacy format validation function
def validate_trans_format_result(result: Optional[Dict[str, Any]], patent_id: str) -> bool:
    """
    Validate trans_format result according to original business rules
    
    Args:
        result: Result from trans_format function
        patent_id: Patent ID for logging
        
    Returns:
        bool: True if result is valid
    """
    if result is None:
        logger.warning(f"trans_format returned None for {patent_id}")
        return False
    
    # Check for required fields as per original validation
    if not result.get('invention_name') and not result.get('apply_date'):
        if result.get('fyxx_code') != 200:
            logger.warning(f"Invalid result for {patent_id}: missing invention_name and apply_date, fyxx_code != 200")
            return False
    
    return True

# Legacy compatibility - create instances with original names
RedisConnection = LegacyRedisConnection
MysqlConnection = LegacyMysqlConnection

# Legacy compatibility exports
__all__ = [
    'get_logger',
    'trans_format', 
    'RedisConnection',
    'MysqlConnection',
    'print',
    'validate_trans_format_result'
]
