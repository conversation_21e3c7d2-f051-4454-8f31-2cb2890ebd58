#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spider兼容性测试
验证重构后的click模块与spider文件的导入兼容性
"""
import os
import sys
import ast
import re
from typing import List, Set

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)


class SpiderCompatibilityTester:
    """Spider兼容性测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.click_dir = os.path.dirname(os.path.dirname(__file__))
        self.spider_files = []
        self.imported_functions = set()
        
    def find_spider_files(self) -> List[str]:
        """查找项目中的spider文件"""
        spider_files = []
        
        # 查找包含corutine_click导入的文件
        for root, dirs, files in os.walk(self.project_root):
            # 跳过click目录本身
            if 'click' in root:
                continue
                
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if 'corutine_click' in content or 'from click import' in content:
                                spider_files.append(file_path)
                    except Exception:
                        continue
        
        return spider_files
    
    def extract_imported_functions(self, file_path: str) -> Set[str]:
        """提取文件中导入的corutine_click函数"""
        imported_functions = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ImportFrom):
                    if node.module == 'corutine_click':
                        for alias in node.names:
                            imported_functions.add(alias.name)
                    elif node.module == 'click':
                        for alias in node.names:
                            imported_functions.add(alias.name)
                            
        except Exception as e:
            print(f"  [WARNING] 解析文件 {file_path} 失败: {e}")
            
            # 回退到正则表达式解析
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找 from corutine_click import 语句
                pattern = r'from\s+corutine_click\s+import\s+\((.*?)\)'
                matches = re.findall(pattern, content, re.DOTALL)
                for match in matches:
                    functions = re.findall(r'(\w+)', match)
                    imported_functions.update(functions)
                
                # 查找单行导入
                pattern = r'from\s+corutine_click\s+import\s+([^()\n]+)'
                matches = re.findall(pattern, content)
                for match in matches:
                    functions = [f.strip() for f in match.split(',')]
                    imported_functions.update(functions)
                    
            except Exception as e2:
                print(f"  [ERROR] 正则表达式解析也失败: {e2}")
        
        return imported_functions
    
    def check_click_module_exports(self) -> Set[str]:
        """检查click模块的导出函数"""
        init_file = os.path.join(self.click_dir, "__init__.py")
        exported_functions = set()
        
        try:
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取__all__列表
            all_match = re.search(r"__all__ = \[(.*?)\]", content, re.DOTALL)
            if all_match:
                all_content = all_match.group(1)
                exported_items = re.findall(r"'([^']+)'", all_content)
                exported_functions.update(exported_items)
            
            # 检查函数定义
            function_pattern = r'(?:async\s+)?def\s+(\w+)\s*\('
            function_matches = re.findall(function_pattern, content)
            exported_functions.update(function_matches)
            
        except Exception as e:
            print(f"  [ERROR] 检查click模块导出失败: {e}")
        
        return exported_functions
    
    def test_import_compatibility(self) -> bool:
        """测试导入兼容性"""
        print("=== 测试Spider文件导入兼容性 ===")
        
        # 查找spider文件
        self.spider_files = self.find_spider_files()
        print(f"  [INFO] 找到 {len(self.spider_files)} 个相关文件")
        
        # 提取所有导入的函数
        all_imported_functions = set()
        for file_path in self.spider_files:
            rel_path = os.path.relpath(file_path, self.project_root)
            print(f"  [INFO] 检查文件: {rel_path}")
            
            imported = self.extract_imported_functions(file_path)
            if imported:
                print(f"    导入函数: {', '.join(imported)}")
                all_imported_functions.update(imported)
            else:
                print(f"    无corutine_click导入")
        
        # 检查click模块导出
        exported_functions = self.check_click_module_exports()
        print(f"  [INFO] click模块导出 {len(exported_functions)} 个函数")
        
        # 检查兼容性
        missing_functions = all_imported_functions - exported_functions
        if missing_functions:
            print(f"  [FAIL] 以下函数在click模块中缺失: {missing_functions}")
            return False
        else:
            print(f"  [OK] 所有导入的函数都在click模块中存在")
            return True
    
    def test_function_usage_patterns(self) -> bool:
        """测试函数使用模式"""
        print("\n=== 测试函数使用模式 ===")
        
        # 检查关键函数的使用模式
        key_functions = [
            'click_event_button_and_get_data',
            'click_event_button_and_get_data_sync',
            'main_page_query_an',
            'main_page_click_an',
            'get_appl_data'
        ]
        
        usage_patterns = {}
        for file_path in self.spider_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for func_name in key_functions:
                    if func_name in content:
                        # 查找函数调用模式
                        pattern = rf'{func_name}\s*\('
                        matches = re.findall(pattern, content)
                        if matches:
                            if func_name not in usage_patterns:
                                usage_patterns[func_name] = 0
                            usage_patterns[func_name] += len(matches)
                            
            except Exception as e:
                print(f"  [WARNING] 检查使用模式失败: {e}")
        
        print(f"  [INFO] 函数使用统计:")
        for func_name, count in usage_patterns.items():
            print(f"    {func_name}: {count} 次调用")
        
        # 检查是否有关键函数被使用
        if usage_patterns:
            print(f"  [OK] 发现 {len(usage_patterns)} 个函数被使用")
            return True
        else:
            print(f"  [WARNING] 未发现关键函数的使用")
            return False
    
    def test_spider_import_syntax(self) -> bool:
        """测试spider文件的导入语法"""
        print("\n=== 测试导入语法兼容性 ===")
        
        # 模拟spider文件的导入方式
        test_imports = [
            "from click import click_event_button_and_get_data",
            "from click import click_event_button_and_get_data_sync",
            "from click import main_page_query_an, main_page_click_an",
            "import click",
            "import click as corutine_click"
        ]
        
        success_count = 0
        for import_statement in test_imports:
            try:
                # 在当前目录下测试导入
                old_path = sys.path[:]
                sys.path.insert(0, os.path.dirname(self.click_dir))
                
                exec(import_statement)
                print(f"  [OK] {import_statement}")
                success_count += 1
                
                sys.path[:] = old_path
                
            except Exception as e:
                print(f"  [FAIL] {import_statement} - {e}")
                sys.path[:] = old_path
        
        if success_count == len(test_imports):
            print(f"  [OK] 所有导入语法测试通过")
            return True
        else:
            print(f"  [FAIL] {len(test_imports) - success_count} 个导入语法测试失败")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("开始Spider兼容性测试...")
        print("=" * 60)
        
        tests = [
            ("导入兼容性", self.test_import_compatibility),
            ("函数使用模式", self.test_function_usage_patterns),
            ("导入语法", self.test_spider_import_syntax),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"测试 {test_name} 执行失败: {e}")
                results.append((test_name, False))
        
        # 输出测试结果汇总
        print("\n" + "=" * 60)
        print("测试结果汇总:")
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "[PASS] 通过" if result else "[FAIL] 失败"
            print(f"{status} {test_name}")
            if result:
                passed += 1

        print(f"\n总计: {passed}/{total} 测试通过")
        success_rate = (passed / total) * 100
        print(f"成功率: {success_rate:.1f}%")

        if passed == total:
            print("\n[SUCCESS] 所有Spider兼容性测试通过！")
            return True
        else:
            print(f"\n[WARNING] 有 {total-passed} 个测试失败")
            return False


def main():
    """主函数"""
    tester = SpiderCompatibilityTester()
    success = tester.run_all_tests()
    return success


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试执行失败: {e}")
        sys.exit(1)
