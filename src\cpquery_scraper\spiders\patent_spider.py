import asyncio
import random
import time
from playwright.async_api import B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Page, expect

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.modules.clicker import click_event_button_and_get_data_sync
from src.cpquery_scraper.core.asset_processor import StaticProcessor

class PatentSpider:
    """
    负责从给定上下文中抓取专利信息的爬虫。
    """

    def __init__(self, name: str, context: BrowserContext, task_queue: asyncio.Queue, result_queue: asyncio.Queue):
        self.name = name
        self.context = context
        self.task_queue = task_queue
        self.result_queue = result_queue
        self.logger = get_logger(self.name)
        self.static_processor = StaticProcessor()
        self.shutdown_event: asyncio.Event | None = None

    async def _info_to_result_queue(self, an: str, info_tuple: list | tuple) -> None:
        """
        Convert info tuple to result queue format as per original specification

        Result format must be: {an: {'sqxx':..., 'fyxx':..., 'fwxx':..., 'ggxx':..., 'scxx':...}}
        Each info must have {code, data, msg} structure with proper code validation
        """
        record = {an: {}}

        for info in info_tuple:
            if not info or "code" not in info:
                self.logger.warning(f"{an} 的数据项结构异常: {info}")
                continue

            # Check code as per original logic: 200/201 normal, 504 timeout, others server error
            code = info["code"]
            if code in [200, 201]:
                if info.get("data"):
                    # Map data types to result keys as per original logic
                    if "zhuluxmxx" in info["data"]:
                        record[an]["sqxx"] = info
                    elif "yijiaofei" in info["data"]:
                        record[an]["fyxx"] = info
                    elif "tongzhishufw" in info["data"]:
                        record[an]["fwxx"] = info
                    elif "shiwugg" in info["data"]:
                        record[an]["ggxx"] = info
                    elif (
                        isinstance(info["data"], list)
                        and info["data"]
                        and "nodeId" in info["data"][0]
                    ):
                        record[an]["scxx"] = info
            elif code == 504:
                self.logger.warning(f"{an} 数据获取超时: {info.get('msg', 'Unknown timeout')}")
                # Still include timeout results as per original behavior
                msg_str = str(info.get("msg", ""))
                if "zhuluxmxx" in msg_str:
                    record[an]["sqxx"] = info
                elif "费用" in msg_str:
                    record[an]["fyxx"] = info
                elif "发文" in msg_str:
                    record[an]["fwxx"] = info
                elif "公告" in msg_str:
                    record[an]["ggxx"] = info
                elif "审查" in msg_str:
                    record[an]["scxx"] = info
            else:
                self.logger.error(f"{an} 服务器错误 (code={code}): {info.get('msg', 'Unknown error')}")
                # Include server errors as per original behavior - allow continuation

        if record[an]:
            await self.result_queue.put(record)
            self.logger.debug(f"Added {an} to result queue with keys: {list(record[an].keys())}")
        else:
            self.logger.warning(f"未提取到 {an} 的有效数据，不加入结果队列。")

    async def _data_page_init(self, page: Page, an: str, retry_times: int = 0) -> Page | None:
        if retry_times >= config.MAX_DATA_PAGE_INIT_RETRY_TIMES:
            self.logger.error(f"初始化 {an} 的详情页失败，已重试 {retry_times} 次。")
            return None

        try:
            # 确保当前是首页
            await expect(page.get_by_placeholder("例如: 2010101995057")).to_be_visible(timeout=30000)
            await page.get_by_placeholder("例如: 2010101995057").fill(an)
            await page.get_by_role("button", name="查询", exact=True).click()

            # 详情页通常在新页面打开，先尝试捕获弹窗
            detail_page: Page | None = None
            try:
                async with page.expect_popup(timeout=45000) as new_page_info:
                    await page.get_by_text(an).click()
                detail_page = await new_page_info.value
                await detail_page.wait_for_url("**/detail/index?*", timeout=30000)
            except Exception:
                # 若未弹出新页面，则可能在当前页打开
                await page.get_by_text(an).click()
                await page.wait_for_url("**/detail/index?*", timeout=45000)
                detail_page = page

            # 校验确实进入详情页
            if "/detail/index" not in detail_page.url:
                self.logger.warning(f"未能进入详情页，当前URL: {detail_page.url}")
                raise RuntimeError("未能进入详情页")

            self.logger.info(f"{an} 的详情页初始化成功。")
            return detail_page
        except Exception as e:
            self.logger.error(f"初始化 {an} 的详情页失败（第 {retry_times} 次重试）: {e}")
            await asyncio.sleep(3)
            return await self._data_page_init(page, an, retry_times + 1)

    async def _do_query(self, data_page: Page, an: str, url_data_page: str):
        try:
            info_tuple = await click_event_button_and_get_data_sync(self.name, data_page, an, url_data_page)

            # PDF processing logic as per original specification
            # Process PDF URLs in 发文信息 data.tongzhishufw[*].pdfUrl and inject oss_pdfUrl
            if info_tuple:
                for info in info_tuple:
                    if (info and info.get("code") in [200, 201] and
                        info.get("data") and info["data"].get("tongzhishufw")):

                        tongzhishufw_list = info["data"]["tongzhishufw"]
                        if isinstance(tongzhishufw_list, list):
                            for fw in tongzhishufw_list:
                                if isinstance(fw, dict) and fw.get("pdfUrl"):
                                    pdf_url = fw["pdfUrl"]
                                    self.logger.debug(f"Processing PDF URL for {an}: {pdf_url}")

                                    try:
                                        oss_url = await self.static_processor.process_url(pdf_url)
                                        if oss_url:
                                            fw["oss_pdfUrl"] = oss_url
                                            self.logger.info(f"Successfully uploaded PDF for {an}: {pdf_url} → {oss_url}")
                                        else:
                                            self.logger.warning(f"Failed to upload PDF for {an}: {pdf_url}")
                                    except Exception as pdf_error:
                                        self.logger.error(f"Error processing PDF for {an} ({pdf_url}): {pdf_error}")
                                        # Continue processing other PDFs

            if info_tuple:
                await self._info_to_result_queue(an, info_tuple)
            else:
                self.logger.warning(f"No data extracted for {an}")

        except Exception as e:
            self.logger.error(f"执行 {an} 的查询过程中发生错误: {e}")
            raise # 重新引发以由主循环处理

    async def start_scraping(self):
        """单个工作程序上下文的主循环。"""
        self.logger.info("爬虫开始运行...")

        if not self.context.pages:
            self.logger.error("当前上下文没有可用页面，无法开始。")
            return

        home_page = self.context.pages[0]

        # 初始页面设置
        an_init = random.choice(config.AN_FOR_DATA_PAGE_INIT)
        data_page = await self._data_page_init(home_page, an_init)
        if not data_page:
            self.logger.error("初始化数据详情页失败，爬虫即将停止。")
            return

        url_data_page = data_page.url

        # Track account usage time for MAX_TIME_USER_BEEN_USED control
        start_time = time.time()

        while config.FLAG_SPIDER_RUNNING:
            # Check account usage time limit as per original logic
            if time.time() - start_time > config.MAX_TIME_USER_BEEN_USED:
                self.logger.info(f"Account usage time limit reached ({config.MAX_TIME_USER_BEEN_USED}s), stopping task supplementation")
                config.FLAG_SPIDER_RUNNING = False
                # Continue processing remaining tasks in queue but don't fetch new ones

            # 若收到关停信号，优先退出主循环
            if not config.FLAG_SPIDER_RUNNING:
                break
            if isinstance(self.shutdown_event, asyncio.Event) and self.shutdown_event.is_set():
                break

            try:
                an = await asyncio.wait_for(self.task_queue.get(), timeout=5.0)
                # 关停期间不再处理新任务
                if isinstance(self.shutdown_event, asyncio.Event) and self.shutdown_event.is_set():
                    self.task_queue.task_done()
                    break
                self.task_queue.task_done()
                an = an.strip()
                # self.logger.info(f"开始处理任务: {an}")

                ts = time.time()
                await self._do_query(data_page, an, url_data_page)
                self.logger.info(f"任务 {an} 已完成，用时 {time.time() - ts:.2f} 秒")

            except asyncio.TimeoutError:
                if isinstance(self.shutdown_event, asyncio.Event) and self.shutdown_event.is_set():
                    break
                self.logger.info("队列暂无任务，等待中...")
                continue
            except Exception as e:
                # Browser crash detection as per original logic
                error_msg = str(e)
                if ("Target" in error_msg and "closed" in error_msg) or ("501" in error_msg and "退出" in error_msg):
                    self.logger.error(f"Browser crashed or closed: {error_msg}")
                    # Clear task queue as per original behavior
                    while not self.task_queue.empty():
                        try:
                            self.task_queue.get_nowait()
                            self.task_queue.task_done()
                        except asyncio.QueueEmpty:
                            break
                    config.FLAG_SPIDER_RUNNING = False
                    break

                if isinstance(self.shutdown_event, asyncio.Event) and self.shutdown_event.is_set():
                    break
                self.logger.error(f"采集循环中发生未预期的错误: {e}")
                await asyncio.sleep(2)

        self.logger.info("爬虫停止运行（标志关闭或循环退出）。")
        await self.static_processor.close()
