"""
Legacy compatibility shim for corutine_spider.py

This module provides backward compatibility for the core spider business logic.
It re-exports the key functions that were in the original corutine_spider.py:
- context_init
- get_context_be_logined  
- query_patent_info_from_web
- data_page_init
- do_query

The original corutine_spider.py contained the core business logic for:
1. Browser context initialization with stealth and resource blocking
2. Login state management and validation
3. Patent data extraction from detail pages
4. Task queue consumption and result queue production
5. Account usage time control and graceful shutdown
"""

import asyncio
import random
import time
from playwright.async_api import BrowserContext, Page

from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger
from src.cpquery_scraper.core.browser_manager import BrowserManager
from src.cpquery_scraper.spiders.patent_spider import PatentSpider
from src.cpquery_scraper.modules.auth import get_context_be_logined as auth_get_context_be_logined
from src.cpquery_scraper.core.asset_processor import StaticProcessor

logger = get_logger(__name__)

async def context_init(playwright, name: str) -> BrowserContext:
    """
    Initialize browser context with stealth and resource blocking
    
    This function replicates the original context_init behavior:
    - Creates new context with iPad Pro 11 device simulation
    - Injects stealth script
    - Sets up resource blocking for images/media/fonts
    - Configures ignore_https_errors
    
    Args:
        playwright: Playwright instance
        name: Context name for logging
        
    Returns:
        BrowserContext: Initialized context
    """
    browser_manager = BrowserManager(playwright)
    await browser_manager.launch_browser()
    
    device_info = config.IPAD_PRO_11
    context = await browser_manager.get_new_context(device_info, name)
    
    return context

async def get_context_be_logined(context: BrowserContext, name: str) -> BrowserContext:
    """
    Ensure context is logged in to the system
    
    This function handles three page states as per original logic:
    - Already redirected to unified auth: re-login
    - Already on query homepage: validate "欢迎您，" contains current user
    - Other URL: raise ValueError
    
    Args:
        context: Browser context
        name: Context name for logging
        
    Returns:
        BrowserContext: Logged in context
        
    Raises:
        ValueError: If unable to determine page state or login fails
    """
    return await auth_get_context_be_logined(context, name)

async def query_patent_info_from_web(context: BrowserContext, name: str, queue: asyncio.Queue, 
                                   result_queue: asyncio.Queue, static_processor: StaticProcessor):
    """
    Main worker loop for consuming tasks and producing results
    
    This function replicates the original query_patent_info_from_web behavior:
    - Loops consuming task_queue
    - Ensures current page is detail page, otherwise calls data_page_init
    - Calls do_query for data extraction
    - Handles account usage time control (MAX_TIME_USER_BEEN_USED)
    - Detects browser crashes ("Target … closed", "501…退出")
    - Manages graceful shutdown via FLAG_SPIDER_RUNNING
    
    Args:
        context: Browser context
        name: Worker name
        queue: Task queue to consume from
        result_queue: Result queue to produce to
        static_processor: Static asset processor for PDF handling
    """
    spider = PatentSpider(name, context, queue, result_queue)
    spider.static_processor = static_processor
    
    try:
        await spider.start_scraping()
    except Exception as e:
        # Check for browser crash patterns as per original logic
        error_msg = str(e)
        if ("Target" in error_msg and "closed" in error_msg) or ("501" in error_msg and "退出" in error_msg):
            logger.error(f"--{name}: Browser crashed or closed, clearing task queue and exiting")
            # Clear task queue as per original behavior
            while not queue.empty():
                try:
                    queue.get_nowait()
                    queue.task_done()
                except:
                    break
            config.FLAG_SPIDER_RUNNING = False
            raise
        else:
            logger.error(f"--{name}: Unexpected error in query_patent_info_from_web: {e}")
            raise

async def data_page_init(page: Page, an: str, retry_times: int = 0) -> Page:
    """
    Initialize data detail page for given patent number
    
    This function replicates the original data_page_init behavior:
    - Navigate from query page to detail page
    - Input AN → click query → click result to open new page
    - Wait for URL to match /detail/index?zhuanlisqh=...
    - Handle login expiration with get_page_logined retry
    - Retry up to MAX_DATA_PAGE_INIT_RETRY_TIMES
    
    Args:
        page: Query page
        an: Patent application number
        retry_times: Current retry count
        
    Returns:
        Page: Detail page or None if failed
    """
    spider = PatentSpider("data_page_init", page.context, asyncio.Queue(), asyncio.Queue())
    return await spider._data_page_init(page, an, retry_times)

async def do_query(data_page: Page, an: str, url_data_page: str, name: str = "do_query"):
    """
    Execute data extraction from detail page
    
    This function replicates the original do_query behavior:
    - Handle EVENT_CLICK_MODE=sync: sequential event clicking with expect_response
    - Handle EVENT_CLICK_MODE=async: parallel event clicking with create_task
    - Apply "申请信息刷新替代点击" logic for application info
    - Process PDF URLs through StaticProcessor.process_url for OSS upload
    - Return info_tuple with {code, data, msg} for each event
    
    Args:
        data_page: Detail page
        an: Patent application number  
        url_data_page: Detail page URL
        name: Task name for logging
        
    Returns:
        tuple: Event data tuple
    """
    spider = PatentSpider(name, data_page.context, asyncio.Queue(), asyncio.Queue())
    await spider._do_query(data_page, an, url_data_page)

# Self-test functionality for local testing
async def gen_task_queue_for_selftest():
    """Generate test task queue for local testing"""
    test_tasks = ["2017104799411", "202130674002X", "2024114351717"]
    queue = asyncio.Queue()
    for task in test_tasks:
        await queue.put(task)
    return queue

# Legacy compatibility exports
__all__ = [
    'context_init',
    'get_context_be_logined', 
    'query_patent_info_from_web',
    'data_page_init',
    'do_query',
    'gen_task_queue_for_selftest'
]
