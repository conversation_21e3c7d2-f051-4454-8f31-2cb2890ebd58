"""
用户信息提供模块
"""
import json
import random
from typing import Dict, List, Any

from .exceptions import CredentialsError, ConfigurationError

class UserProvider:
    """用户信息提供类"""
    
    def __init__(self, config: Any, logger: Any):
        """
        初始化用户提供者
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self._users = None
    
    def get_user(self) -> Dict[str, str]:
        """
        随机获取一个用户信息
        
        Returns:
            用户信息字典，包含id、pass、type等字段
            
        Raises:
            CredentialsError: 当用户信息获取失败时
        """
        users = self._load_users()
        if not users:
            self.logger.error("！！！无可用用户信息，请检查配置！！！")
            raise CredentialsError(
                "无可用用户信息",
                error_code="NO_USERS",
                details={"source": "file" if self.config.USER_INFO_SOURCE_FROM_FILE else "config"}
            )
            
        user_info = random.choice(users)
        self.logger.info(f"--随机方式获取用户:{user_info['id']}, {user_info['type']}")
        return user_info
    
    def _load_users(self) -> List[Dict[str, str]]:
        """
        加载用户信息
        
        Returns:
            用户信息列表
            
        Raises:
            CredentialsError: 当用户信息加载失败时
        """
        # 如果已加载，直接返回
        if self._users is not None:
            return self._users
            
        # 从文件加载
        if self.config.USER_INFO_SOURCE_FROM_FILE:
            try:
                with open("user_config.json", 'r', encoding='utf-8') as f:
                    users = json.load(f)
            except Exception as e:
                self.logger.error("！！！读取用户信息配置文件出错，请核实用户配置文件（user_config.json）有效后重试！！！")
                raise ConfigurationError(
                    f"读取用户信息配置文件出错:{e}",
                    error_code="FILE_READ_ERROR",
                    config_key="user_config.json",
                    details={"original_error": str(e)}
                )
            else:
                if len(users) < 5:
                    self.logger.error("！！！配置文件中的用户少于5个，程序不能正常运行，请完善配置文件后重试！！！")
                    raise ConfigurationError(
                        "配置文件中的用户至少需要5个",
                        error_code="INSUFFICIENT_USERS",
                        config_key="user_config.json",
                        details={"current_count": len(users), "minimum_required": 5}
                    )
                elif len(users) < 10:
                    self.logger.warning("！！！配置文件中的用户少于10个，程序运行有极大风险，可能导致现有账号因超时使用被封！！！")
                else:
                    self.logger.info(f"--从配置文件读取到用户信息{len(users)}个，将随机使用这些用户执行查询")
        # 从配置加载
        else:
            users = self.config.USER_INFO
            
        self._users = users
        return users