I'll scan the repository to identify entry points, key modules, and how the main business logic flows so I can write an accurate, detailed documentation for refactoring.

I'll quickly list the repository structure to identify all key modules and then open the important files (scheduling, result processing, config, spider) to capture business flows accurately.

I'll search the repo for the auth and click implementations referenced by corutine_spider to accurately document all business steps.

## 文档目的与阅读指引

- 目的：帮助程序员在安全重构当前项目（nuitka_build）时，准确理解“业务逻辑与系统约束”，并在修改结构、划分模块或替换实现细节后，仍与现有行为保持100%一致。
- 范围：本文聚焦 Playwright 爬取 CPQUERY 审查信息系统的业务逻辑、任务队列/结果队列数据流、登录与页面操作契约、结果持久化（Redis/MySQL）、静态资源处理（OSS）等核心流程。包含关键不变量、异常处理约束及验收建议。
- 备注：项目中还包含一个与 Excel 批处理相关的 main.py，不属于本 RPA 业务主线，文末“模块清单”会标注。

---

## 一、业务概要（What & Why）

- 目标系统：国家知识产权局 CPQUERY 审查信息查询系统（cpquery.cponline.cnipa.gov.cn），登录入口为统一身份认证平台（tysf.cponline.cnipa.gov.cn）。
- 业务目的：以 RPA 方式（Playwright 控制浏览器）自动登录、到达详情页、直接拦截/请求后台接口，批量获取专利号的：
  - 申请信息（著录）
  - 费用信息（已缴费/应缴费/退费/滞纳金/收据发文等）
  - 发文信息（通知书发文、证书发文、退信）
  - 公告信息（公布公告/事务公告）
  - 审查信息（下载通知书 PDF、上传到阿里云 OSS，并在结果中回写 OSS URL）
- 业务约束：
  - 登录需要验证码，调用自有验证码识别服务（LOGIN_CAPTCHA_API_URL）。
  - 为降低风控与资源消耗，浏览器 context 添加“stealth”脚本、拦截静态资源等。
  - 任务来自 MySQL/Excel/Redis（配置驱动），优先从 Redis 大规模发号，MySQL 作兜底，Excel 做本地测试。
  - 结果先写 Redis（短期缓存），主节点周期性“批量 upsert”到 MySQL（业务库）。
  - 分布式部署：Role.MAIN（主）与 Role.DISTRIBUTED（从）职责不同（主节点才做 Redis → MySQL 同步）。

---

## 二、系统运行形态与关键配置

- 运行入口
  - 主线：corutine_main.py（异步调度、创建 worker 协程、后台任务）
  - 本地自测：corutine_spider.py 中的 start()/run()（内置自测队列）
- 运行模式
  - RUN_ONCE：运行一次后退出；关闭则循环运行（换账号、延迟再跑）
  - BROWSER_HEADLESS：无头/有头
  - CONSOLE_MODE：日志输出到控制台/文件
  - 角色：config.ROLE = Role.MAIN | Role.DISTRIBUTED（决定是否执行 Redis → MySQL 同步）
- 数据源
  - TASK_SOURCE in {'mysql','excel','redis'}，决定 gen_task_queue 行为
- 并发与资源
  - MAX_TASK_NUMBER：context/worker 数量
  - CONTEXT_TIMEOUT：默认超时
  - START_WAIT_TIME：启动预热时间
  - MAX_TIME_USER_BEEN_USED：单账号最大使用时长（达限停止补充任务并“等队列清空再退出”）

推荐保留的关键参数（均来自 config/config.py）：
- 任务队列与结果队列：
  - TASK_QUEUE_MAX_SIZE、TASK_QUEUE_FROM_REDIS_SIZE、TASK_MONITOR_INTERVAL
  - RESULT_TO_REDIS_INTERVAL、RESULT_TO_MYSQL_INTERVAL
- 页面点击/等待与重试：
  - EVENT_CLICK_MODE（sync/async）、SLEEP_TIME_SEC_ASYNC
  - WAIT_RESPONSE_TIME_MILLISEC_SYNC、WAIT_RESPONSE_TIME_MILLISEC_ASYNC
  - MAX_DATA_PAGE_INIT_RETRY_TIMES、MAX_DATA_PAGE_CLICK_RETRY_TIMES
- 站点与静态资源：
  - CPQUERY_URL、CPQUERY_MAINPAGE_URL、AUTH_MAINPAGE_URL
  - STEALTH_FILE_PATH、BROWSER_PATH
- 外部系统：
  - LOGIN_CAPTCHA_API_URL、OSS_CONFIG、MYSQL_PARAM、REDIS_PARAM
- 运行标志：
  - FLAG_SPIDER_RUNNING（运行期内被用作“是否继续补充任务”的全局门闩）

---

## 三、端到端业务流（Dataflow）

以下描述 corutine_main.py 主流程，确保重构后严格等价：

1) 启动阶段
- 打印启动信息（版本、任务来源、Python/Playwright 版本、浏览器路径、并发数量）
- 初始化 asyncio 队列：
  - task_queue：maxsize = TASK_QUEUE_MAX_SIZE + 额外缓冲（1000）
  - result_queue：固定 5000
- 启动 Playwright；校验 BROWSER_PATH 是否存在；launch chromium（channel=msedge）

2) 工作协程创建
- 根据 MAX_TASK_NUMBER 创建 worker 任务，每个 worker：
  - context_init：new_context（设备模拟：iPad Pro 11），注入 stealth 脚本，拦截图片/媒体/字体等请求，ignore_https_errors
  - get_context_be_logined：若页面跳转至统一认证则登录；如已是查询首页校验“欢迎您，用户名”；异常则重试
  - query_patent_info_from_web：循环消费任务队列
    - 保证当前页为“详情页”（/detail/index?zhuanlisqh=…），否则 data_page_init：
      - 从查询页输入 an → 点击查询 → 点击具体结果打开新页 → 等待 URL 匹配
      - 若登录失效：get_page_logined 重登（递归 + 最大重试次数）
    - do_query：
      - EVENT_CLICK_MODE=sync：按顺序触发事件标签（申请/费用/发文/公告），捕获响应 JSON（expect_response）。审查信息中的 PDF 链接会调用 StaticProcessor.process_url 上传 OSS，成功后在结构中添加 "oss_pdfUrl"
      - EVENT_CLICK_MODE=async：并行创建任务捕获各事件响应
    - info_to_result_queue：将每个事件的 {code, data, msg} 结果，整合为 {an: {'sqxx':..., 'fyxx':..., ...}} 的 record，放入 result_queue（队列满则等待 QUEUE_FULL_WAIT_TIMEOUT）
    - 账号使用时长控制：超过 MAX_TIME_USER_BEEN_USED → FLAG_SPIDER_RUNNING=False，不再补充任务；待队列清空后收尾退出
    - 浏览器崩溃/关闭检测：捕获包含“Target … closed”或“501…退出”的错误 → 清空 task_queue → 关闭 context 并退出
- worker 启动错峰：每个 worker 之间等待 WORKER_START_DELAY 秒（最后一个不等待）

3) 后台协程（与 worker 并行）
- gen_task_queue（queue_scheduling_v2.compatibility）：
  - 兼容旧接口；根据 TASK_SOURCE（mysql/excel/redis）拉取任务
  - 使用 config.FLAG_SPIDER_RUNNING 门闩 + 阈值 TASK_QUEUE_FROM_REDIS_SIZE：不足则补充
  - redis 源场景：直接调用数据源 fetch_tasks(limit=…) 并批量 put 到 task_queue
- result 处理（ResultQueueProcessor.start_scheduled_tasks）：
  - 周期性从 result_queue 读取全部消息，调用 trans_format 转换为业务表结构（见下文契约），写入 Redis（db=2）
  - 主节点（Role.MAIN）周期性将 Redis “未同步”数据批量 upsert 到 MySQL
  - 成功同步后更新 Redis 中该专利记录的 "synced"=1，并删除已同步数据与索引集合项
  - 同步完成后更新任务表 epatent_0.state=1（仅主节点）

4) 收尾
- 等待 task_queue.join → 结果队列 join → 额外等待 FINAL_WAIT_TIME
- 清理：取消未完成任务，关闭浏览器

---

## 四、关键契约与不变量（Refactor 必守）

1) 登录与页面契约
- get_context_be_logined 必须能处理三种页面状态：
  - 已跳转统一认证页：重新登录
  - 已在查询首页：“欢迎您，”包含当前用户，视为登录成功；否则调起登录流程
  - 其他 URL：报错（raise ValueError）
- 登录流程依赖验证码服务 LOGIN_CAPTCHA_API_URL，应支持失败重试逻辑；user_login 返回 (page, user_name) 或 None；None 视为失败需重试
- data_page_init 在详情页 URL 未匹配或弹窗未出现时需执行重试分支；MAX_DATA_PAGE_INIT_RETRY_TIMES 达到即返回 None（交由上层处理）

2) 任务队列补充契约
- gen_task_queue：
  - 仅当队列长度 < TASK_QUEUE_FROM_REDIS_SIZE 且 FLAG_SPIDER_RUNNING=True 才补充
  - Redis 数据源：fetch_tasks(limit=fetch_limit) → 提取 task_id 列表 → put 到 task_queue
  - 监控日志节流：每 TASK_MONITOR_INTERVAL 秒输出一次队列大小

3) 事件点击与数据采集契约
- EVENT_CLICK_MODE=sync：
  - “申请信息”事件使用刷新替代点击（以保证后台接口可靠触发）
  - 通过 expect_response(event_to_request_url(event)) 捕获接口 JSON
  - 对“申请信息”的 result 校验：data.zhuluxmxx.zhuluxmxx.zhuanlisqh 必须等于请求 an；否则抛出 DataExtractionError
- EVENT_CLICK_MODE=async：
  - 申请信息 await，同步等待；其他事件 create_task 并稍作 sleep 分隔（SLEEP_TIME_SEC_ASYNC）
- 审查信息 PDF 处理：
  - 如果 info_tuple 中发文信息 data.tongzhishufw[*].pdfUrl 存在，调用 StaticProcessor.process_url(url) 上传到 OSS；成功时把 "oss_pdfUrl" 写回原结构
- info_to_result_queue：
  - 入队 record 格式为 { an: { 'sqxx'|’fyxx’|’fwxx’|’ggxx’|’scxx’: {code, data, msg}} }，且对每个 info 的 code 进行检查（200/201 为正常，504 为超时，其他为服务器错误）
  - 任何异常结构须打印并允许继续（除结构根本不可解析则 raise）

4) 结果格式转换与持久化契约
- trans_format 输入：上游 record（必须只包含单个 an 键）
- trans_format 输出：MySQL 表字段风格的 dict，含：
  - patent_id, invention_name, apply_date, ady, adm, case_state, ipc 字段、申请人/发明人/代理、优先权、阶段、变更等
  - 费用信息（fee_paid/fee_payable/refund/demurrage/receipt）
  - 发文/证书/退信（dispatch/letter/e_return）
  - 公告（publish/business）
  - "__time"（采集时间）
- JSON 字段入库前转字符串（保持 None → NULL，不转空串）
- 有效性规则：若发明名称+申请日都缺失，且费用 code 非 200，则返回 None（不入队/入库）

5) Redis 与 MySQL 同步契约（主节点）
- Redis：
  - 每条专利数据用 hash 存储，key = "patent_id:{an}"
  - “索引集合” key = "patent_index" 存所有待同步的 an
  - 成功同步后将 hash.synced = "1"，随后删除 hash 与集合元素
- MySQL：
  - 批量 upsert：
    - INSERT ... ON DUPLICATE KEY UPDATE
    - 批次 size=500
  - 同步完成后，更新任务表 epatent_0.state=1（已完成）

6) 运行控制与错误处理
- config.FLAG_SPIDER_RUNNING 为“是否继续补充任务”的全局阀；worker 检测到使用时长达限会置 False
- 浏览器/页面崩溃（“Target … closed”“501…退出”）：
  - worker 清空 task_queue 并关闭 context 退出
- 所有长期循环均带 sleep 与日志，避免过于频繁
- 清理流程必须取消后台任务并关闭浏览器

---

## 五、模块地图与职责

- corutine_main.py（主调度/入口）
  - 启动信息、队列与浏览器初始化
  - 创建 worker 与后台任务（gen_task_queue、ResultQueueProcessor）
  - 等待与清理
  - 示例：
````python path=corutine_main.py mode=EXCERPT
    if __name__ == "__main__":
        if config.RUN_ONCE:
            asyncio.run(main())
        else:
            while True:
                asyncio.run(main())
                print("------------10秒后，换用户登录，再次开启任务------------")
                time.sleep(10)
````

- corutine_spider.py（核心业务）
  - context_init、get_context_be_logined（登录与状态校验）
  - data_page_init、do_query（到达详情页并按事件抓取）
  - query_patent_info_from_web（循环消费任务、控制时长/退出/异常）
  - 示例：
````python path=corutine_spider.py mode=EXCERPT
    async def query_patent_info_from_web(context, name, queue, result_queue, static_processor):
        # 轮训 start(context) 的返回值决定重试、退出
        while True:
            r = await start(context)
            if r == "finished" or "501" in repr(r):
                return
````

- auth 包（登录模块重构，保持兼容）
  - __init__.py 提供 user_login/get_page_logined/get_user 等原接口
  - login_manager/user_provider/login_checker 等“新实现”
  - 约束：行为与 corutine_user_login.py 等价
  - 示例：
````python path=auth/__init__.py mode=EXCERPT
    async def user_login(name, page, user_info, before_redirect_url):
        return await _login_manager.login(name, page, user_info, before_redirect_url)
````

- click 包（事件点击与数据抽取）
  - __init__.py 暴露 click_event_button_and_get_data(_sync)
  - sync_click_manager.py 在 sync 模式下封装 expect_response + 数据校验
  - 约束：与旧的 corutine_click.py 行为一致（含“申请信息刷新替代点击”）

- queue_scheduling_v2.py 与 queue_scheduling/compatibility.py
  - 向后兼容原 corutine_queue_scheduling.py 接口 gen_task_queue、result_queue_save_to_Mysql
  - 新实现采用面向对象的数据源/队列处理器，但对 corutine_main 保持一致 API

- result_processor.py（结果队列 → Redis → MySQL）
  - start_scheduled_tasks 内部周期调用 write_to_redis 和（主节点）sync_to_mysql

- corutine_utility.py（通用工具）
  - get_logger、trans_format（核心格式转换）、RedisConnection/MysqlConnection（带 pipeline/批量 upsert）

- web_asset_to_oss.py（静态资源到 OSS）
  - StaticProcessor.process_url：下载→命名→上传→返回 OSS URL

- reminder.txt（Nuitka 打包命令备忘）
  - 包含 onefile/standalone、include-data-dir、include-package-data 等参数样例

- main.py、processors.py 等（与高校清单/Excel 处理相关）
  - 非本 RPA 主线，请勿与 corutine_main.py 逻辑混淆

---

## 六、外部依赖与环境前置

- Playwright（Python 版，chromium channel=msedge），需与 config.BROWSER_PATH 的可执行程序兼容
- Redis（config.REDIS_PARAM）
- MySQL（config.MYSQL_PARAM）
- 阿里云 OSS（config.OSS_CONFIG）
- 验证码识别服务（config.LOGIN_CAPTCHA_API_URL）
- 静态资源脚本（config.STEALTH_FILE_PATH）

打包（Nuitka）要点（reminder.txt）：
- include-data-dir 指向 browser 与 stealth 目录
- include-package-data=playwright 及第三方依赖的 data files
- onefile/standalone 二选一；Windows 可选 --mingw64、--windows-disable-console
- 注意：打包时需确保 BROWSER_PATH 与 stealth 脚本被正确打入或以相对路径可访问

---

## 七、重构注意事项（风险点与防回归清单）

强制保持不变的行为与接口：
- 导入面：corutine_main.py 仍可从 queue_scheduling_v2 导入 gen_task_queue；从 corutine_spider 导入 context_init / get_context_be_logined / query_patent_info_from_web
- 任务/结果队列契约：task_queue.put 的对象为 an（字符串）；result_queue.put 的对象为 { an: {...} }
- trans_format 入参/出参契约与有效性判断（无效数据返回 None）
- 审查信息 PDF → OSS 的注入字段名：oss_pdfUrl
- FLAG_SPIDER_RUNNING 在“超时达限”与“崩溃退出”的用法
- 错误信息中“Target … closed”“501…退出”的判断逻辑
- result_processor 的 Redis key 命名（"patent_id:{an}"、"patent_index"）与 "synced" 状态字段
- MySQL 批量 upsert + 分批（500）策略，失败回滚与日志

常见坑：
- 修改事件路由地址匹配（event_to_request_url）会导致 expect_response 捉不到响应
- 将 JSON 字段直接写入 MySQL 而未转字符串，或把 None 误写为空串，导致下游对 NULL 语义依赖失效
- 忽略 MAX_TIME_USER_BEEN_USED/START_WAIT_TIME 导致启动/收尾策略变化
- 漏掉 stealth 脚本或资源拦截，触发 WAF 或性能显著下降
- 把队列满（QueueFull）等待逻辑删除，导致丢包
- 将“主/从角色”逻辑简化，导致从节点错误地对 MySQL 进行写入

---

## 八、建议的验证策略（重构验收）

- 单元级（建议最小改动优先）
  - event_to_request_url 对各事件的路由匹配
  - trans_format 针对“完整数据”“部分缺失”“异常 code（201/504/非 200）”的输出
  - StaticProcessor.process_url 对有效/无效 URL 的返回、命名稳定性
  - RedisConnection/MysqlConnection 的 pipeline/executemany 健壮性

- 集成级（本地/测试环境）
  - 使用 corutine_spider.gen_task_queue_for_selftest 的固定 an 列表，跑 end-to-end，验证：
    - 详情页到达、四类信息均可抓取
    - result_queue → Redis → MySQL 的链路打通（主节点）
    - 审查 PDF 的 oss_pdfUrl 回写
  - 模拟验证码服务不可用，观察重试日志与恢复

- 回归清单（必须通过）
  - 相同任务集下，结果集中的每条数据在字段维度与原版一致（可忽略时间戳差异）
  - 队列管理在相同配置下产生近似的补充节奏与队列上限行为
  - 达到 MAX_TIME_USER_BEEN_USED 后，不再补充任务，待清空后退出
  - 浏览器崩溃后，任务队列清空且 worker 退出

---

## 九、给重构者的实现建议

- 保持“兼容适配层”导出旧接口（auth 与 queue_scheduling 已做示范），新实现内部可面向对象
- 把易变部分参数化（全部从 Config 读取），避免硬编码在业务方法中
- 日志替换 print，但保留兼容包装（项目里已用 get_logger 接管 print）
- 渐进重构：先围绕“契约与不变量”补齐测试，再移动代码位置与类职责
- 秘钥/凭据迁移到环境变量或本地安全配置；仓库中出现的敏感值仅作占位

---

## 十、文件与模块速览（仅列核心，与业务主线相关）

- 入口与业务
  - corutine_main.py：主调度/worker/后台任务/收尾
  - corutine_spider.py：context_init、登录、页面到达与事件抓取
- 队列与数据源（兼容层）
  - queue_scheduling_v2.py：兼容导出 gen_task_queue
  - queue_scheduling/compatibility.py：兼容旧接口、内部走新处理器
- 登录
  - auth/__init__.py：兼容旧接口
  - auth/login_manager.py、user_provider.py、login_checker.py、（captcha_solver 等）
- 事件点击
  - click/__init__.py：兼容旧接口
  - click/sync_click_manager.py：sync 模式核心实现
- 结果处理
  - result_processor.py：队列→Redis→MySQL
  - corutine_utility.py：trans_format、Redis/Mysql 封装、日志
- 静态资源
  - web_asset_to_oss.py：PDF 下载+上传 OSS
- 配置
  - config/config.py：全部关键参数集中于此
- 打包
  - reminder.txt：Nuitka 命令备忘



