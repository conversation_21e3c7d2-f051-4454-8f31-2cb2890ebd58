import os
import oss2
import httpx
import asyncio
import hashlib
from urllib.parse import urlparse
from typing import Optional

# Corrected import paths
from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger

class StaticProcessor:
    """处理静态资源的类，下载资源并上传到阿里云 OSS。"""
    
    def __init__(self):
        """初始化 OSS 客户端和 httpx 异步客户端."""
        self.logger = get_logger(__name__)
        self.auth = oss2.Auth(config.OSS_CONFIG['access_key_id'], config.OSS_CONFIG['access_key_secret'])
        self.bucket = oss2.Bucket(self.auth, config.OSS_CONFIG['endpoint'], config.OSS_CONFIG['bucket_name'])
        self.async_client = httpx.AsyncClient()

    async def process_url(self, url: str, file_info: Optional[dict] = None) -> str | None:
        """处理 URL，下载静态资源并上传到 OSS。"""
        try:
            content = await self._download_resource(url)
            if not content:
                return None

            oss_filename = self._generate_oss_filename(url, file_info)
            oss_url = await self._upload_to_oss(content, oss_filename)
            return oss_url
        except httpx.HTTPError as e:
            self.logger.error(f"下载 {url} 失败: {e}")
            return None
        except oss2.exceptions.OssError as e:
            self.logger.error(f"上传 {url} 到 OSS 失败: {e}")
            return None
        except Exception as e:
            self.logger.exception(f"处理 {url} 发生未知错误: {e}")
            return None

    async def _download_resource(self, url: str, timeout: float = 10.0) -> bytes | None:
        """使用 httpx 异步下载资源，添加超时和重试机制."""
        retries = 3  # 重试次数
        for attempt in range(retries):
            try:
                async with self.async_client.stream("GET", url, timeout=timeout) as response:
                    response.raise_for_status()
                    return await response.aread()
            except httpx.ConnectError as e:
                if attempt < retries - 1:
                    self.logger.warning(f"连接 {url} 失败，正在重试 ({attempt + 1}/{retries}): {e}")
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    self.logger.error(f"连接 {url} 失败，达到最大重试次数: {e}")
                    return None
            except httpx.HTTPStatusError as e:
                self.logger.error(f"下载 {url} 失败，状态码: {e.response.status_code}")
                return None
            except httpx.TimeoutException as e:
                if attempt < retries - 1:
                    self.logger.warning(f"下载 {url} 超时，正在重试 ({attempt + 1}/{retries}): {e}")
                    await asyncio.sleep(2 ** attempt)
                else:
                    self.logger.error(f"下载 {url} 超时，达到最大重试次数: {e}")
                    return None
            except Exception as e:
                 self.logger.exception(f"下载 {url} 发生未知错误: {e}")
                 return None
        return None

    def _generate_oss_filename(self, url: str, file_info: Optional[dict]) -> str:
        """根据 URL 和文件信息 生成 OSS 上的文件名，包含原始文件名和后缀."""
        parsed_url = urlparse(url)
        path = parsed_url.path
        filename = os.path.basename(path)
        name, ext = os.path.splitext(filename)
        hashed_name = hashlib.md5(name.encode()).hexdigest()
        return f"{hashed_name}{ext}"

    async def _upload_to_oss(self, content: bytes, filename: str) -> str | None:
        """将内容上传到 OSS。"""
        try:
            if config.TEST_MODE:
                filename = f"test/{filename}"
            else:
                filename = f"{filename}"

            self.bucket.put_object(filename, content)
            return f"https://{config.OSS_CONFIG['bucket_name']}.{config.OSS_CONFIG['endpoint']}/{filename}"
        except oss2.exceptions.OssError as e:
            self.logger.error(f"上传到 OSS 失败: {e}")
            return None

    async def close(self):
        """关闭 httpx 异步客户端。"""
        await self.async_client.aclose()
