"""
重构验证脚本

验证重构后的任务调度模块功能是否正常，包括兼容性测试和新功能测试。

Author: wwind
Date: 2025.07.08
"""

import asyncio
import tempfile
import os
import json
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_compatibility_interface():
    """测试兼容性接口"""
    logger.info("=== 测试兼容性接口 ===")
    
    try:
        # 测试导入兼容性接口
        from ..compatibility import (
            fetch_task_from_Mysql,
            fetch_task_from_excel,
            gen_task_queue,
            result_queue_save_to_Mysql
        )
        logger.info("✓ 兼容性接口导入成功")
        
        # 测试Excel数据源（不需要外部依赖）
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试Excel文件（模拟）
            excel_path = os.path.join(temp_dir, "test.xlsx")
            
            # 由于可能没有openpyxl，我们只测试目录不存在的情况
            result = fetch_task_from_excel("/nonexistent/path")
            if result is None:
                logger.info("✓ Excel数据源错误处理正常")
            else:
                logger.warning("⚠ Excel数据源应该返回None对于不存在的路径")
        
        logger.info("✓ 兼容性接口测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 兼容性接口测试失败: {e}")
        return False


async def test_new_interface():
    """测试新的面向对象接口"""
    logger.info("=== 测试新的面向对象接口 ===")
    
    try:
        # 测试导入新接口
        from .. import (
            DataSourceType, QueueType, TaskStatus,
            DataSourceConfig, QueueConfig, TaskItem,
            DataSourceFactory, TaskProcessor, TaskProcessorFactory
        )
        logger.info("✓ 新接口导入成功")
        
        # 测试数据模型
        task_item = TaskItem(task_id="2023123456789")
        assert task_item.is_valid(), "任务项验证失败"
        logger.info("✓ 数据模型测试通过")
        
        # 测试无效任务项
        invalid_task = TaskItem(task_id="invalid.task")
        assert not invalid_task.is_valid(), "无效任务项应该验证失败"
        logger.info("✓ 任务验证逻辑正常")
        
        # 测试配置
        data_source_config = DataSourceConfig(
            source_type=DataSourceType.MYSQL,
            connection_params={
                'host': 'localhost',
                'user': 'test',
                'password': 'test',
                'database': 'test'
            }
        )
        assert data_source_config.validate(), "数据源配置验证失败"
        logger.info("✓ 配置验证正常")
        
        # 测试队列配置
        queue_config = QueueConfig(
            queue_type=QueueType.TASK_QUEUE,
            max_size=1000,
            monitor_interval=60,
            low_threshold=100
        )
        assert queue_config.validate(), "队列配置验证失败"
        logger.info("✓ 队列配置验证正常")
        
        logger.info("✓ 新接口测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 新接口测试失败: {e}")
        return False


async def test_data_source_factory():
    """测试数据源工厂"""
    logger.info("=== 测试数据源工厂 ===")
    
    try:
        from .. import DataSourceFactory, DataSourceConfig, DataSourceType
        
        # 测试获取支持的类型
        supported_types = DataSourceFactory.get_supported_types()
        expected_types = [DataSourceType.MYSQL, DataSourceType.EXCEL, DataSourceType.REDIS]
        
        for expected_type in expected_types:
            assert expected_type in supported_types, f"缺少支持的数据源类型: {expected_type}"
        
        logger.info(f"✓ 支持的数据源类型: {[t.value for t in supported_types]}")
        
        # 测试创建Excel数据源（最安全的测试）
        with tempfile.TemporaryDirectory() as temp_dir:
            config = DataSourceConfig(
                source_type=DataSourceType.EXCEL,
                connection_params={'excel_dir': temp_dir}
            )
            
            data_source = DataSourceFactory.create(config)
            assert data_source.source_type == DataSourceType.EXCEL
            logger.info("✓ Excel数据源创建成功")
        
        logger.info("✓ 数据源工厂测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据源工厂测试失败: {e}")
        return False


async def test_excel_data_source():
    """测试Excel数据源的完整功能"""
    logger.info("=== 测试Excel数据源功能 ===")
    
    try:
        from .. import DataSourceFactory, DataSourceConfig, DataSourceType
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建配置
            config = DataSourceConfig(
                source_type=DataSourceType.EXCEL,
                connection_params={'excel_dir': temp_dir}
            )
            
            # 创建数据源
            data_source = DataSourceFactory.create(config)
            
            # 测试连接
            await data_source.connect()
            assert data_source.is_connected, "Excel数据源连接失败"
            logger.info("✓ Excel数据源连接成功")
            
            # 测试健康检查
            health = await data_source.health_check()
            assert health, "Excel数据源健康检查失败"
            logger.info("✓ Excel数据源健康检查通过")
            
            # 测试获取任务（空目录）
            tasks = await data_source.fetch_tasks()
            assert isinstance(tasks, list), "获取任务应该返回列表"
            assert len(tasks) == 0, "空目录应该返回空任务列表"
            logger.info("✓ Excel数据源任务获取正常")
            
            # 测试断开连接
            await data_source.disconnect()
            assert not data_source.is_connected, "Excel数据源断开连接失败"
            logger.info("✓ Excel数据源断开连接成功")
        
        logger.info("✓ Excel数据源功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ Excel数据源功能测试失败: {e}")
        return False


async def test_task_processor_factory():
    """测试任务处理器工厂"""
    logger.info("=== 测试任务处理器工厂 ===")
    
    try:
        from .. import TaskProcessorFactory
        
        # 测试创建Excel处理器
        with tempfile.TemporaryDirectory() as temp_dir:
            processor = TaskProcessorFactory.create_excel_processor(
                excel_dir=temp_dir,
                task_queue_max_size=100,
                result_queue_max_size=200
            )
            
            assert processor is not None, "处理器创建失败"
            assert not processor.is_running, "新创建的处理器不应该在运行"
            logger.info("✓ Excel任务处理器创建成功")
            
            # 测试获取状态
            status = await processor.get_status()
            assert 'is_running' in status, "状态信息缺少运行状态"
            assert 'data_source_type' in status, "状态信息缺少数据源类型"
            assert status['data_source_type'] == 'excel', "数据源类型不正确"
            logger.info("✓ 任务处理器状态获取正常")
        
        logger.info("✓ 任务处理器工厂测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 任务处理器工厂测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理"""
    logger.info("=== 测试错误处理 ===")
    
    try:
        from .. import (
            DataSourceFactory, DataSourceConfig, DataSourceType,
            TaskSchedulingError, DataSourceError, ConfigurationError
        )
        
        # 测试无效配置
        try:
            invalid_config = DataSourceConfig(
                source_type=DataSourceType.EXCEL,
                connection_params={'excel_dir': '/nonexistent/path'}
            )
            DataSourceFactory.create(invalid_config)
            logger.error("✗ 应该抛出异常对于不存在的路径")
            return False
        except DataSourceError:
            logger.info("✓ 无效路径正确抛出DataSourceError")
        
        # 测试异常信息
        try:
            raise TaskSchedulingError("测试异常", error_code="TEST_ERROR")
        except TaskSchedulingError as e:
            assert e.error_code == "TEST_ERROR", "错误代码不正确"
            assert "测试异常" in str(e), "错误消息不正确"
            logger.info("✓ 异常信息处理正常")
        
        logger.info("✓ 错误处理测试完成")
        return True
        
    except Exception as e:
        logger.error(f"✗ 错误处理测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    logger.info("开始验证重构后的任务调度模块...")
    
    tests = [
        ("兼容性接口", test_compatibility_interface),
        ("新的面向对象接口", test_new_interface),
        ("数据源工厂", test_data_source_factory),
        ("Excel数据源功能", test_excel_data_source),
        ("任务处理器工厂", test_task_processor_factory),
        ("错误处理", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- 开始测试: {test_name} ---")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 出现未捕获异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    logger.info("\n" + "="*50)
    logger.info("测试结果汇总:")
    logger.info("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    logger.info("-"*50)
    logger.info(f"总计: {len(results)} 个测试")
    logger.info(f"通过: {passed} 个")
    logger.info(f"失败: {failed} 个")
    logger.info(f"成功率: {passed/len(results)*100:.1f}%")
    
    if failed == 0:
        logger.info("\n🎉 所有测试通过！重构成功！")
    else:
        logger.warning(f"\n⚠️  有 {failed} 个测试失败，需要检查相关功能")
    
    return failed == 0


if __name__ == "__main__":
    # 运行验证
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
