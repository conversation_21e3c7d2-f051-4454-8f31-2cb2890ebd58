"""
Click模块测试运行器
运行所有测试并生成报告
"""
import asyncio
import subprocess
import sys
import os
from typing import List, Tuple

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))


class ClickTestRunner:
    """Click模块测试运行器"""
    
    def __init__(self):
        self.test_dir = os.path.dirname(__file__)
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    def run_test_script(self, script_name: str, description: str, timeout: int = 60) -> bool:
        """
        运行测试脚本
        
        Args:
            script_name: 脚本文件名
            description: 测试描述
            timeout: 超时时间（秒）
            
        Returns:
            bool: 测试是否通过
        """
        print(f"\n{'='*60}")
        print(f"运行测试: {description}")
        print(f"脚本: {script_name}")
        print(f"{'='*60}")
        
        script_path = os.path.join(self.test_dir, script_name)
        
        if not os.path.exists(script_path):
            print(f"[FAIL] 测试脚本不存在: {script_path}")
            return False
        
        try:
            # 运行测试脚本
            result = subprocess.run(
                [sys.executable, script_path],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=timeout,
                encoding='utf-8',
                errors='replace'
            )
            
            # 输出测试结果
            if result.stdout:
                print(result.stdout)
            
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            # 检查返回码
            if result.returncode == 0:
                print(f"[PASS] {description} - 测试通过")
                return True
            else:
                print(f"[FAIL] {description} - 测试失败 (返回码: {result.returncode})")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[TIMEOUT] {description} - 测试超时")
            return False
        except Exception as e:
            print(f"[ERROR] {description} - 测试执行异常: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("Click模块测试运行器")
        print("=" * 60)
        
        # 定义测试列表
        tests = [
            ("test_exceptions.py", "异常类功能测试"),
            ("test_click_compatibility.py", "Click模块兼容性测试"),
        ]
        
        results: List[Tuple[str, bool]] = []
        
        # 运行每个测试
        for script_name, description in tests:
            success = self.run_test_script(script_name, description)
            results.append((description, success))
        
        # 输出测试结果汇总
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for description, success in results:
            status = "[PASS]" if success else "[FAIL]"
            print(f"{status} {description}")
            if success:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 测试通过")
        success_rate = (passed / total) * 100 if total > 0 else 0
        print(f"成功率: {success_rate:.1f}%")
        
        if passed == total:
            print("\n[SUCCESS] 所有测试通过！Click模块重构成功！")
            return True
        else:
            print(f"\n[WARNING] 有 {total-passed} 个测试失败，请检查相关问题")
            return False


def main():
    """主函数"""
    runner = ClickTestRunner()
    success = runner.run_all_tests()
    return success


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试运行器执行失败: {e}")
        sys.exit(1)
