"""
验证码处理模块
"""
import requests
# import json
from typing import Dict, Any

# from .exceptions import CaptchaError, NetworkError

class CaptchaSolver:
    """验证码求解类"""
    
    def __init__(self, config: Any, logger: Any):
        """
        初始化验证码求解器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
        self.api_url = config.LOGIN_CAPTCHA_API_URL
        self.retry_times = config.API_RETRY_TIMES
        self.default_offset = config.OFFSET_DEFAULT
    
    def calculate_offset(self, base64_str: Dict[str, str]) -> float:
        """
        计算验证码滑块偏移量
        
        Args:
            base64_str: 包含验证码图片base64编码的字典
            
        Returns:
            计算出的偏移量
            
        Raises:
            CaptchaError: 当验证码处理失败时
        """
        if "originalImageBase64" not in base64_str:
            self.logger.warning(f"不明图片格式:{base64_str}\n返回默认偏移值")
            return self.default_offset
            
        # 构造载荷
        post_data = {
            "action": "checkNewCode",
            "image": "data:image/png;base64," + base64_str["originalImageBase64"],
            "project_name": "sell_slider_230210",
        }
        
        # 发送请求
        for i in range(self.retry_times + 1):
            try:
                r = requests.post(self.api_url, data=post_data, timeout=10)
            except Exception as err:
                self.logger.error(f"连接验证码计算服务器出现错误：{err}")
                if i < self.retry_times - 1:
                    self.logger.info(f"第{i+1}次连接远程接口{self.api_url}失败，重试")
                    continue
                else:
                    self.logger.warning(f"第{i+1}次连接远程接口{self.api_url}失败，直接返回默认偏移值")
                    return self.default_offset
            
            # 处理响应
            if r.status_code == 200:
                if len(r.content) == 0:
                    self.logger.warning(f"第{i+1}次连接远程接口{self.api_url}，接口返回内容为空，重试")
                    continue
                
                result = r.json()
                if result["code"] == 0 and result["success"]:
                    offset = result["data"]
                    return float(offset)
                else:
                    if i == self.retry_times - 1:
                        self.logger.warning(f"第{i+1}次失败，不再尝试连接该接口，直接返回默认偏移值")
                        return self.default_offset
                    else:
                        self.logger.warning(f"--验证码识别接口返回错误信息={result['msg']}，再次尝试")
                        continue
            else:
                self.logger.warning(f"第{i+1}次连接验证码识别接口{self.api_url},接口响应异常:{r.status_code},重试")
                if i >= self.retry_times:
                    self.logger.warning(f"第{i+1}次失败，不再尝试连接该接口，直接返回默认偏移值")
                    return self.default_offset
                else:
                    continue
                    
        # 如果所有尝试都失败，返回默认值
        return self.default_offset