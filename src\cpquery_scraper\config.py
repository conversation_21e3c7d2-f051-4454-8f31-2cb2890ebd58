'''
集中存放所有模块需要的常量
2023.04.21
'''
# -*- coding: utf-8 -*-

from enum import Enum
from pathlib import Path

# [新增] 项目根目录，用于构建绝对路径
# BASE_DIR 应该是根目录下的 'cpquery_scraper' 目录
# 正确确定项目根目录。假设脚本从项目根目录运行。
# 现在的结构是 /main.py, /src/cpquery_scraper/config.py
# 所以我们需要从这个文件的目录向上两级。
BASE_DIR = Path(__file__).resolve().parent.parent.parent

class Role(Enum):
    MAIN = 'main'        # 主节点
    DISTRIBUTED = 'distributed'      # 分布式节点


class Config:
    """
    配置类，用于存放系统的配置信息
    """
    Role = Role         # 使 config.Role 可用

    def __init__(self):
        self.VERSION = 1.672                     # 版本号

        # 一、程序关键参数设置：
        # 1.测试模式开关
        self.TEST_MODE = False                  # 生产模式（结果写入，爬取状态更新）
        # self.TEST_MODE = True                 # 测试模式（结果不写入，爬取状态不更新）
        # 2.浏览器运行模式开关
        self.BROWSER_HEADLESS = True            # True  无头模式 
        self.BROWSER_HEADLESS = False         # False 有头模式
        # 3.程序运行主逻辑，是运行一次结束，还是持续运行多次
        self.RUN_ONCE = True
        # self.RUN_ONCE = False
        # 4.程序运行时的角色，是主节点还是分布式节点（使用枚举类型实现）
        self.ROLE = Role.MAIN
        # self.ROLE = Role.DISTRIBUTED 
        # 5.系统协程并发数、上下文等待时长、单个用户最大使用时长
        self.MAX_TASK_NUMBER = 1                # 并发任务数量,默认1
        self.CONTEXT_TIMEOUT = 30000            # context 超时等待周期
        self.MAX_TIME_USER_BEEN_USED = 3000     # 设定单个用户允许被使用的最长时间（秒）,默认3000秒（50分钟）

        # 二、程序行为定制参数
        # 1.在数据详情页，程序（模拟人工）点击各个数据项（链接）的动作模式
        self.EVENT_CLICK_MODE = "sync"                  # "sync"：串行点击，逐个点击；"async"：并行点击，几个按钮一起点
        # self.EVENT_CLICK_MODE = "async"
        # 2.指定上下文（context）登录状态暂存文件名称（仅用于测试时使用，用于保持登陆状态，避免频繁登录）
        self.AUTHENTICATION_FILE_NAME = "authentication.json"
        # 3.浏览器上下文（context）初始化参数（模拟一个设备的浏览器参数，避免被WAF识别）
        self.IPAD_PRO_11 = {
            'user_agent': 'Mozilla/5.0 (iPad; CPU OS 12_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1',
            'viewport': {'width': 1194, 'height': 834}, 
            'device_scale_factor': 2, 
            'is_mobile': True, 
            'has_touch': True,
            'default_browser_type': 'webkit'
                        }
        # 4.获取数据范围、类别参数：获取案卷数据的遮罩，True表示要获取， False表示不获取
        self.DATA_SCOPE_MASK = {
                    '申请信息' : True, 
                    '费用信息' : True,
                    '发文信息' : True,
                    '公告信息' : True,
                    # '审查信息' : True,
                    # '专利权质押' : False,
                    # '实施许可备案' : False,
                    # '开放许可声明' : False,
                    # '同族案件信息' : False,
                    }
        # 5.任务队列和结果队列相关参数（用于从外部获取申请号、保存爬取结果到数据库）
        self.TASK_QUEUE_MAX_SIZE = 1000         # 任务队列最大长度，同时也是从mysql服务器单次取回任务的数量（默认5000）,测试用500
        self.TASK_MONITOR_INTERVAL = 100        # 任务监控间隔时间：秒数 默认600
        self.QUEUE_FULL_WAIT_TIMEOUT = 600      # （任务）队列满时的等待时间：秒数
        self.RESULT_TO_MYSQL_INTERVAL = 600      # 结果队列写入Mysql服务器的间隔周期：秒数(默认3600)
        self.RESULT_TO_REDIS_INTERVAL = 60      # 结果队列写入Redis服务器的间隔周期：秒数
        self.START_WAIT_TIME = 60               # 爬虫启动等待期秒数（超过等待期如果获取不到任务或结果，爬虫退出）,默认300
        self.TASK_QUEUE_FROM_REDIS_SIZE = 100   # 任务队列由Redis生成时的队列长度，100
        self.FLAG_SPIDER_RUNNING: bool = False            # 系统爬取任务运行状态，默认值取False
        # 6.爬取任务来源指定
        # self.TASK_SOURCE = 'mysql'       # 读取任务的来源 mysql为从数据库读取
        # self.TASK_SOURCE = 'excel'       # 读取任务的来源 excel为从本地文件读取
        self.TASK_SOURCE = 'redis'       # 读取任务的来源 redis为从远程Redis服务中读取
        # 7.程序爬取数据的动作行为参数（此时浏览器当前页面为数据详情页）
        self.WAIT_RESPONSE_TIME_MILLISEC_SYNC = int(30000)   # 数据页面超时：毫秒 最佳5000，忙时：10000
        self.WAIT_RESPONSE_TIME_MILLISEC_ASYNC = int(15000)   # 数据页面超时：毫秒 最佳5000，忙时：10000
        self.SLEEP_TIME_SEC_ASYNC = float(0.2)                # 数据页面不同请求的间隔时间 0.2秒
        self.MAX_DATA_PAGE_REFRESH_RETRY_TIMES = 3                 # 数据页面重试刷新的最大次数
        self.WAIT_RESPONSE_TIME_MILLISEC_REFRESH = int(40000)      # 页面刷新等待的最长时间
        # AN_FOR_DATA_PAGE_INIT = '2017104799411'    # 202130674002X ，'2017104799411'
        self.AN_FOR_DATA_PAGE_INIT = ('202130674002X', '2024114351717', '2017104799411')
        # CLICK_EVENT_TUPLE = ("申请信息", "费用信息", "发文信息", "公告信息", "审查信息")
        self.MAX_DATA_PAGE_INIT_RETRY_TIMES = 5         # 数据初始化页面最大尝试次数
        self.MAX_DATA_PAGE_CLICK_RETRY_TIMES = 3        # 数据点击页面最大尝试次数

        # 三、静态文件位置
        # [修改] 使用BASE_DIR构建绝对路径，避免相对路径问题
        self.STEALTH_FILE_PATH = BASE_DIR / "static_assets/browser/Chrome-bin/user_data/stealth/stealth.min_2.72.js"
        self.BROWSER_PATH = BASE_DIR / "static_assets/browser/Chrome-bin/109.0.1518.78/msedge.exe"
        self.TASK_EXCEL_DIR = BASE_DIR / "task_excel/"

        # 四、系统日志选项开关
        self.LOG_OPTION = "console" if self.TEST_MODE else "file"       # 生产模式（结果写入，爬取状态更新）
        # 2025.4.19:为了测试1.62版本稳定性，临时观测，日志输出到控制台
        self.LOG_OPTION = "console"

        # 五、源网站用户登录相关参数
        # 1.登录用户信息保存方式
        # self.USER_INFO_SOURCE_FROM_FILE = True  # True为从配置文件（user_config.json）获取，False为从代码（corutine_config.py）获取
        self.USER_INFO_SOURCE_FROM_FILE = False
        # 2.登录源网站所用的用户信息
        self.USER_INFO = [
                        {'id': '91130302MABLN4P523', 'pass': 'Qckjbln4p523$', 'user_name': '', 'type': '法人'},
                        {'id': '420684199009093522', 'pass': 'Slm09093522$', 'user_name': '', 'type': '自然人'},
                        {'id': '130303198706202626', 'pass': 'Wll06202626$', 'user_name': '', 'type': '自然人'},
                        {'id': '91130302MA7NE6KL2Q', 'pass': 'Blkj7ne6kl2q$', 'user_name': '', 'type': '法人'},  
                        {'id': '91130302MABMACJ56J', 'pass': 'Bcgybmacj56j$', 'user_name': '', 'type': '法人'},
                        {'id': '18291925400', 'pass': 'H@nliang032', 'user_name': '', 'type': '自然人'},
                        {'id': '18710688750', 'pass': 'Zk921228789@', 'user_name': '', 'type': '自然人'},
                        {'id': '15811490326', 'pass': 'Redalert@00', 'user_name': '', 'type': '自然人'},   # 20231028增加
                        {'id': '610326199207231426', 'pass': 'Zhibao199265ping%', 'user_name': '', 'type': '自然人'},
                        {'id': '420983197902281340', 'pass': 'Zxe02281340$', 'user_name': '', 'type': '自然人'},
                        {'id': '130924199501183520', 'pass': 'Mage123456.', 'user_name': '', 'type': '自然人'}, 

                        # 密码错误
                        # {'id': '91610131MA710N9A2M', 'pass': 'Zk921228789@', 'user_name': '', 'type': '法人'},   # 2024.12.24:密码错误
                        # {'id': '18392048661', 'pass': 'Zjf@321369', 'user_name': '', 'type': '自然人'},  # 密码错误

                        # 开发测试用
                        # {'id': '13811654760', 'pass': 'Redalert@00', 'user_name': '董庆伟', 'type': '自然人'},  
                        
                        # # 重庆理工账号
                        # {'id': '12500000450381787L', 'pass': 'Cqut-62560451', 'user_name': '', 'type': '法人'}   # 20241004增加
                        ]
        # 3.登录验证码识别服务API地址
        self.LOGIN_CAPTCHA_API_URL = "http://47.92.52.61/"    # 外网：年管家验证码识别服务地址
        self.LOGIN_CAPTCHA_API_URL = "http://10.2.2.29/"           # zerotier内网：年管家验证码识别服务地址
        # self.LOGIN_CAPTCHA_API_URL = "http://*********/"     # 模拟验证码服务不可用的地址（虚假地址，用于测试）
        self.API_RETRY_TIMES = 3
        self.OFFSET_DEFAULT: float = 189.65        # 189.65，232.5, 164.3，107.725, 159.65, 130.9

        # 六、源网站相关参数
        self.AUTH_MAINPAGE_TITLE = "专利和集成电路布图设计业务办理统一身份认证平台"
        self.AUTH_MAINPAGE_URL = "https://tysf.cponline.cnipa.gov.cn/am/#/user/login"
        self.CPQUERY_MAINPAGE_TITLE = "中国及多国专利审查信息查询"
        self.CPQUERY_URL = "https://cpquery.cponline.cnipa.gov.cn/"
        self.CPQUERY_MAINPAGE_URL = "https://cpquery.cponline.cnipa.gov.cn/chinesepatent/index"
        self.URL_DATA_PAGE_INIT = "https://cpquery.cponline.cnipa.gov.cn/detail/index?zhuanlisqh=8WgB%252BZ6W%252Bg3406nKnbBxgA%253D%253D&anjianbh"

        # 七、采集得到的数据保存参数
        # 1.mysql服务器连接参数
        self.MYSQL_PARAM = {
            "host": "cnpatent.mysql.rds.aliyuncs.com",
            "port": 3306,
            "db": "cpquery",
            "user": "spider",
            "password": "shenjian18A"
        }
        # 2.redis服务连接参数
        self.REDIS_PARAM = {
            # 'host': 'r-2zeb5d92596ccd24.redis.rds.aliyuncs.com',
            'host': 'cpquery20.redis.rds.aliyuncs.com',
            'port': 6379,
            'password': 'ngj2020L',
            'decode_responses': True,
        }
        # 3.保存数据到mysql数据库中的数据表名字
        self.DB_RESULT_TABLE_NAME = 'patent_process_data_temp' if self.TEST_MODE else 'patent_process_data'
        # 4.保存数据到Redis中的数据表名字
        self.REDIS_RESULT_TABLE_NAME = 'patent_process_data_temp' if self.TEST_MODE else 'patent_process_data'
        # 5.阿里云OSS认证信息（获取的数据中如果包含图片、pdf等静态文件，则需要上传到阿里云OSS）
        self.OSS_CONFIG = {
            "access_key_id": "LTAI5tGLh8iYAQY83kXWQcyr",  # 阿里云账号的 AccessKey ID
            "access_key_secret": "******************************",  # 阿里云账号的 AccessKey Secret
            "endpoint": "oss-cn-beijing.aliyuncs.com",  # OSS Endpoint，例如：oss-cn-hangzhou.aliyuncs.com
            "bucket_name": "cnpatent-examinfo-files",  # OSS Bucket 名称
            # 以下是可选配置，根据需要添加
            # "sts_token": "YOUR_STS_TOKEN", # 使用STS授权时需要
            # "security_token": "YOUR_SECURITY_TOKEN", # 使用STS授权时需要，与sts_token作用相同，二选一即可
            "is_cname": False, # 是否使用自定义域名，默认为False，如果使用自定义域名，则endpoint需要设置为自定义域名
            "connect_timeout": 60, # 连接超时时间，单位秒
            "socket_timeout": 60, # socket超时时间，单位秒
        }


config = Config()
# 让 config 成为全局变量，方便其他模块访问
# 例如：from src.cpquery_scraper.config import config
