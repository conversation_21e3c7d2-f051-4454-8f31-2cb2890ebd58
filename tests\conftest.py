# Global test configuration and skips
import os
import pytest

# Skip Playwright-dependent auth tests if browsers are not installed
PLAYWRIGHT_BROWSERS_INSTALLED = os.environ.get("PLAYWRIGHT_BROWSERS_INSTALLED", "0") == "1"

AUTH_TEST_MARK = pytest.mark.skipif(
    not PLAYWRIGHT_BROWSERS_INSTALLED,
    reason="Playwright browsers not installed; set PLAYWRIGHT_BROWSERS_INSTALLED=1 to enable auth tests",
)

def pytest_collection_modifyitems(config, items):
    if PLAYWRIGHT_BROWSERS_INSTALLED:
        return
    for item in items:
        # Skip tests in auth module that require launching browsers
        try:
            path = str(item.fspath)
        except Exception:
            path = item.nodeid
        norm = path.replace('\\', '/').lower()
        if norm.endswith('src/cpquery_scraper/modules/auth/tests/test_auth_module.py'):
            item.add_marker(AUTH_TEST_MARK)
