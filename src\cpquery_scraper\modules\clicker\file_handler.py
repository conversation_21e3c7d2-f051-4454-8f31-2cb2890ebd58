"""
文件处理管理器
负责处理各种文件类型的点击和数据获取
"""
# import asyncio
import json
# import time
from typing import Optional, Set
from playwright.async_api import Page, Response

from .exceptions import (
    # FileAccessError, ResponseTimeoutError, 
    PageCrashedError,
    # DataExtractionError, RouteInterceptError
)
from src.cpquery_scraper.utils.helpers import check_page_exception_msg_is_crashed


class FileHandler:
    """文件处理管理器"""
    
    def __init__(self, config, logger):
        """
        初始化文件处理管理器
        
        Args:
            config: 配置对象
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger
    
    def check_file_permission(self, file_info: dict) -> bool:
        """
        检查文件权限
        
        根据文件信息中的additionalData字段，判断当前用户是否有权限查看该文件
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            bool: 是否有权限访问文件
        """
        # 如果是附件类型，直接返回True
        if 'fujianmc' in file_info:
            return True
        
        # 检查权限字段
        if 'additionalData' not in file_info:
            return False
            
        permission_word = file_info['additionalData'].get("xsnr", "")
        if permission_word == "BTHST":
            return True
        elif permission_word == "JXSBT":
            return False
        else:
            self.logger.warning(f"文件信息格式错误，xsnr字段值未定义：{permission_word}")
            return False
    
    async def click_file_name(self, name: str, data_page: Page, file_info: dict, an: str) -> dict:
        """
        获取文件信息
        
        通过点击文件名链接，获取文件信息
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            file_info: 文件信息字典
            an: 申请号
            
        Returns:
            dict: 文件信息结果
            
        Raises:
            FileAccessError: 文件访问失败时抛出
            ResponseTimeoutError: 响应超时时抛出
            PageCrashedError: 页面崩溃时抛出
        """
        # 检查文件权限
        if not self.check_file_permission(file_info):
            return {
                "code": 503,
                "msg": "当前用户没有权限查看该实体文件！",
            }
        
        try:
            # 根据文件类型选择处理方式
            if 'additionalData' in file_info and file_info['additionalData']['ds'] == "TZS":
                # 通知书类型
                result = await self._handle_tzs_file(name, data_page, file_info, an)
                
                # 检查是否有检索报告
                file_meta_data = file_info['additionalData']
                if file_meta_data.get("shoucijsbg") or file_meta_data.get("buchongjsbg"):
                    self.logger.info(f"--{name}: {file_info['name']} 存在检索报告标记，需要获取检索报告数据")
                    result["jsbg_data"] = await self._handle_jsbg_file(name, data_page, file_meta_data, an)
                
            elif 'fujianmc' in file_info and file_info['ds'] == "TZS_FJ":
                # 附件类型
                result = await self._handle_attachment_file(name, data_page, file_info, an)
                
            else:
                # 其他正常文件类型
                result = await self._handle_normal_file(name, data_page, file_info, an)
                
        except Exception as err:
            if check_page_exception_msg_is_crashed(name, repr(err), "click_file_name"):
                self.logger.error(f"--{name}: 浏览器或页面已关闭，退出click_file_name函数")
                raise PageCrashedError(
                    "浏览器或页面已关闭",
                    error_code="PAGE_CRASHED",
                    crash_reason=str(err)
                )
            elif "Timeout" in repr(err):
                self.logger.warning(f"--{name}: 超时原因未获取到数据，放弃获取文件:{file_info['name']}")
                return {
                    "code": 504,
                    "data": None,
                    "msg": f"获取文件:{file_info['name']}信息超时，放弃获取文件信息",
                }
            else:
                self.logger.error(f"--{name}: 其它原因未获取到数据，放弃获取文件:{file_info['name']}：{err}")
                return {
                    "code": 503,
                    "data": None,
                    "msg": f"获取文件:{file_info['name']}信息失败报错:{err}",
                }
        
        return result
    
    async def _handle_normal_file(self, name: str, data_page: Page, file_info: dict, an: str) -> dict:
        """
        处理普通文件
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            file_info: 文件信息字典
            an: 申请号
            
        Returns:
            dict: 文件信息结果
        """
        file_info_url = "**/api/view/gn/fetch-file-infos?hHp4Kgam=**"
        
        # 设置路由拦截器
        async def handle_route(route):
            mock_post_data = json.loads(route.request.post_data)
            if "zhuanlisqh" in route.request.post_data:
                mock_post_data['zhuanlisqh'] = an
            await route.continue_(post_data=mock_post_data)
        
        await data_page.route(file_info_url, handle_route)
        
        try:
            # 点击文件名，获取文件信息
            async with data_page.expect_response(
                file_info_url, 
                timeout=self.config.WAIT_RESPONSE_TIME_MILLISEC_SYNC
            ) as info:
                await data_page.get_by_role("treeitem").get_by_text(file_info['name'], exact=True).click()
            
            info_response = await info.value
            result = await info_response.json()
            
        finally:
            await data_page.unroute(file_info_url)
        
        return result
    
    async def _handle_tzs_file(self, name: str, data_page: Page, file_info: dict, an: str) -> dict:
        """
        处理通知书文件
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            file_info: 文件信息字典
            an: 申请号
            
        Returns:
            dict: 通知书文件信息结果
        """
        file_info_url = "**/api/view/gn/fetch-file-infos?hHp4Kgam=**"
        
        # 设置路由拦截器
        async def handle_route(route):
            mock_post_data = json.loads(route.request.post_data)
            if "zhuanlisqh" in route.request.post_data:
                mock_post_data['zhuanlisqh'] = an
            await route.continue_(post_data=mock_post_data)
        
        await data_page.route(file_info_url, handle_route)
        
        try:
            result = await self._fetch_tzs_data(data_page, file_info, an)
        finally:
            await data_page.unroute(file_info_url)
        
        return result
    
    async def _fetch_tzs_data(self, data_page: Page, file_info: dict, an: str) -> dict:
        """
        获取通知书数据
        
        Args:
            data_page: 数据页面对象
            file_info: 文件信息字典
            an: 申请号
            
        Returns:
            dict: 通知书数据
        """
        data_dict = {}
        captured_urls: Set[str] = set()
        
        # 定义响应监听函数
        async def handle_response(response: Response):
            if response.request.resource_type == 'xhr':
                if response.url not in captured_urls and "/api/view/gn/" in response.url:
                    try:
                        json_data = await response.json()
                        data_dict[response.url] = json_data
                        captured_urls.add(response.url)
                    except Exception as e:
                        self.logger.error(f"{response.url}返回的数据格式错误: {e}")
        
        # 监听response事件
        data_page.on("response", handle_response)
        
        try:
            # 点击通知书链接
            await data_page.get_by_role("treeitem").get_by_text(file_info['name']).click()
            
            # 等待响应数据
            max_wait_time = self.config.WAIT_RESPONSE_TIME_MILLISEC_SYNC
            time_elapsed = 0
            check_interval = 100
            
            while len(captured_urls) < 2 and time_elapsed < max_wait_time:
                await data_page.wait_for_timeout(check_interval)
                time_elapsed += check_interval
            
            if len(captured_urls) < 2:
                self.logger.warning(f"未捕获到全部通知书数据，只捕获到{len(captured_urls)}个响应")
            
        finally:
            # 移除监听器
            data_page.remove_listener("response", handle_response)
        
        # 整理结果数据
        combined_result = {}
        for url, data in data_dict.items():
            if "gn/fetch-file-infos" in url:
                combined_result['main_file'] = data
            elif "gn/scxx/tzs/fjjsbg" in url:
                combined_result['attachment'] = data
        
        return combined_result
    
    async def _handle_attachment_file(self, name: str, data_page: Page, file_info: dict, an: str) -> dict:
        """
        处理附件文件
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            file_info: 文件信息字典
            an: 申请号
            
        Returns:
            dict: 附件文件信息结果
        """
        data_dict = {}
        captured_urls: Set[str] = set()
        new_page: Optional[Page] = None
        
        try:
            # 响应监听函数
            async def handle_response(response: Response):
                if response.request.resource_type == 'xhr':
                    if response.url not in captured_urls and "/api/view/gn/" in response.url:
                        try:
                            json_data = await response.json()
                            data_dict[response.url] = json_data
                            captured_urls.add(response.url)
                        except Exception as e:
                            self.logger.error(f"{response.url}返回的数据格式错误: {e}")
            
            # 路由拦截处理器
            async def handle_route(route):
                request_url = route.request.url
                if "/api/view/gn/fetch-file-infos" in request_url:
                    try:
                        new_payload = {
                            "ds": file_info['ds'],
                            "rid": file_info['rid'],
                            "wenjiandm": "000000",
                            "zhuanlisqh": file_info['yewuztbh'],
                        }
                        await route.continue_(
                            post_data=json.dumps(new_payload),
                            headers={
                                **route.request.headers,
                                'Content-Type': 'application/json'
                            }
                        )
                        return
                    except Exception as e:
                        self.logger.error(f"--{name}: 处理文件信息请求时发生错误: {e}")
                        await route.continue_()
                        return
                
                await route.continue_()
            
            # 监听新页面创建
            async def handle_page(page: Page):
                nonlocal new_page
                new_page = page
                await new_page.route("**/*", handle_route)
                new_page.on('response', handle_response)
            
            # 添加页面创建监听
            data_page.context.on('page', handle_page)
            
            try:
                # 点击附件名称
                await data_page.get_by_text(file_info['name'], exact=True).click()
                
                # 等待新页面创建和加载
                if not new_page:
                    await data_page.wait_for_timeout(2000)
                
                if new_page:
                    await new_page.wait_for_load_state('domcontentloaded', timeout=5000)
                else:
                    raise Exception("新页面创建失败")
                
            finally:
                # 清理监听器和页面
                data_page.context.remove_listener('page', handle_page)
                if new_page:
                    try:
                        await new_page.close()
                    except Exception as e:
                        self.logger.warning(f"--{name}: 关闭新页面时发生错误: {e}")
            
            # 返回结果
            if data_dict:
                return {
                    "code": 200,
                    "name": file_info['name'],
                    "data": list(data_dict.values()),
                    "msg": f"成功打开附件页面并捕获数据(URLs: {len(captured_urls)})"
                }
            else:
                return {
                    "code": 404,
                    "name": file_info['name'],
                    "data": None,
                    "msg": f"成功打开附件页面，但未捕获到数据(URLs: {len(captured_urls)})"
                }
                
        except Exception as err:
            if check_page_exception_msg_is_crashed(file_info['name'], repr(err), "handle_attachment_file"):
                self.logger.error(f"--{name}: 浏览器或页面已关闭，退出函数")
                raise PageCrashedError(
                    "浏览器或页面已关闭",
                    error_code="PAGE_CRASHED",
                    crash_reason=str(err)
                )
            
            self.logger.error(f"--{name}: 处理附件失败: {err}")
            return {
                "code": 505,
                "name": file_info['name'],
                "data": None,
                "msg": f"处理附件失败: {err}"
            }
    
    async def _handle_jsbg_file(self, name: str, data_page: Page, file_meta_data: dict, an: str) -> dict:
        """
        处理检索报告文件
        
        Args:
            name: 任务名称
            data_page: 数据页面对象
            file_meta_data: 文件元数据
            an: 申请号
            
        Returns:
            dict: 检索报告数据
        """
        data_dict = {}
        captured_urls: Set[str] = set()
        new_page: Optional[Page] = None
        
        # 响应监听函数
        async def handle_response(response: Response):
            if response.request.resource_type == 'xhr':
                if response.url not in captured_urls and "/api/view/gn/" in response.url:
                    try:
                        json_data = await response.json()
                        data_dict[jsbg_type] = json_data
                        captured_urls.add(response.url)
                    except Exception as e:
                        self.logger.error(f"{response.url}返回的数据格式错误: {e}")
        
        # 监听新页面创建
        async def handle_page(page: Page):
            nonlocal new_page
            new_page = page
            new_page.on('response', handle_response)
        
        jsbg_to_capture = {}
        if file_meta_data.get("shoucijsbg"):
            jsbg_to_capture["首次检索"] = file_meta_data["shoucijsbg"]
        if file_meta_data.get("buchongjsbg"):
            jsbg_to_capture["补充检索"] = file_meta_data["buchongjsbg"]
        
        # 添加页面创建监听
        data_page.context.on('page', handle_page)
        
        try:
            for jsbg_type, jsbg_id in jsbg_to_capture.items():
                if jsbg_id is None:
                    continue
                
                try:
                    # 点击检索报告
                    await data_page.get_by_text(jsbg_type, exact=True).click()
                    
                    # 等待新页面创建和加载
                    if not new_page:
                        await data_page.wait_for_timeout(2000)
                    
                    if new_page:
                        await new_page.wait_for_load_state('networkidle', timeout=10000)
                    else:
                        raise Exception("新页面创建失败")
                        
                finally:
                    if new_page:
                        try:
                            await new_page.close()
                        except Exception as e:
                            self.logger.warning(f"--{name}: 关闭新页面时发生错误: {e}")
                        new_page = None
                        
        finally:
            # 移除页面创建监听
            data_page.context.remove_listener('page', handle_page)
        
        # 返回结果
        if data_dict:
            return {
                "code": 200,
                "name": "检索报告数据",
                "data": data_dict,
                "msg": f"成功打开附件页面并捕获数据(URLs: {len(captured_urls)})"
            }
        else:
            return {
                "code": 404,
                "name": "检索报告数据",
                "data": None,
                "msg": f"成功打开附件页面，但未捕获到数据(URLs: {len(captured_urls)})"
            }
