"""
Click模块 - 重构后的页面点击和数据提取模块

这个模块提供与原始corutine_click.py完全兼容的接口，
同时采用模块化设计提供更好的可维护性和错误处理能力。

主要功能：
- 主页查询和页面跳转
- 数据页面初始化和刷新
- 事件数据提取（同步和异步模式）
- 文件信息获取和附件处理
- 完善的异常处理和重试机制

使用方式：
    # 方式1：直接替换导入
    from click import main_page_query_an, main_page_click_an, get_appl_data
    
    # 方式2：模块导入
    import click
    
    # 方式3：别名导入（推荐，无缝替换）
    import click as corutine_click
"""

import asyncio
from typing import Optional, List, Union
from playwright.async_api import Page

# 导入配置和工具
from src.cpquery_scraper.config import config
from src.cpquery_scraper.utils.logger import get_logger

# 导入重构后的模块
from .query_manager import QueryManager
from .data_extractor import DataExtractor
from .sync_click_manager import SyncClickManager
from .file_handler import FileHandler

# 导入异常类
from .exceptions import (
    ClickError, QueryError, PageInitError, DataExtractionError,
    FileAccessError, RouteInterceptError, ResponseTimeoutError,
    PageCrashedError, RetryExhaustedError, ClickTimeoutError,
    DataValidationError
)

# 模块信息
__version__ = "2.0.0"
__author__ = "Refactored Click Module"

# 设置日志
__name__ = "corutine_spider"
logger = get_logger(__name__)

# 初始化管理器
_query_manager = QueryManager(config, logger)
_data_extractor = DataExtractor(config, logger)
_sync_click_manager = SyncClickManager(config, logger)
_file_handler = FileHandler(config, logger)

# 禁用print输出（如果不是测试模式）
if not config.TEST_MODE:
    def print(*args, **kwargs):
        logger.info(*args, **kwargs)


# ==================== 兼容接口函数 ====================

async def main_page_query_an(name: str, query_page: Page, an: str, retry_times: int = 0) -> dict:
    """
    在cpquery主页查询页面,执行查询an，以获取查询结果
    
    这是与原模块完全兼容的接口函数
    
    Args:
        name: 任务名称
        query_page: 查询页面对象
        an: 申请号
        retry_times: 重试次数
        
    Returns:
        dict: 查询结果
        
    Raises:
        QueryError: 查询失败时抛出
        PageCrashedError: 页面崩溃时抛出
    """
    try:
        return await _query_manager.query_an(name, query_page, an, retry_times)
    except QueryError as e:
        # 为了保持兼容性，将异常转换为原始格式
        if e.error_code == "QUERY_MAX_RETRIES":
            return {
                "code": 502,
                "data": None,
                "msg": f"查询页查询 {an} 5次后失败，放弃查询",
            }
        else:
            # 重新抛出异常，让上层处理
            raise
    except Exception:
        # 其他异常直接重新抛出
        raise


async def main_page_click_an(name: str, query_page: Page, an: str, retry_times: int = 0) -> Page:
    """
    点击查询出的结果，弹出新的页面（数据详情页），获取该页面
    
    这是与原模块完全兼容的接口函数
    
    Args:
        name: 任务名称
        query_page: 查询页面对象
        an: 申请号
        retry_times: 重试次数
        
    Returns:
        Page: 数据详情页面对象
        
    Raises:
        PageInitError: 页面初始化失败时抛出
        PageCrashedError: 页面崩溃时抛出
    """
    return await _query_manager.click_an_result(name, query_page, an, retry_times)


async def get_appl_data(
    name: str,
    data_page: Page,
    an: str,
    fut: Optional[asyncio.Future] = None,
    retry_times: int = 0
) -> Optional[asyncio.Future[dict[str, Union[int, str, None]]]]:
    """
    从初始化好的数据页面查询申请信息
    
    这是与原模块完全兼容的接口函数
    
    Args:
        name: 任务名称
        data_page: 数据页面对象
        an: 申请号
        fut: 异步Future对象
        retry_times: 重试次数
        
    Returns:
        Future对象或None
        
    Raises:
        RetryExhaustedError: 重试次数耗尽时抛出
        DataExtractionError: 数据提取失败时抛出
    """
    return await _data_extractor.extract_application_data(name, data_page, an, fut, retry_times)


async def click_event_button_and_get_data(
    name: str,
    data_page: Page,
    event: str,
    an: str,
    fut: Optional[asyncio.Future] = None,
    retry_times: int = 0,
) -> Optional[asyncio.Future[dict[str, Union[int, str, None]]]]:
    """
    异步模式：同时点击、获取各个事件的按钮，以及提取数据
    
    这是与原模块完全兼容的接口函数
    
    Args:
        name: 任务名称
        data_page: 数据页面对象
        event: 事件名称
        an: 申请号
        fut: Future对象
        retry_times: 重试次数
        
    Returns:
        Future对象或None
        
    Raises:
        RetryExhaustedError: 重试次数耗尽时抛出
        DataExtractionError: 数据提取失败时抛出
    """
    return await _data_extractor.extract_event_data(name, data_page, event, an, fut, retry_times)


async def click_event_button_and_get_data_sync(
    name: str,
    data_page: Page,
    an: str,
    url_init_page: str,
) -> List[dict[str, dict]]:
    """
    同步模式：逐个点击、获取各个事件的按钮，以及提取数据
    
    这是与原模块完全兼容的接口函数
    
    Args:
        name: 任务名称
        data_page: 数据页面对象
        an: 申请号
        url_init_page: 初始化页面URL
        
    Returns:
        list: 事件数据列表
        
    Raises:
        DataExtractionError: 数据提取失败时抛出
        PageCrashedError: 页面崩溃时抛出
    """
    return await _sync_click_manager.click_events_sync(name, data_page, an, url_init_page)


# ==================== 新增的便利函数 ====================

async def get_page_with_retry(name: str, query_page: Page, an: str, max_retries: int = 3) -> Page:
    """
    获取数据页面，带重试机制
    
    这是新增的便利函数，提供更好的错误处理
    
    Args:
        name: 任务名称
        query_page: 查询页面对象
        an: 申请号
        max_retries: 最大重试次数
        
    Returns:
        Page: 数据详情页面对象
        
    Raises:
        QueryError: 查询失败时抛出
        PageInitError: 页面初始化失败时抛出
    """
    return await _query_manager.get_page_with_retry(name, query_page, an, max_retries)


def check_file_permission(file_info: dict) -> bool:
    """
    检查文件权限
    
    这是新增的便利函数
    
    Args:
        file_info: 文件信息字典
        
    Returns:
        bool: 是否有权限访问文件
    """
    return _file_handler.check_file_permission(file_info)


async def click_file_name(name: str, data_page: Page, file_info: dict, an: str) -> dict:
    """
    获取文件信息
    
    这是新增的便利函数，提供统一的文件处理接口
    
    Args:
        name: 任务名称
        data_page: 数据页面对象
        file_info: 文件信息字典
        an: 申请号
        
    Returns:
        dict: 文件信息结果
        
    Raises:
        FileAccessError: 文件访问失败时抛出
        ResponseTimeoutError: 响应超时时抛出
        PageCrashedError: 页面崩溃时抛出
    """
    return await _file_handler.click_file_name(name, data_page, file_info, an)


# ==================== 模块导出 ====================

# 兼容性导出（与原模块相同的函数名）
__all__ = [
    # 主要兼容函数
    'main_page_query_an',
    'main_page_click_an', 
    'get_appl_data',
    'click_event_button_and_get_data',
    'click_event_button_and_get_data_sync',
    
    # 新增便利函数
    'get_page_with_retry',
    'check_file_permission',
    'click_file_name',
    
    # 异常类
    'ClickError',
    'QueryError',
    'PageInitError',
    'DataExtractionError',
    'FileAccessError',
    'RouteInterceptError',
    'ResponseTimeoutError',
    'PageCrashedError',
    'RetryExhaustedError',
    'ClickTimeoutError',
    'DataValidationError',
    
    # 管理器类（供高级用户使用）
    'QueryManager',
    'DataExtractor',
    'SyncClickManager',
    'FileHandler',
]
