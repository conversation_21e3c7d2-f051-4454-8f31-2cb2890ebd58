"""
Click模块异常类测试
验证异常类的功能和继承关系
"""
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from ..exceptions import *


def test_exception_classes():
    """测试异常类功能"""
    print("开始测试改进后的异常类...")
    
    print("=== 测试基础点击异常 ===")
    # 基本异常
    basic_error = ClickError("基本点击错误")
    print(f"基本异常: {basic_error}")
    print(f"异常表示: {repr(basic_error)}")
    
    # 带错误码的异常
    error_with_code = ClickError("带错误码的点击错误", error_code="CLICK_001")
    print(f"带错误码: {error_with_code}")
    print(f"错误码: {error_with_code.error_code}")
    
    # 带详细信息的异常
    error_with_details = ClickError(
        "详细点击错误", 
        error_code="CLICK_002",
        details={'operation': 'click_button', 'attempt': 3}
    )
    print(f"详细异常: {error_with_details}")
    print(f"详细信息: {error_with_details.details}")
    
    print("\n=== 测试查询异常 ===")
    query_error = QueryError(
        "查询申请号失败",
        error_code="QUERY_FAILED",
        an="2023123456789",
        retry_count=3
    )
    print(f"查询异常: {query_error}")
    print(f"申请号: {query_error.an}")
    print(f"重试次数: {query_error.retry_count}")
    print(f"详细信息: {query_error.details}")
    
    print("\n=== 测试页面初始化异常 ===")
    page_error = PageInitError(
        "数据页面初始化失败",
        error_code="PAGE_INIT_FAILED",
        page_url="https://example.com/current",
        expected_url_pattern="**/detail/index?zhuanlisqh=**"
    )
    print(f"页面异常: {page_error}")
    print(f"当前URL: {page_error.page_url}")
    print(f"期望URL模式: {page_error.expected_url_pattern}")
    print(f"详细信息: {page_error.details}")
    
    print("\n=== 测试数据提取异常 ===")
    data_error = DataExtractionError(
        "提取申请信息失败",
        error_code="DATA_EXTRACTION_FAILED",
        event="申请信息",
        an="2023123456789",
        data_type="application_info"
    )
    print(f"数据异常: {data_error}")
    print(f"事件: {data_error.event}")
    print(f"申请号: {data_error.an}")
    print(f"数据类型: {data_error.data_type}")
    print(f"详细信息: {data_error.details}")
    
    print("\n=== 测试文件访问异常 ===")
    file_error = FileAccessError(
        "无权限访问文件",
        error_code="FILE_ACCESS_DENIED",
        file_name="通知书.pdf",
        file_type="TZS",
        permission_denied=True
    )
    print(f"文件异常: {file_error}")
    print(f"文件名: {file_error.file_name}")
    print(f"文件类型: {file_error.file_type}")
    print(f"权限被拒绝: {file_error.permission_denied}")
    print(f"详细信息: {file_error.details}")
    
    print("\n=== 测试路由拦截异常 ===")
    route_error = RouteInterceptError(
        "路由拦截设置失败",
        error_code="ROUTE_SETUP_FAILED",
        route_pattern="**/api/view/gn/sqxx?hHp4Kgam=**",
        intercept_type="POST_DATA_MODIFICATION"
    )
    print(f"路由异常: {route_error}")
    print(f"路由模式: {route_error.route_pattern}")
    print(f"拦截类型: {route_error.intercept_type}")
    print(f"详细信息: {route_error.details}")
    
    print("\n=== 测试响应超时异常 ===")
    timeout_error = ResponseTimeoutError(
        "等待页面响应超时",
        error_code="RESPONSE_TIMEOUT",
        timeout_seconds=30.0,
        operation="click_event_button"
    )
    print(f"超时异常: {timeout_error}")
    print(f"超时时间: {timeout_error.timeout_seconds}秒")
    print(f"操作: {timeout_error.operation}")
    print(f"详细信息: {timeout_error.details}")
    
    print("\n=== 测试页面崩溃异常 ===")
    crash_error = PageCrashedError(
        "浏览器页面已崩溃",
        error_code="PAGE_CRASHED",
        page_url="https://example.com/detail",
        crash_reason="Target closed"
    )
    print(f"崩溃异常: {crash_error}")
    print(f"页面URL: {crash_error.page_url}")
    print(f"崩溃原因: {crash_error.crash_reason}")
    print(f"详细信息: {crash_error.details}")
    
    print("\n=== 测试重试耗尽异常 ===")
    retry_error = RetryExhaustedError(
        "重试次数已达上限",
        error_code="MAX_RETRIES_EXCEEDED",
        max_retries=5,
        operation="extract_event_data"
    )
    print(f"重试异常: {retry_error}")
    print(f"最大重试次数: {retry_error.max_retries}")
    print(f"操作: {retry_error.operation}")
    print(f"详细信息: {retry_error.details}")


def test_exception_inheritance():
    """测试异常继承关系"""
    print("\n=== 测试异常继承关系 ===")
    
    # 测试所有异常都继承自ClickError
    exception_classes = [
        QueryError, PageInitError, DataExtractionError, FileAccessError,
        RouteInterceptError, ResponseTimeoutError, PageCrashedError, RetryExhaustedError
    ]
    
    for exc_class in exception_classes:
        is_click_error = issubclass(exc_class, ClickError)
        is_exception = issubclass(exc_class, Exception)
        print(f"{exc_class.__name__} 是 ClickError 的子类: {is_click_error}")
        print(f"{exc_class.__name__} 是 Exception 的子类: {is_exception}")
    
    # 测试向后兼容的异常类
    print(f"ClickTimeoutError 是 ResponseTimeoutError 的子类: {issubclass(ClickTimeoutError, ResponseTimeoutError)}")
    print(f"DataValidationError 是 DataExtractionError 的子类: {issubclass(DataValidationError, DataExtractionError)}")


def test_exception_handling_scenarios():
    """测试异常处理场景"""
    print("\n=== 测试异常处理场景 ===")
    
    # 场景1：处理查询失败
    try:
        raise QueryError("查询失败", an="test123", retry_count=3)
    except ClickError as e:
        print(f"捕获查询异常: {e}")
        print(f"异常类型: {type(e).__name__}")
        if hasattr(e, 'an'):
            print(f"申请号: {e.an}")
    
    # 场景2：处理页面崩溃
    try:
        raise PageCrashedError("页面崩溃", crash_reason="Target closed")
    except ClickError as e:
        print(f"捕获页面崩溃: {e}")
        print(f"崩溃原因: {getattr(e, 'crash_reason', '未知')}")
    
    # 场景3：处理文件访问权限
    try:
        raise FileAccessError("权限不足", file_name="secret.pdf", permission_denied=True)
    except ClickError as e:
        print(f"捕获文件访问异常: {e}")
        print(f"权限被拒绝: {getattr(e, 'permission_denied', False)}")


def test_error_logging():
    """测试错误日志记录"""
    print("\n=== 测试错误日志记录 ===")
    
    errors = [
        QueryError("查询错误", error_code="QUERY_001", an="test***"),
        DataExtractionError("数据提取失败", details={'event': '申请信息', 'retry_count': 3}),
        FileAccessError("文件访问失败", details={'file_type': 'TZS', 'permission': 'denied'})
    ]
    
    for error in errors:
        print(f"[ERROR] {type(error).__name__}: {error}")
        if hasattr(error, 'error_code') and error.error_code:
            print(f"[ERROR] 错误码: {error.error_code}")
        if hasattr(error, 'details') and error.details:
            print(f"[ERROR] 详细信息: {error.details}")
        print()


def main():
    """主函数"""
    print("开始测试Click模块异常类...")
    print("=" * 60)
    
    try:
        test_exception_classes()
        test_exception_inheritance()
        test_exception_handling_scenarios()
        test_error_logging()
        
        print("\n" + "=" * 60)
        print("所有异常类测试完成！")
        return True
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
