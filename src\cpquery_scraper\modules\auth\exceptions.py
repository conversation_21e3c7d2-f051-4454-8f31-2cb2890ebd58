"""
登录模块异常类定义
提供详细的异常信息和错误处理能力
"""
from typing import Optional, Dict, Any


class LoginError(Exception):
    """
    登录过程中的基础异常类

    所有登录相关异常的基类，提供统一的错误处理接口
    """

    def __init__(self, message: str, error_code: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None):
        """
        初始化登录异常

        Args:
            message: 错误消息
            error_code: 错误代码（如API返回的错误码）
            details: 详细错误信息字典
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def __str__(self) -> str:
        """返回格式化的错误信息"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

    def __repr__(self) -> str:
        """返回详细的异常表示"""
        return f"{self.__class__.__name__}(message='{self.message}', error_code='{self.error_code}')"


class CaptchaError(LoginError):
    """
    验证码处理相关异常

    当验证码识别、处理或验证失败时抛出
    """

    def __init__(self, message: str, captcha_type: Optional[str] = None,
                 retry_count: Optional[int] = None, **kwargs):
        """
        初始化验证码异常

        Args:
            message: 错误消息
            captcha_type: 验证码类型（如 'blockPuzzle'）
            retry_count: 已重试次数
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.captcha_type = captcha_type
        self.retry_count = retry_count

        # 添加到详细信息中
        if captcha_type:
            self.details['captcha_type'] = captcha_type
        if retry_count is not None:
            self.details['retry_count'] = retry_count


class CredentialsError(LoginError):
    """
    用户凭据错误异常

    当用户名、密码或用户类型错误时抛出
    """

    def __init__(self, message: str, user_id: Optional[str] = None,
                 user_type: Optional[str] = None, **kwargs):
        """
        初始化凭据异常

        Args:
            message: 错误消息
            user_id: 用户ID
            user_type: 用户类型
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.user_id = user_id
        self.user_type = user_type

        # 添加到详细信息中（注意不要记录敏感信息如密码）
        if user_id:
            # 只记录用户ID的前几位，保护隐私
            masked_id = user_id[:4] + '*' * (len(user_id) - 4) if len(user_id) > 4 else user_id
            self.details['user_id'] = masked_id
        if user_type:
            self.details['user_type'] = user_type


class NetworkError(LoginError):
    """
    网络连接异常

    当网络请求失败、超时或连接问题时抛出
    """

    def __init__(self, message: str, url: Optional[str] = None,
                 status_code: Optional[int] = None, timeout: Optional[float] = None, **kwargs):
        """
        初始化网络异常

        Args:
            message: 错误消息
            url: 请求的URL
            status_code: HTTP状态码
            timeout: 超时时间
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.url = url
        self.status_code = status_code
        self.timeout = timeout

        # 添加到详细信息中
        if url:
            self.details['url'] = url
        if status_code:
            self.details['status_code'] = status_code
        if timeout:
            self.details['timeout'] = timeout


class PageCrashedError(LoginError):
    """
    页面崩溃异常

    当浏览器页面崩溃或上下文关闭时抛出
    """

    def __init__(self, message: str, page_url: Optional[str] = None,
                 crash_reason: Optional[str] = None, **kwargs):
        """
        初始化页面崩溃异常

        Args:
            message: 错误消息
            page_url: 崩溃页面的URL
            crash_reason: 崩溃原因
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.page_url = page_url
        self.crash_reason = crash_reason

        # 添加到详细信息中
        if page_url:
            self.details['page_url'] = page_url
        if crash_reason:
            self.details['crash_reason'] = crash_reason


class TimeoutError(LoginError):
    """
    操作超时异常

    当登录操作超过预期时间时抛出
    """

    def __init__(self, message: str, operation: Optional[str] = None,
                 timeout_seconds: Optional[float] = None, **kwargs):
        """
        初始化超时异常

        Args:
            message: 错误消息
            operation: 超时的操作名称
            timeout_seconds: 超时时间（秒）
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.operation = operation
        self.timeout_seconds = timeout_seconds

        # 添加到详细信息中
        if operation:
            self.details['operation'] = operation
        if timeout_seconds:
            self.details['timeout_seconds'] = timeout_seconds


class ConfigurationError(LoginError):
    """
    配置错误异常

    当配置项缺失或无效时抛出
    """

    def __init__(self, message: str, config_key: Optional[str] = None,
                 config_value: Optional[Any] = None, **kwargs):
        """
        初始化配置异常

        Args:
            message: 错误消息
            config_key: 配置项键名
            config_value: 配置项值
            **kwargs: 其他参数传递给基类
        """
        super().__init__(message, **kwargs)
        self.config_key = config_key
        self.config_value = config_value

        # 添加到详细信息中
        if config_key:
            self.details['config_key'] = config_key
        if config_value is not None:
            self.details['config_value'] = str(config_value)


# 为了向后兼容，保留原有的简单异常类
class LoginTimeoutError(TimeoutError):
    """登录超时异常（向后兼容）"""
    pass


class CaptchaTimeoutError(CaptchaError, TimeoutError):
    """验证码超时异常（向后兼容）"""
    pass