"""
重构后登录模块使用示例
演示如何使用新的auth模块进行用户登录
"""
import asyncio
import sys
import os
from playwright.async_api import async_playwright

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 方式1：兼容性导入（推荐用于快速迁移）
import auth
from src.cpquery_scraper.config import config

# 方式2：直接使用类（推荐用于新项目）
from auth import LoginManager, UserProvider, LoginChecker, CaptchaSolver
from auth.exceptions import (
    LoginError, CredentialsError, CaptchaError, NetworkError,
    PageCrashedError, TimeoutError, ConfigurationError
)
from src.cpquery_scraper.utils.logger import get_logger

async def example_compatible_usage():
    """
    示例1：兼容性使用方式
    与原corutine_user_login模块使用方式完全相同
    """
    print("=== 兼容性使用示例 ===")
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 1. 获取用户信息（与原模块完全相同）
            user_info = auth.get_user()
            print(f"获取到用户: {user_info['id']} ({user_info['type']})")
            
            # 2. 检查登录状态（与原模块完全相同）
            await page.goto(config.AUTH_MAINPAGE_URL)
            login_status = await auth.check_login_page_is_logined("示例", page)
            print(f"当前登录状态: {login_status}")
            
            # 3. 执行用户登录（与原模块完全相同）
            result = await auth.user_login("示例", page, user_info, config.CPQUERY_URL)
            if result:
                logged_page, username = result
                print(f"登录成功，用户名: {username}")
            else:
                print("登录失败")
                
        except Exception as e:
            print(f"登录过程出错: {e}")
        finally:
            await context.close()
            await browser.close()

async def example_class_based_usage():
    """
    示例2：基于类的使用方式
    提供更好的控制和错误处理
    """
    print("\n=== 基于类的使用示例 ===")
    
    # 初始化组件
    logger = get_logger(__name__)
    user_provider = UserProvider(config, logger)
    login_checker = LoginChecker(config, logger)
    login_manager = LoginManager(config, logger)
    captcha_solver = CaptchaSolver(config, logger)
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 1. 获取用户信息
            user_info = user_provider.get_user()
            print(f"获取到用户: {user_info['id']} ({user_info['type']})")
            
            # 2. 检查登录状态
            await page.goto(config.AUTH_MAINPAGE_URL)
            login_status = await login_checker.check_login_status("示例", page)
            print(f"当前登录状态: {login_status}")
            
            # 3. 执行登录
            result = await login_manager.login("示例", page, user_info, config.CPQUERY_URL)
            if result:
                logged_page, username = result
                print(f"登录成功，用户名: {username}")
            else:
                print("登录失败")
                
        except CredentialsError as e:
            print(f"用户凭据错误: {e}")
            print(f"  错误码: {e.error_code}")
            print(f"  用户类型: {e.user_type}")
            print(f"  详细信息: {e.details}")
        except CaptchaError as e:
            print(f"验证码处理错误: {e}")
            print(f"  验证码类型: {e.captcha_type}")
            print(f"  重试次数: {e.retry_count}")
        except NetworkError as e:
            print(f"网络错误: {e}")
            print(f"  URL: {e.url}")
            print(f"  状态码: {e.status_code}")
        except PageCrashedError as e:
            print(f"页面崩溃: {e}")
            print(f"  页面URL: {e.page_url}")
            print(f"  崩溃原因: {e.crash_reason}")
        except TimeoutError as e:
            print(f"操作超时: {e}")
            print(f"  操作: {e.operation}")
            print(f"  超时时间: {e.timeout_seconds}秒")
        except LoginError as e:
            print(f"登录过程错误: {e}")
            print(f"  错误码: {e.error_code}")
            print(f"  详细信息: {e.details}")
        except Exception as e:
            print(f"未知错误: {e}")
        finally:
            await context.close()
            await browser.close()

async def example_advanced_usage():
    """
    示例3：高级使用方式
    演示错误处理和重试机制
    """
    print("\n=== 高级使用示例 ===")
    
    logger = get_logger(__name__)
    login_manager = LoginManager(config, logger)
    user_provider = UserProvider(config, logger)
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 获取用户信息
                user_info = user_provider.get_user()
                print(f"尝试第 {retry_count + 1} 次登录，用户: {user_info['id']}")
                
                # 执行登录
                result = await login_manager.login("高级示例", page, user_info, config.CPQUERY_URL)
                if result:
                    logged_page, username = result
                    print(f"登录成功！用户名: {username}")
                    break
                else:
                    print("登录失败，准备重试...")
                    retry_count += 1
                    
            except CredentialsError as e:
                print(f"用户凭据错误，尝试其他用户: {e}")
                retry_count += 1
                continue
                
            except CaptchaError as e:
                print(f"验证码错误，重试: {e}")
                retry_count += 1
                await asyncio.sleep(2)  # 等待2秒后重试
                continue
                
            except Exception as e:
                print(f"其他错误: {e}")
                retry_count += 1
                await asyncio.sleep(5)  # 等待5秒后重试
                continue
        
        if retry_count >= max_retries:
            print("达到最大重试次数，登录失败")
            
        await context.close()
        await browser.close()

def example_utility_functions():
    """
    示例4：工具函数使用
    演示验证码处理和其他工具函数
    """
    print("\n=== 工具函数使用示例 ===")
    
    # 1. 验证码偏移计算
    fake_base64_data = {
        "originalImageBase64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    }
    
    offset = auth.calculate_offset(fake_base64_data)
    print(f"计算得到的验证码偏移量: {offset}")
    
    # 2. 用户信息获取（多次调用演示随机性）
    print("随机获取用户信息:")
    for i in range(5):
        user = auth.get_user()
        print(f"  用户 {i+1}: {user['id']} ({user['type']})")

async def example_error_handling():
    """
    示例5：错误处理演示
    展示各种异常情况的处理
    """
    print("\n=== 错误处理示例 ===")
    
    logger = get_logger(__name__)
    login_manager = LoginManager(config, logger)
    
    # 测试无效用户信息
    invalid_user = {
        'id': 'invalid_user_id',
        'pass': 'invalid_password',
        'type': 'invalid_type'
    }
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            result = await login_manager.login("错误测试", page, invalid_user, config.CPQUERY_URL)
            print(f"意外成功: {result}")
        except CredentialsError as e:
            print(f"✓ 正确捕获用户凭据错误: {e}")
            print(f"  错误码: {e.error_code}")
            print(f"  用户类型: {e.user_type}")
            print(f"  详细信息: {e.details}")
        except Exception as e:
            print(f"✓ 捕获其他错误: {type(e).__name__}: {e}")
            if hasattr(e, 'error_code'):
                print(f"  错误码: {e.error_code}")
            if hasattr(e, 'details'):
                print(f"  详细信息: {e.details}")
        finally:
            await context.close()
            await browser.close()

async def main():
    """主函数，运行所有示例"""
    print("重构后登录模块使用示例")
    print("=" * 50)
    
    # 基础示例（需要网络连接）
    try:
        # 兼容性使用示例
        await example_compatible_usage()
        
        # 基于类的使用示例
        await example_class_based_usage()
        
        # 高级使用示例
        await example_advanced_usage()
        
        # 错误处理示例
        await example_error_handling()
        
    except Exception as e:
        print(f"网络相关示例跳过: {e}")
    
    # 工具函数示例（不需要网络）
    example_utility_functions()
    
    print("\n" + "=" * 50)
    print("所有示例运行完成！")

if __name__ == "__main__":
    asyncio.run(main())
