## PDF中间文件URL拼接规则

### 背景
获取中间文件的URL规则，用于下载中间文件。


https://cpquery.cponline.cnipa.gov.cn/api/pcshoss/view/fetch-file?osslujing=sqwj/2023/08/09/file/cn/2023109997363/301556706282/69721d2e07445178823127e70060f844.pdf&wenjianhzm=pdf&timestamp=1728186565621&sign=c92490bd9ca86cd4e1d9919261b8e549&isDN=false&ds=SQWJ&wenjiandm=100001

必要参数解析：

域名：https://cpquery.cponline.cnipa.gov.cn/api/pcshoss/view/fetch-file?osslujing=
pdf文件路径：sqwj/2023/08/09/file/cn/2023109997363/301556706282/69721d2e07445178823127e70060f844.pdf
文件扩展名：&wenjianhzm=pdf
时间戳：&timestamp=1728032560125  （对应时间为：2024-10-04 17:02:40）
签名：&sign=8b1aaae38d8245190bb6c4dd7012b9df
未知参数：&isDN=false
未知参数（猜测可能为：代码属性，申请文件）：&ds=SQWJ
文件代码：&wenjiandm=100001    （100001为权利要求书）

拼接规则：
1、固定前缀：https://cpquery.cponline.cnipa.gov.cn/api/pcshoss/view/fetch-file?
2、pdf文件路径：osslujing=sqwj/2023/08/09/file/cn/2023109997363/301556706282/69721d2e07445178823127e70060f844.pdf
3、文件扩展名：&wenjianhzm=pdf
4、时间戳：&timestamp=1728032560125
5、签名：&sign=8b1aaae38d8245190bb6c4dd7012b9df
6、是否 官文？通知书？：&isDN=false
7、代码属性(文件类别)：&ds=SQWJ
8、文件代码：&wenjiandm=100001


--task-1: 审查信息 -> 申请文件 -> 2021-07-21  发明专利请求书 开始获取文件信息:
--task-1:超时原因未获取到数据，放弃获取文件:2021-07-21  发明专利请求书信息:Timeout 30000ms exceeded while waiting for event "response"
=========================== logs ===========================
waiting for response **/api/view/gn/fetch-file-infos?hHp4Kgam=**
============================================================
--task-1: 审查信息 -> 申请文件 -> 2021-07-21  发明专利请求书” 点击后未返回正确code，返回数据 =
{'code': 504, 'data': None, 'msg': '获取文件:2021-07-21  发明专利请求书信息超时，放弃获取文件信息'}

todo：
1、捕捉 "该用户没有权限查看实体文件!" 错误，并返回权限不足的提示信息。 ok
2、增加获取文件流的功能，用于下载中间文件。