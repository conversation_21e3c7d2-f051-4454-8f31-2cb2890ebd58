"""
登录模块工具函数
"""

from typing import Optional
from playwright.async_api import Page

async def get_welcome_message(page: Page) -> Optional[str]:
    """
    获取页面上的欢迎信息
    
    Args:
        page: Playwright页面对象
        
    Returns:
        欢迎信息文本，如果未找到则返回None
    """
    try:
        welcome_locator = page.get_by_text("欢迎您，")
        if await welcome_locator.count() > 0:
            return await welcome_locator.inner_text()
        return None
    except Exception:
        return None

def extract_username_from_welcome(welcome_text: str) -> str:
    """
    从欢迎信息中提取用户名
    
    Args:
        welcome_text: 欢迎信息文本
        
    Returns:
        提取的用户名
    """
    return welcome_text.split("欢迎您，")[-1]

def check_page_crashed(error_message: str) -> bool:
    """
    检查错误信息是否表明页面已崩溃
    
    Args:
        error_message: 错误信息文本
        
    Returns:
        如果页面已崩溃则返回True，否则返回False
    """
    crash_patterns = [
        "Target closed",
        "Page crashed",
        "Browser context",
        "Target page, context or browser has been closed"
    ]
    return any(pattern in error_message for pattern in crash_patterns)

def check_page_exception_msg_is_crashed(name: str, error_message: str, function_name: str) -> bool:
    """
    兼容原模块的页面崩溃检查函数
    
    Args:
        name: 协程名称
        error_message: 错误信息
        function_name: 函数名称
        
    Returns:
        如果页面已崩溃则返回True，否则返回False
    """
    return check_page_crashed(error_message)