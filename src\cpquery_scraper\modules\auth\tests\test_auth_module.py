"""
测试重构后的登录模块
验证与原模块的兼容性和功能一致性
"""
import asyncio
import time
import pytest
from playwright.async_api import async_playwright

# 添加项目根目录到路径
import sys
import os
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 测试新的auth模块
import src.cpquery_scraper.modules.auth as auth
from src.cpquery_scraper.config import config

# 测试原模块的兼容性
import src.cpquery_scraper.modules.auth as corutine_user_login

@pytest.mark.asyncio
async def test_new_auth_module():
    """测试新的auth模块"""
    print("=== 测试新的auth模块 ===")
    
    async with async_playwright() as playwright:
        # 初始化浏览器
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 测试获取用户信息
            print("1. 测试获取用户信息...")
            user_info = auth.get_user()
            print(f"获取到用户信息: {user_info}")
            
            # 测试登录状态检查
            print("2. 测试登录状态检查...")
            await page.goto(config.AUTH_MAINPAGE_URL)
            login_status = await auth.check_login_page_is_logined("测试", page)
            print(f"登录状态检查结果: {login_status}")
            
            # 测试用户登录
            print("3. 测试用户登录...")
            result = await auth.user_login("测试", page, user_info, config.CPQUERY_URL)
            if result:
                page_result, username = result
                print(f"登录成功，用户名: {username}")
            else:
                print("登录失败")
                
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
        finally:
            await context.close()
            await browser.close()

@pytest.mark.asyncio
async def test_original_module():
    """测试原模块"""
    print("\n=== 测试原模块 ===")
    
    async with async_playwright() as playwright:
        # 初始化浏览器
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # 测试获取用户信息
            print("1. 测试获取用户信息...")
            user_info = corutine_user_login.get_user()
            print(f"获取到用户信息: {user_info}")
            
            # 测试登录状态检查
            print("2. 测试登录状态检查...")
            await page.goto(config.AUTH_MAINPAGE_URL)
            login_status = await corutine_user_login.check_login_page_is_logined("测试", page)
            print(f"登录状态检查结果: {login_status}")
            
            # 测试用户登录
            print("3. 测试用户登录...")
            result = await corutine_user_login.user_login("测试", page, user_info, config.CPQUERY_URL)
            if result:
                page_result, username = result
                print(f"登录成功，用户名: {username}")
            else:
                print("登录失败")
                
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
        finally:
            await context.close()
            await browser.close()

def test_compatibility():
    """测试兼容性 - 比较两个模块的接口"""
    print("\n=== 测试接口兼容性 ===")
    
    # 检查函数是否存在
    functions_to_check = [
        'get_user',
        'user_login', 
        'check_login_page_is_logined',
        'get_page_logined',
        'calculate_offset'
    ]
    
    print("检查原模块函数:")
    for func_name in functions_to_check:
        if hasattr(corutine_user_login, func_name):
            print(f"  [OK] {func_name} 存在")
        else:
            print(f"  [FAIL] {func_name} 不存在")
    
    print("\n检查新模块函数:")
    for func_name in functions_to_check:
        if hasattr(auth, func_name):
            print(f"  [OK] {func_name} 存在")
        else:
            print(f"  [FAIL] {func_name} 不存在")

@pytest.mark.asyncio
async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试无效用户信息
        print("1. 测试无效用户信息处理...")
        invalid_user = {'id': 'invalid', 'pass': 'invalid', 'type': 'invalid'}
        
        async with async_playwright() as playwright:
            browser = await playwright.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                result = await auth.user_login("错误测试", page, invalid_user, config.CPQUERY_URL)
                print(f"无效用户登录结果: {result}")
            except Exception as e:
                print(f"捕获到预期的错误: {type(e).__name__}: {e}")
            finally:
                await context.close()
                await browser.close()
                
    except Exception as e:
        print(f"错误处理测试中出现意外错误: {e}")

def performance_comparison():
    """性能对比测试"""
    print("\n=== 性能对比测试 ===")
    
    # 测试用户信息获取性能
    print("1. 用户信息获取性能对比:")
    
    # 原模块
    start_time = time.time()
    for _ in range(100):
        corutine_user_login.get_user()
    original_time = time.time() - start_time
    print(f"  原模块: {original_time:.4f}秒 (100次调用)")
    
    # 新模块
    start_time = time.time()
    for _ in range(100):
        auth.get_user()
    new_time = time.time() - start_time
    print(f"  新模块: {new_time:.4f}秒 (100次调用)")
    
    if new_time < original_time:
        print(f"  新模块性能提升: {((original_time - new_time) / original_time * 100):.2f}%")
    else:
        print(f"  原模块性能更好: {((new_time - original_time) / original_time * 100):.2f}%")

async def main():
    """主测试函数"""
    print("开始测试重构后的登录模块...")
    
    # 基础兼容性测试
    test_compatibility()
    
    # 性能对比测试
    performance_comparison()
    
    # 错误处理测试
    await test_error_handling()
    
    # 功能测试（需要网络连接）
    print("\n注意: 以下测试需要网络连接，如果网络不可用将跳过")
    try:
        await test_new_auth_module()
        await test_original_module()
    except Exception as e:
        print(f"网络测试跳过，原因: {e}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
