"""
任务调度模块数据模型和枚举定义

定义了任务调度模块中使用的数据模型、枚举类型和常量。

Author: wwind
Date: 2025.07.08
"""

from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass
import time


class DataSourceType(Enum):
    """数据源类型枚举"""
    MYSQL = "mysql"
    EXCEL = "excel"
    REDIS = "redis"


class QueueType(Enum):
    """队列类型枚举"""
    TASK_QUEUE = "task_queue"
    RESULT_QUEUE = "result_queue"


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = 0      # 待处理
    PROCESSING = 1   # 处理中
    COMPLETED = 2    # 已完成
    FAILED = 3       # 失败


class OperationType(Enum):
    """操作类型枚举"""
    READ = "read"
    WRITE = "write"
    UPDATE = "update"
    DELETE = "delete"


@dataclass
class TaskItem:
    """
    任务项数据模型
    
    表示队列中的一个任务项
    """
    task_id: str                    # 任务ID（通常是申请号）
    data: Optional[Dict[str, Any]] = None  # 任务数据
    priority: int = 0               # 优先级（数字越大优先级越高）
    created_at: Optional[float] = None        # 创建时间戳
    retry_count: int = 0            # 重试次数
    max_retries: int = 3            # 最大重试次数
    status: TaskStatus = TaskStatus.PENDING  # 任务状态
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_at is None:
            self.created_at = time.time()
    
    def is_valid(self) -> bool:
        """检查任务项是否有效"""
        if not self.task_id or not isinstance(self.task_id, str):
            return False
        if self.task_id.strip() == "":
            return False
        # 检查申请号格式（不包含点号和空格）
        if "." in self.task_id or " " in self.task_id:
            return False
        return True
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries
    
    def increment_retry(self) -> None:
        """增加重试次数"""
        self.retry_count += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'data': self.data,
            'priority': self.priority,
            'created_at': self.created_at,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'status': self.status.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskItem':
        """从字典创建任务项"""
        status = TaskStatus(data.get('status', TaskStatus.PENDING.value))
        return cls(
            task_id=data['task_id'],
            data=data.get('data'),
            priority=data.get('priority', 0),
            created_at=data.get('created_at'),  # 现在类型匹配了
            retry_count=data.get('retry_count', 0),
            max_retries=data.get('max_retries', 3),
            status=status
        )


@dataclass
class DataSourceConfig:
    """
    数据源配置数据模型
    
    表示数据源的配置信息
    """
    source_type: DataSourceType     # 数据源类型
    connection_params: Dict[str, Any]  # 连接参数
    max_connections: int = 10       # 最大连接数
    timeout: int = 30               # 超时时间（秒）
    retry_times: int = 3            # 重试次数
    
    def validate(self) -> bool:
        """验证配置是否有效"""
        if not isinstance(self.connection_params, dict):
            return False
        if self.max_connections <= 0:
            return False
        if self.timeout <= 0:
            return False
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'source_type': self.source_type.value,
            'connection_params': self.connection_params,
            'max_connections': self.max_connections,
            'timeout': self.timeout,
            'retry_times': self.retry_times
        }


@dataclass
class QueueConfig:
    """
    队列配置数据模型
    
    表示队列的配置信息
    """
    queue_type: QueueType           # 队列类型
    max_size: int                   # 最大队列大小
    monitor_interval: int           # 监控间隔（秒）
    low_threshold: int              # 低水位阈值
    batch_size: int = 100           # 批处理大小
    
    def validate(self) -> bool:
        """验证配置是否有效"""
        if self.max_size <= 0:
            return False
        if self.monitor_interval <= 0:
            return False
        if self.low_threshold < 0 or self.low_threshold >= self.max_size:
            return False
        if self.batch_size <= 0:
            return False
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'queue_type': self.queue_type.value,
            'max_size': self.max_size,
            'monitor_interval': self.monitor_interval,
            'low_threshold': self.low_threshold,
            'batch_size': self.batch_size
        }


@dataclass
class ProcessingStats:
    """
    处理统计数据模型
    
    记录任务处理的统计信息
    """
    total_tasks: int = 0            # 总任务数
    completed_tasks: int = 0        # 已完成任务数
    failed_tasks: int = 0           # 失败任务数
    processing_tasks: int = 0       # 处理中任务数
    start_time: Optional[float] = None        # 开始时间
    last_update_time: Optional[float] = None  # 最后更新时间
    
    def __post_init__(self):
        """初始化后处理"""
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time
        if self.last_update_time is None:
            self.last_update_time = current_time
    
    def update_stats(self, completed: int = 0, failed: int = 0, processing: int = 0) -> None:
        """更新统计信息"""
        self.completed_tasks += completed
        self.failed_tasks += failed
        self.processing_tasks = processing
        self.last_update_time = time.time()
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        total_processed = self.completed_tasks + self.failed_tasks
        if total_processed == 0:
            return 0.0
        return self.completed_tasks / total_processed
    
    def get_processing_time(self) -> float:
        """获取处理时长（秒）"""
        if self.start_time is None or self.last_update_time is None:
            return 0.0
        return self.last_update_time - self.start_time
    
    def get_average_processing_time(self) -> float:
        """获取平均处理时间（秒/任务）"""
        total_processed = self.completed_tasks + self.failed_tasks
        if total_processed == 0:
            return 0.0
        return self.get_processing_time() / total_processed
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'processing_tasks': self.processing_tasks,
            'start_time': self.start_time,
            'last_update_time': self.last_update_time,
            'success_rate': self.get_success_rate(),
            'processing_time': self.get_processing_time(),
            'average_processing_time': self.get_average_processing_time()
        }


# 常量定义
class Constants:
    """常量定义类"""
    
    # 默认配置值
    DEFAULT_QUEUE_MAX_SIZE = 5000
    DEFAULT_MONITOR_INTERVAL = 100
    DEFAULT_LOW_THRESHOLD = 500
    DEFAULT_BATCH_SIZE = 100
    DEFAULT_MAX_CONNECTIONS = 10
    DEFAULT_TIMEOUT = 30
    DEFAULT_RETRY_TIMES = 3

    # 数据源特定的优化配置
    REDIS_MONITOR_INTERVAL = 120  # Redis监控间隔优化
    REDIS_BATCH_SIZE = 200        # Redis批处理大小优化
    MYSQL_BATCH_SIZE = 150        # MySQL批处理大小优化
    EXCEL_MONITOR_INTERVAL = 300  # Excel监控间隔优化
    EXCEL_BATCH_SIZE = 500        # Excel批处理大小优化
    
    # 任务验证规则
    TASK_ID_MIN_LENGTH = 1
    TASK_ID_MAX_LENGTH = 50
    INVALID_TASK_ID_CHARS = ['.', ' ', '\t', '\n', '\r']
    
    # 错误代码
    ERROR_CODES = {
        'INVALID_TASK_ID': 'E001',
        'QUEUE_FULL': 'E002',
        'QUEUE_EMPTY': 'E003',
        'CONNECTION_FAILED': 'E004',
        'FILE_NOT_FOUND': 'E005',
        'INVALID_CONFIG': 'E006',
        'RESOURCE_EXHAUSTED': 'E007'
    }
