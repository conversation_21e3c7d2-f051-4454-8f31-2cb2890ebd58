from abc import ABC, abstractmethod

class BasePipeline(ABC):
    """
    所有数据处理管道的抽象基类。
    """

    @abstractmethod
    async def open_spider(self, spider):
        """
        当爬虫被打开时调用。
        使用此方法初始化数据库连接等资源。
        """
        pass

    @abstractmethod
    async def close_spider(self, spider):
        """
        当爬虫被关闭时调用。
        使用此方法清理资源。
        """
        pass

    @abstractmethod
    async def process_item(self, item, spider):
        """
        处理爬虫抓取的项目。
        所有管道都必须实现此方法。
        """
        return item
